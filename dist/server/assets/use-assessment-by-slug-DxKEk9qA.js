import{jsx as e,jsxs as t}from"react/jsx-runtime";import*as s from"react";import{useState as r,use<PERSON>allback as a,useEffect as i}from"react";import{h as n,k as l,s as c,u as o,C as d,a as m,b as h,c as u,d as p,l as g}from"../entry-server.js";import*as b from"@radix-ui/react-progress";import{AlertCircle as x,RefreshCw as f,CheckCircle as w,AlertTriangle as y,Shield as _,TrendingUp as N,ExternalLink as v}from"lucide-react";const k=s.forwardRef((({className:t,value:s,...r},a)=>e(b.Root,{ref:a,className:n("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...r,children:e(b.Indicator,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})));k.displayName=b.Root.displayName;const C=e=>{const[t,s]=r(null),[i,n]=r(null),[o,d]=r(!1),{user:m}=l(),h=a((async t=>{d(!0);try{const r={assessment_type_id:e,company_name:t.companyName,industry:t.industry,employee_count:t.employeeCount,contact_name:t.contactName,email:t.email,phone:t.phone||null,status:"in_progress",ip_address:null,user_agent:navigator.userAgent,referrer:document.referrer||null,user_id:m?.id||null},{data:a,error:i}=await c.from("assessment_submissions").insert(r).select("id, session_id").single();if(i)throw i;return s(a.id),n(a.session_id),await c.from("assessment_analytics").insert({submission_id:a.id,step_name:"company_info",step_completed_at:(new Date).toISOString(),device_type:/Mobile|Android|iPhone|iPad/.test(navigator.userAgent)?"mobile":"desktop",browser_name:navigator.userAgent.split(" ")[0],screen_resolution:`${screen.width}x${screen.height}`}),a.id}catch(r){throw r}finally{d(!1)}}),[e,m]),u=a((async(e,s,r)=>{if(t)try{const a={submission_id:t,question_id:e,selected_option_id:r||null,risk_score:s},{error:i}=await c.from("assessment_answers").upsert(a,{onConflict:"submission_id,question_id"});if(i)throw i;await c.from("assessment_analytics").insert({submission_id:t,step_name:`question_${e}`,step_completed_at:(new Date).toISOString()})}catch(a){throw a}}),[t]),p=a(((e,t)=>{const s=[],r=e.reduce(((e,t)=>e+t),0)/e.length;return r>=4?(s.push("Implement immediate security awareness training"),s.push("Conduct urgent security assessment"),s.push("Deploy endpoint detection and response (EDR)")):r>=3?(s.push("Enhance email security with advanced filtering"),s.push("Implement multi-factor authentication"),s.push("Regular security training and testing")):(s.push("Maintain current security posture"),s.push("Consider advanced threat protection"),s.push("Regular security audits and updates")),t.toLowerCase().includes("healthcare")?(s.push("HIPAA compliance assessment"),s.push("Medical device security review")):t.toLowerCase().includes("finance")?(s.push("Financial regulatory compliance check"),s.push("PCI DSS compliance verification")):t.toLowerCase().includes("education")&&(s.push("FERPA compliance review"),s.push("Student data protection enhancement")),s.slice(0,4)}),[]),g=a((async e=>{if(t)try{const{data:s}=await c.from("assessment_submissions").select("industry").eq("id",t).single(),r=e.reduce(((e,t)=>e+t),0),a=5*e.length,i=Math.round(r/a*100);let n,l,o;i<=30?(n="LOW",l="low_priority",o=3):i<=60?(n="MEDIUM",l="within_week",o=6):(n="HIGH",l="immediate",o=9);const d=p(e,s?.industry||""),m={submission_id:t,total_risk_score:r,max_possible_score:a,risk_percentage:i,risk_level:n,lead_priority:o,follow_up_urgency:l,top_recommendations:d},{error:h}=await c.from("lead_scores").insert(m);if(h)throw h;const{error:u}=await c.from("assessment_submissions").update({status:"completed",completed_at:(new Date).toISOString()}).eq("id",t);if(u)throw u;await c.from("assessment_analytics").insert({submission_id:t,step_name:"completed",step_completed_at:(new Date).toISOString()})}catch(s){throw s}}),[t,p]);return{submissionId:t,sessionId:i,isSubmitting:o,createSubmission:h,saveAnswer:u,calculateAndSaveScores:g}},A=({questions:s,onComplete:a,assessmentType:n})=>{const[l,c]=r(0),[u,p]=r([]),[g,b]=r([]),{saveAnswer:N,calculateAndSaveScores:v}=C(n.id),{toast:A}=o();i((()=>{}),[s.length,l,u.length,n.title]);const S=()=>{window.location.reload()};if(0===s.length)return e(d,{className:"cyber-gradient-card border border-red-500/30",children:t(m,{className:"p-8 text-center",children:[e(x,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),e("h3",{className:"text-xl font-semibold text-white mb-2",children:"Assessment Questions Not Available"}),t("p",{className:"text-red-400 mb-4",children:['The questions for "',n.title,'" are not yet available.']}),t("div",{className:"space-y-2 text-white/70 text-sm mb-6",children:[e("p",{children:"This might be due to:"}),t("ul",{className:"list-disc list-inside space-y-1",children:[e("li",{children:"Assessment still being configured"}),e("li",{children:"Temporary system issues"}),e("li",{children:"Database connectivity problems"})]})]}),t(h,{onClick:S,className:"bg-green-bright hover:bg-green-muted text-black font-semibold",children:[e(f,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})});const I=(l+1)/s.length*100,q=s[l];return e(d,{className:"cyber-gradient-card border border-green-muted/30",children:t(m,{className:"p-8",children:[t("div",{className:"mb-8",children:[t("div",{className:"flex items-center justify-between mb-4",children:[t("span",{className:"text-green-bright font-medium",children:["Question ",l+1," of ",s.length]}),e("span",{className:"text-white/70",children:q.category})]}),e(k,{value:I,className:"mb-6"})]}),t("div",{className:"mb-8",children:[e("h2",{className:"text-xl md:text-2xl font-bold mb-6 text-white leading-relaxed",children:q.question_text}),e("div",{className:"space-y-4",children:q.options.map((r=>e(h,{variant:"outline",onClick:()=>(async(e,t)=>{const r=[...u,t],i=[...g,e];p(r),b(i);try{await N(s[l].id,t,e)}catch(n){A({title:"Save Error",description:"Your answer was recorded but not saved. Continuing assessment...",variant:"destructive"})}if(l<s.length-1)c((e=>e+1));else try{await v(r),a(r)}catch(n){a(r)}})(r.id,r.risk_score),className:"w-full text-left p-4 h-auto border-green-muted/30 hover:border-green-bright/50 hover:bg-green-dark/20 transition-all duration-200",children:t("div",{className:"flex items-start gap-3",children:[e("div",{className:"p-1 rounded-full flex-shrink-0 mt-1 "+(r.risk_score<=2?"bg-green-dark/40":r.risk_score<=3?"bg-yellow-500/20":"bg-red-500/20"),children:r.risk_score<=2?e(w,{className:"h-4 w-4 text-green-bright"}):r.risk_score<=3?e(y,{className:"h-4 w-4 text-yellow-400"}):e(_,{className:"h-4 w-4 text-red-400"})}),e("span",{className:"text-white",children:r.option_text})]})},r.id)))})]}),e("div",{className:"text-center text-white/60 text-sm",children:"Click on the option that best describes your current situation"})]})})},S=({submissionId:s,onStartNew:a})=>{const[n,l]=r(null),[b,x]=r(!0),[f,k]=r(!1),{toast:C}=o();i((()=>{A()}),[s]);const A=async()=>{try{const{data:e,error:t}=await c.from("lead_scores").select("*").eq("submission_id",s).maybeSingle();if(t)throw t;if(e){const t={risk_level:e.risk_level,risk_percentage:e.risk_percentage,lead_priority:e.lead_priority,follow_up_urgency:e.follow_up_urgency||"",top_recommendations:Array.isArray(e.top_recommendations)?e.top_recommendations:[]};l(t),await S()}else setTimeout((()=>{A()}),2e3)}catch(e){C({title:"Error",description:"Failed to load assessment results. Please try again.",variant:"destructive"})}finally{x(!1)}},S=async()=>{if(!f)try{const{error:e}=await c.functions.invoke("send-assessment-results",{body:{submissionId:s}});if(e)throw e;k(!0)}catch(e){}},I=e=>{switch(e){case"LOW":return"text-green-400";case"MEDIUM":return"text-yellow-400";case"HIGH":return"text-red-400";default:return"text-white"}};if(b)return e(d,{className:"cyber-gradient-card border border-green-muted/30",children:t(m,{className:"p-8 text-center",children:[e("div",{className:"w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"}),e("p",{className:"text-white mb-2",children:"Calculating your security risk assessment..."}),e("p",{className:"text-white/60 text-sm",children:"This may take a few moments"})]})});if(!n)return e(d,{className:"cyber-gradient-card border border-red-500/30",children:t(m,{className:"p-8 text-center",children:[e(y,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e("p",{className:"text-white mb-4",children:"Unable to calculate assessment results"}),e(h,{onClick:a,className:"bg-green-bright hover:bg-green-muted text-black",children:"Start New Assessment"})]})});const q=(e=>{switch(e){case"LOW":return w;case"MEDIUM":return y;default:return _}})(n.risk_level),M=(D=n.risk_percentage,{implementedControls:Math.max(5,Math.floor((100-D)/10)),missingControls:Math.floor(D/15)+2,criticalGaps:D>70?Math.floor(D/25):0});var D;const R=Math.min(85,Math.round(.8*n.risk_percentage));return t("div",{className:"space-y-6",children:[t(d,{className:"cyber-gradient-card border border-green-muted/30",children:[t(u,{className:"text-center",children:[e("div",{className:"flex items-center justify-center mb-4",children:e(q,{className:`h-16 w-16 ${I(n.risk_level)}`})}),e(p,{className:"text-2xl text-white",children:"Assessment Complete"}),e("div",{className:"flex items-center justify-center gap-2 mt-2",children:t(g,{variant:"outline",className:`${I(n.risk_level)} border-current text-lg px-4 py-1`,children:[n.risk_level," RISK"]})})]}),t(m,{className:"text-center",children:[t("div",{className:"text-4xl font-bold text-white mb-2",children:[n.risk_percentage,"%"]}),e("p",{className:"text-white/70 mb-6",children:"Overall Security Risk Score"}),f&&e("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6",children:e("p",{className:"text-green-400 text-sm",children:"✅ Detailed results have been sent to your email"})})]})]}),t(d,{className:"cyber-gradient-card border border-green-muted/30",children:[e(u,{children:t(p,{className:"text-white flex items-center gap-2",children:[e(N,{className:"h-5 w-5 text-green-bright"}),"Risk Reduction Potential"]})}),e(m,{children:t("div",{className:"text-center",children:[t("div",{className:"text-3xl font-bold text-green-bright mb-2",children:["Up to ",R,"%"]}),e("p",{className:"text-white/70 text-sm",children:"Potential risk reduction with recommended security improvements"})]})})]}),t("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e(d,{className:"cyber-gradient-card border border-green-muted/30",children:t(m,{className:"p-4 text-center",children:[e("div",{className:"text-2xl font-bold text-green-bright mb-1",children:M.implementedControls}),e("div",{className:"text-white/70 text-sm",children:"Controls in Place"})]})}),e(d,{className:"cyber-gradient-card border border-yellow-500/30",children:t(m,{className:"p-4 text-center",children:[e("div",{className:"text-2xl font-bold text-yellow-400 mb-1",children:M.missingControls}),e("div",{className:"text-white/70 text-sm",children:"Areas for Improvement"})]})}),e(d,{className:"cyber-gradient-card border border-red-500/30",children:t(m,{className:"p-4 text-center",children:[e("div",{className:"text-2xl font-bold text-red-400 mb-1",children:M.criticalGaps}),e("div",{className:"text-white/70 text-sm",children:"Critical Gaps"})]})})]}),n.top_recommendations&&n.top_recommendations.length>0&&t(d,{className:"cyber-gradient-card border border-green-muted/30",children:[e(u,{children:e(p,{className:"text-white",children:"Priority Recommendations"})}),e(m,{children:e("div",{className:"space-y-3",children:n.top_recommendations.map(((s,r)=>t("div",{className:"flex items-start gap-3 p-3 bg-green-dark/20 rounded-lg",children:[e("div",{className:"bg-green-bright text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mt-0.5",children:r+1}),e("p",{className:"text-white text-sm",children:s})]},r)))})})]}),t("div",{className:"flex flex-col sm:flex-row gap-4",children:[e(h,{onClick:a,variant:"outline",className:"flex-1 border-green-muted text-green-bright hover:bg-green-dark/20",children:"Take Another Assessment"}),t(h,{onClick:()=>window.open("/contact","_blank"),className:"flex-1 bg-green-bright hover:bg-green-muted text-black",children:[e(v,{className:"w-4 h-4 mr-2"}),"Get Professional Help"]})]})]})},I=e=>{const[t,s]=r(null),[a,n]=r([]),[l,o]=r(!0),[d,m]=r(null);return i((()=>{(async()=>{if(e)try{o(!0),m(null);const{data:t,error:r}=await c.from("assessment_types").select("*").eq("slug",e).eq("is_active",!0).single();if(r)throw new Error("Assessment not found");s(t);const{data:a,error:i}=await c.from("assessment_questions").select("*").eq("assessment_type_id",t.id).eq("is_active",!0).order("order_index");if(i)throw i;if(!a||0===a.length)return void m("This assessment is not yet available. Questions are being prepared.");const{data:l,error:d}=await c.from("assessment_question_options").select("*").in("question_id",a.map((e=>e.id))).order("question_id, order_index");if(d)throw d;const h=a.map((e=>{const t=l?.filter((t=>t.question_id===e.id))||[];return{...e,options:t}}));n(h)}catch(t){const e=t instanceof Error?t.message:"Failed to load assessment";m(e)}finally{o(!1)}})()}),[e]),{assessmentType:t,questions:a,loading:l,error:d}};export{A,C as a,S as b,I as u};
