import{jsx as r}from"react/jsx-runtime";import*as a from"react";import*as e from"@radix-ui/react-label";import{cva as o}from"class-variance-authority";import{h as t}from"../entry-server.js";const s=o("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),m=a.forwardRef((({className:a,...o},m)=>r(e.Root,{ref:m,className:t(s(),a),...o})));m.displayName=e.Root.displayName;export{m as L};
