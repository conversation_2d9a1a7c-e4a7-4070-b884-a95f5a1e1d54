import{jsx as e,jsxs as t}from"react/jsx-runtime";import{useState as r,useEffect as a,useRef as i}from"react";import{H as s,S as n,C as l,a as o,u as c,P as d,B as m}from"../entry-server.js";import{ArrowRight as u}from"lucide-react";import{Link as h}from"react-router-dom";import"react-helmet";import"react-dom/server";import"react-router-dom/server.mjs";import"framer-motion";import"@supabase/supabase-js";import"@radix-ui/react-slot";import"class-variance-authority";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";const p=()=>e(s,{title:"About BlackVeil",description:"Learn about our journey from discovering email security vulnerabilities to creating innovative cybersecurity solutions."});const b=()=>{const[s,c]=r(!1),d=i(null),m=function(e,t={}){const[i,s]=r(!1),{once:n=!0,threshold:l=0,rootMargin:o="0px"}=t;return a((()=>{const t=e.current;if(!t)return;const r=new IntersectionObserver((([e])=>{e.isIntersecting?(s(!0),n&&r&&t&&r.unobserve(t)):n||s(!1)}),{threshold:l,rootMargin:o});return r.observe(t),()=>{r&&t&&r.unobserve(t)}}),[e,n,l,o]),i}(d,{once:!0,threshold:.2});return a((()=>{c(!0)}),[]),e(n,{className:"py-20 sm:py-24 md:py-28 relative",id:"team",children:t("div",{ref:d,className:"max-w-5xl mx-auto",children:[t("div",{className:"text-center mb-12 sm:mb-16",children:[e("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light \n            px-4 py-1.5 rounded-full cyber-glow inline-block mb-4 opacity-0 "+(m?"animate-fade-in":""),children:"Our Team"}),e("h2",{className:"text-3xl md:text-4xl font-bold mb-4 cyber-glow-text opacity-0 "+(m?"animate-fade-in":""),style:{animationDelay:"0.2s"},children:"Meet The Experts"}),e("div",{className:"w-0 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6 \n            transition-all duration-1000 opacity-0 "+(m?"w-24 opacity-100":"")}),e("p",{className:"text-white/80 max-w-2xl mx-auto leading-relaxed mb-6 opacity-0 "+(m?"animate-fade-in":""),style:{animationDelay:"0.3s"},children:"Our team combines expertise in cybersecurity, software development, and compliance to deliver innovative email security solutions."})]}),e(l,{className:"cyber-gradient-card border-green-muted/40 overflow-hidden",children:t(o,{className:"p-8",children:[t("div",{className:"flex flex-col items-center mb-8",children:[e("div",{className:"inline-block p-4 bg-green-dark/40 rounded-full mb-6",children:e("span",{role:"img","aria-label":"team",className:"text-green-bright text-3xl",children:"👥"})}),e("h3",{className:"text-xl font-bold mb-4 text-center",children:"A Team of Security Experts"}),e("p",{className:"text-white/80 max-w-2xl mx-auto mb-4 text-center",children:"Our team combines expertise in cybersecurity, software development, and data analysis to deliver cutting-edge protection for businesses of all sizes."})]}),t("div",{className:"grid md:grid-cols-2 gap-6",children:[t("div",{className:"p-5 bg-black-soft rounded-lg border border-green-muted/30 hover:border-green-muted/60 transition-all duration-300",children:[e("h4",{className:"font-bold text-green-light mb-2",children:"Research Team"}),e("p",{className:"text-white/70 text-sm",children:"Dedicated professionals constantly monitoring emerging threats and improving detection algorithms"})]}),t("div",{className:"p-5 bg-black-soft rounded-lg border border-green-muted/30 hover:border-green-muted/60 transition-all duration-300",children:[e("h4",{className:"font-bold text-green-light mb-2",children:"Development Team"}),e("p",{className:"text-white/70 text-sm",children:"Expert engineers building robust and scalable security solutions"})]})]}),t("div",{className:"mt-8 text-center",children:[e("div",{className:"w-16 h-px bg-green-muted/50 mx-auto my-6"}),e("p",{className:"text-white/60 text-sm italic",children:"Our complete team roster will be updated soon"})]})]})})]})})},x=()=>e(n,{className:"py-16 xs:py-20",children:t("div",{className:"max-w-4xl mx-auto text-center",children:[e("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full",children:"Looking Forward"}),e("h2",{className:"text-2xl xs:text-3xl font-bold mt-5 mb-4",children:"Our Vision for the Future"}),e("p",{className:"text-white/70 max-w-2xl mx-auto mb-8",children:"We're building a future where businesses can communicate with confidence, free from the threat of email fraud and impersonation."}),t(h,{to:"/services",className:"cyber-button inline-flex items-center gap-2",children:[e("span",{children:"Explore Our Services"}),e(u,{className:"w-4 h-4"})]})]})}),g=()=>e(n,{className:"py-12 xs:py-16",children:t("div",{className:"max-w-4xl mx-auto",children:[t("div",{className:"text-center mb-12",children:[e("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full",children:"Our Journey"}),e("h2",{className:"text-2xl xs:text-3xl font-bold mt-5 mb-4",children:"The BlackVeil Story"}),e("p",{className:"text-white/70",children:"From research to revolution: How we're changing the landscape of email security."})]}),t("div",{className:"grid md:grid-cols-2 gap-8",children:[t("div",{className:"cyber-gradient-card p-8 rounded-lg",children:[e("h3",{className:"text-xl font-bold mb-4",children:"The Beginning"}),e("p",{className:"text-white/80 leading-relaxed",children:"Our journey began with a simple question: How vulnerable are New Zealand businesses to email fraud? This led us to develop specialized crawlers to analyze email security across thousands of domains."})]}),t("div",{className:"cyber-gradient-card p-8 rounded-lg",children:[e("h3",{className:"text-xl font-bold mb-4",children:"The Discovery"}),e("p",{className:"text-white/80 leading-relaxed",children:"The results were startling. Our research revealed widespread vulnerabilities in email systems, with many businesses completely unprotected against sophisticated impersonation attacks."})]})]})]})}),y=()=>{const{toast:r}=c();a((()=>{localStorage.getItem("visited-about")||setTimeout((()=>{r({title:"Welcome to our story",description:"Learn about our journey in email security innovation",variant:"default"}),localStorage.setItem("visited-about","true")}),1500);["/path/to/team-member-1.jpg","/path/to/team-member-2.jpg"].forEach((e=>{(new Image).src=e}))}),[r]);return t("div",{className:"about-page",children:[e(d,{title:"About Blackveil - Our Story & Team",description:"Learn about Blackveil's journey from discovering email security vulnerabilities to creating innovative cybersecurity solutions. Featured on Firetail.ai Modern Cyber Podcast.",canonicalUrl:"https://blackveil.co.nz/about",keywords:"Blackveil, cybersecurity, email security, New Zealand, Firetail.ai, Modern Cyber Podcast, company story"}),e(m,{items:[{name:"Home",url:"https://blackveil.co.nz/"},{name:"About",url:"https://blackveil.co.nz/about"}]}),e(p,{}),e(g,{}),e(b,{}),e(x,{})]})};export{y as default};
