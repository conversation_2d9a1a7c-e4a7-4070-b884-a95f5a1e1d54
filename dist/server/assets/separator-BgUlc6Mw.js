import{jsx as r}from"react/jsx-runtime";import*as o from"react";import*as a from"@radix-ui/react-separator";import{h as e}from"../entry-server.js";const t={xs:375,sm:640,md:768,lg:1024,xl:1280,"2xl":1536},s=o.forwardRef((({className:o,orientation:t="horizontal",decorative:s=!0,...i},m)=>r(a.Root,{ref:m,decorative:s,orientation:t,className:e("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",o),...i})));s.displayName=a.Root.displayName;export{s as S,t as b};
