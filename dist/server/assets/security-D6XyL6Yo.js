function t(t){return t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/`/g,"&#96;").replace(/\(/g,"&#40;").replace(/\)/g,"&#41;").replace(/{/g,"&#123;").replace(/}/g,"&#125;"):""}function e(t){if(!t)return!1;return/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(t.toLowerCase())}function r(t,e=""){if(!t)return e;try{const a=t.toString().trim().toLowerCase(),n=["javascript:","data:text/html","vbscript:","file:","data:text/plain"],s=/on\w+\s*=/i.test(a);if(n.some((t=>a.startsWith(t)))||s)return e;const c=["https://","http://","mailto:","tel:","data:image/"].some((t=>a.startsWith(t)));if(!a.includes(":")||c){if(a.includes("://"))try{return new URL(t),t}catch(r){return e}return t}return e}catch(a){return e}}export{r as a,e as i,t as s};
