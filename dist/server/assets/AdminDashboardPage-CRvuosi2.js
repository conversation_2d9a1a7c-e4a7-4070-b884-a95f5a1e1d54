import{jsxs as e,jsx as t,Fragment as s}from"react/jsx-runtime";import{h as a,u as i,s as r,C as l,c as n,d as c,a as d,b as o,l as m,m as h,n as u,P as p,S as x,e as g,f,g as y,T as N}from"../entry-server.js";import*as v from"react";import{useState as b,useEffect as w}from"react";import{I as _}from"./input-Bha7rE9x.js";import*as k from"@radix-ui/react-select";import{ChevronDown as R,ChevronUp as S,Check as C,Users as A,AlertTriangle as M,Search as D,Mail as T,Phone as j,Building as L,TrendingUp as I,Target as F,Calendar as B,CalendarDays as E,TestTube as P,BarChart3 as q,Pause as H,Play as z,Shield as O,Globe as U,TrendingDown as $,CheckCircle as G,XCircle as K,RefreshCw as V,Download as W,Clock as Q,Settings as Y}from"lucide-react";import{format as J}from"date-fns";import{ResponsiveContainer as X,LineChart as Z,CartesianGrid as ee,XAxis as te,YAxis as se,Tooltip as ae,Line as ie,PieChart as re,Pie as le,Cell as ne,BarChart as ce,Bar as de}from"recharts";import{L as oe}from"./label-BdpnMMwV.js";import{T as me}from"./textarea-CpkNdKXR.js";import"react-helmet";import"react-dom/server";import"react-router-dom/server.mjs";import"react-router-dom";import"framer-motion";import"@supabase/supabase-js";import"@radix-ui/react-slot";import"class-variance-authority";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";import"./security-D6XyL6Yo.js";import"@radix-ui/react-label";const he=k.Root,ue=k.Value,pe=v.forwardRef((({className:s,children:i,...r},l)=>e(k.Trigger,{ref:l,className:a("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...r,children:[i,t(k.Icon,{asChild:!0,children:t(R,{className:"h-4 w-4 opacity-50"})})]})));pe.displayName=k.Trigger.displayName;const xe=v.forwardRef((({className:e,...s},i)=>t(k.ScrollUpButton,{ref:i,className:a("flex cursor-default items-center justify-center py-1",e),...s,children:t(S,{className:"h-4 w-4"})})));xe.displayName=k.ScrollUpButton.displayName;const ge=v.forwardRef((({className:e,...s},i)=>t(k.ScrollDownButton,{ref:i,className:a("flex cursor-default items-center justify-center py-1",e),...s,children:t(R,{className:"h-4 w-4"})})));ge.displayName=k.ScrollDownButton.displayName;const fe=v.forwardRef((({className:s,children:i,position:r="popper",...l},n)=>t(k.Portal,{children:e(k.Content,{ref:n,className:a("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:r,...l,children:[t(xe,{}),t(k.Viewport,{className:a("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:i}),t(ge,{})]})})));fe.displayName=k.Content.displayName;v.forwardRef((({className:e,...s},i)=>t(k.Label,{ref:i,className:a("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s}))).displayName=k.Label.displayName;const ye=v.forwardRef((({className:s,children:i,...r},l)=>e(k.Item,{ref:l,className:a("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...r,children:[t("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t(k.ItemIndicator,{children:t(C,{className:"h-4 w-4"})})}),t(k.ItemText,{children:i})]})));ye.displayName=k.Item.displayName;v.forwardRef((({className:e,...s},i)=>t(k.Separator,{ref:i,className:a("-mx-1 my-1 h-px bg-muted",e),...s}))).displayName=k.Separator.displayName;const Ne=()=>{const[s,a]=b([]),[h,u]=b(!0),[p,x]=b(null),[g,f]=b(""),[y,N]=b("all"),[v,k]=b("all"),{toast:R}=i();w((()=>{S()}),[]);const S=async()=>{try{u(!0),x(null);const{data:e,error:t}=await r.from("assessment_submissions").select("\n          id,\n          company_name,\n          contact_name,\n          email,\n          phone,\n          industry,\n          employee_count,\n          status,\n          created_at,\n          completed_at,\n          assessment_type_id,\n          lead_scores (\n            risk_level,\n            risk_percentage,\n            lead_priority,\n            follow_up_urgency,\n            total_risk_score,\n            max_possible_score\n          ),\n          assessment_types (\n            title,\n            name\n          )\n        ").order("created_at",{ascending:!1});if(t)return x(`Failed to load leads: ${t.message}`),void R({title:"Error",description:"Failed to load leads. Please try again.",variant:"destructive"});a(e||[])}catch(e){x("An unexpected error occurred while loading leads."),R({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{u(!1)}},C=s.filter((e=>{const t=e.company_name.toLowerCase().includes(g.toLowerCase())||e.contact_name.toLowerCase().includes(g.toLowerCase())||e.email.toLowerCase().includes(g.toLowerCase())||e.industry.toLowerCase().includes(g.toLowerCase()),s="all"===y||e.status===y,a="all"===v||e.lead_scores?.risk_level?.toLowerCase()===v.toLowerCase();return t&&s&&a})),F=e=>{switch(e?.toLowerCase()){case"high":return"destructive";case"medium":return"default";case"low":return"secondary";default:return"outline"}},B=e=>{switch(e.toLowerCase()){case"completed":return"default";case"in_progress":return"secondary";default:return"outline"}};return e(l,h?{children:[t(n,{children:e(c,{className:"flex items-center gap-2",children:[t(A,{className:"h-5 w-5"}),"Loading Leads..."]})}),t(d,{children:t("div",{className:"flex items-center justify-center py-8",children:t("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})})]}:p?{children:[t(n,{children:e(c,{className:"flex items-center gap-2 text-red-600",children:[t(M,{className:"h-5 w-5"}),"Error Loading Leads"]})}),t(d,{children:e("div",{className:"text-center py-8",children:[t("p",{className:"text-red-600 mb-4",children:p}),t(o,{onClick:S,variant:"outline",children:"Try Again"})]})})]}:{children:[e(n,{children:[e(c,{className:"flex items-center gap-2",children:[t(A,{className:"h-5 w-5"}),"Lead Management (",C.length,")"]}),e("div",{className:"flex flex-col sm:flex-row gap-4 mt-4",children:[e("div",{className:"relative flex-1",children:[t(D,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),t(_,{placeholder:"Search leads...",value:g,onChange:e=>f(e.target.value),className:"pl-10"})]}),e(he,{value:y,onValueChange:N,children:[t(pe,{className:"w-full sm:w-40",children:t(ue,{placeholder:"Status"})}),e(fe,{children:[t(ye,{value:"all",children:"All Status"}),t(ye,{value:"completed",children:"Completed"}),t(ye,{value:"in_progress",children:"In Progress"}),t(ye,{value:"started",children:"Started"})]})]}),e(he,{value:v,onValueChange:k,children:[t(pe,{className:"w-full sm:w-40",children:t(ue,{placeholder:"Risk Level"})}),e(fe,{children:[t(ye,{value:"all",children:"All Risk"}),t(ye,{value:"high",children:"High Risk"}),t(ye,{value:"medium",children:"Medium Risk"}),t(ye,{value:"low",children:"Low Risk"})]})]})]})]}),t(d,{children:0===C.length?e("div",{className:"text-center py-12",children:[t(A,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),t("h3",{className:"text-lg font-semibold mb-2",children:"No leads found"}),t("p",{className:"text-gray-600",children:g||"all"!==y||"all"!==v?"Try adjusting your filters":"Leads will appear here once assessments are completed"})]}):t("div",{className:"space-y-4",children:C.map((s=>{return t("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:e("div",{className:"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4",children:[e("div",{className:"flex-1 space-y-2",children:[e("div",{className:"flex items-center gap-3 flex-wrap",children:[t("h3",{className:"font-semibold text-lg",children:s.company_name}),t(m,{variant:B(s.status),children:s.status.replace("_"," ")}),s.lead_scores?.risk_level&&e(m,{variant:F(s.lead_scores.risk_level),children:[s.lead_scores.risk_level," Risk"]})]}),e("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600",children:[e("div",{className:"flex items-center gap-2",children:[t(T,{className:"h-4 w-4"}),s.contact_name," - ",s.email]}),s.phone&&e("div",{className:"flex items-center gap-2",children:[t(j,{className:"h-4 w-4"}),s.phone]}),e("div",{className:"flex items-center gap-2",children:[t(L,{className:"h-4 w-4"}),s.industry]}),e("div",{className:"flex items-center gap-2",children:[t(A,{className:"h-4 w-4"}),s.employee_count," employees"]})]}),s.assessment_types&&e("div",{className:"text-sm text-gray-600",children:["Assessment: ",s.assessment_types.title]})]}),e("div",{className:"flex flex-col items-end gap-2",children:[t("div",{className:"text-sm text-gray-500",children:J(new Date(s.created_at),"MMM dd, yyyy")}),s.lead_scores&&e("div",{className:"text-right space-y-1",children:[e("div",{className:"flex items-center gap-2",children:[t(I,{className:"h-4 w-4"}),e("span",{className:"text-sm font-medium",children:[s.lead_scores.risk_percentage,"% Risk"]})]}),e("div",{className:"text-xs text-gray-600",children:["Priority: ",(a=s.lead_scores.lead_priority,a?a<=3?"High":a<=6?"Medium":"Low":"Pending")]}),s.lead_scores.total_risk_score&&s.lead_scores.max_possible_score&&e("div",{className:"text-xs text-gray-600",children:["Score: ",s.lead_scores.total_risk_score,"/",s.lead_scores.max_possible_score]})]})]})]})},s.id);var a}))})})]})},ve=()=>{const[s,a]=b(null),[i,o]=b(!0);w((()=>{m()}),[]);const m=async()=>{try{const{count:e,error:t}=await r.from("assessment_submissions").select("*",{count:"exact",head:!0});if(t)throw t;const{count:s,error:i}=await r.from("assessment_submissions").select("*",{count:"exact",head:!0}).eq("status","completed");if(i)throw i;const{count:l,error:n}=await r.from("lead_scores").select("*",{count:"exact",head:!0}).eq("risk_level","HIGH"),{count:c,error:d}=await r.from("assessment_submissions").select("*",{count:"exact",head:!0}).gte("created_at",new Date(Date.now()-6048e5).toISOString());if(d)throw d;const{data:o}=await r.from("assessment_submissions").select("industry").eq("status","completed"),{data:m}=await r.from("lead_scores").select("risk_level, risk_percentage, lead_priority"),{count:h}=await r.from("assessment_submissions").select("*",{count:"exact",head:!0}).gte("created_at",new Date(Date.now()-12096e5).toISOString()).lt("created_at",new Date(Date.now()-6048e5).toISOString()),u=o?.reduce(((e,t)=>(e[t.industry]=(e[t.industry]||0)+1,e)),{})||{},p=m?.reduce(((e,t)=>(e[t.risk_level]=(e[t.risk_level]||0)+1,e)),{})||{},x=m?.reduce(((e,t)=>(e[t.lead_priority]=(e[t.lead_priority]||0)+1,e)),{})||{},g=m?.length>0?m.reduce(((e,t)=>e+(t.risk_percentage||0)),0)/m.length:0,f=h>0?Math.round(((c||0)-h)/h*100):0;a({totalSubmissions:e||0,completedAssessments:s||0,highRiskLeads:l||0,conversionRate:e?Math.round((s||0)/e*100):0,industryBreakdown:Object.entries(u).map((([e,t])=>({industry:e,count:t}))),riskDistribution:Object.entries(p).map((([e,t])=>({risk_level:e,count:t}))),averageRiskScore:Math.round(100*g)/100,leadsByPriority:Object.entries(x).map((([e,t])=>({priority:parseInt(e),count:t}))),recentSubmissions:c||0,completionTrend:f})}catch(e){}finally{o(!1)}};return i?t(l,{className:"cyber-gradient-card border border-green-muted/30",children:t(d,{className:"p-8 text-center",children:t("div",{className:"text-white",children:"Loading analytics..."})})}):s?e("div",{className:"space-y-6",children:[e(l,{className:"cyber-gradient-card border border-green-muted/30",children:[t(n,{children:t(c,{className:"text-white",children:"Key Metrics"})}),e(d,{className:"space-y-4",children:[e("div",{className:"flex items-center gap-3 p-3 bg-green-dark/20 rounded-lg",children:[t(A,{className:"h-8 w-8 text-green-bright"}),e("div",{children:[t("div",{className:"text-2xl font-bold text-white",children:s.totalSubmissions}),t("div",{className:"text-white/60 text-sm",children:"Total Submissions"})]})]}),e("div",{className:"flex items-center gap-3 p-3 bg-blue-500/20 rounded-lg",children:[t(F,{className:"h-8 w-8 text-blue-400"}),e("div",{children:[t("div",{className:"text-2xl font-bold text-white",children:s.completedAssessments}),t("div",{className:"text-white/60 text-sm",children:"Completed Assessments"})]})]}),e("div",{className:"flex items-center gap-3 p-3 bg-red-500/20 rounded-lg",children:[t(M,{className:"h-8 w-8 text-red-400"}),e("div",{children:[t("div",{className:"text-2xl font-bold text-white",children:s.highRiskLeads}),t("div",{className:"text-white/60 text-sm",children:"High Risk Leads"})]})]}),e("div",{className:"flex items-center gap-3 p-3 bg-yellow-500/20 rounded-lg",children:[t(I,{className:"h-8 w-8 text-yellow-400"}),e("div",{children:[e("div",{className:"text-2xl font-bold text-white",children:[s.conversionRate,"%"]}),t("div",{className:"text-white/60 text-sm",children:"Completion Rate"})]})]}),e("div",{className:"flex items-center gap-3 p-3 bg-purple-500/20 rounded-lg",children:[t(F,{className:"h-8 w-8 text-purple-400"}),e("div",{children:[e("div",{className:"text-2xl font-bold text-white",children:[s.averageRiskScore,"%"]}),t("div",{className:"text-white/60 text-sm",children:"Average Risk Score"})]})]}),e("div",{className:"flex items-center gap-3 p-3 bg-cyan-500/20 rounded-lg",children:[t(A,{className:"h-8 w-8 text-cyan-400"}),e("div",{children:[t("div",{className:"text-2xl font-bold text-white",children:s.recentSubmissions}),t("div",{className:"text-white/60 text-sm",children:"Recent Submissions (7d)"})]})]}),e("div",{className:"flex items-center gap-3 p-3 bg-orange-500/20 rounded-lg",children:[t(I,{className:"h-8 w-8 "+(s.completionTrend>=0?"text-green-400":"text-red-400")}),e("div",{children:[e("div",{className:"text-2xl font-bold "+(s.completionTrend>=0?"text-green-400":"text-red-400"),children:[s.completionTrend>=0?"+":"",s.completionTrend,"%"]}),t("div",{className:"text-white/60 text-sm",children:"Weekly Trend"})]})]})]})]}),e(l,{className:"cyber-gradient-card border border-green-muted/30",children:[t(n,{children:t(c,{className:"text-white",children:"Industry Breakdown"})}),t(d,{children:t("div",{className:"space-y-3",children:s.industryBreakdown.map((({industry:s,count:a})=>e("div",{className:"flex justify-between items-center",children:[t("span",{className:"text-white capitalize",children:s}),t("span",{className:"text-green-bright font-semibold",children:a})]},s)))})})]}),e(l,{className:"cyber-gradient-card border border-green-muted/30",children:[t(n,{children:t(c,{className:"text-white",children:"Risk Distribution"})}),t(d,{children:t("div",{className:"space-y-3",children:s.riskDistribution.map((({risk_level:s,count:a})=>e("div",{className:"flex justify-between items-center",children:[e("span",{className:"font-medium "+("HIGH"===s?"text-red-400":"MEDIUM"===s?"text-yellow-400":"text-green-400"),children:[s," Risk"]}),t("span",{className:"text-white font-semibold",children:a})]},s)))})})]}),e(l,{className:"cyber-gradient-card border border-green-muted/30",children:[t(n,{children:t(c,{className:"text-white",children:"Lead Priority"})}),t(d,{children:t("div",{className:"space-y-3",children:s.leadsByPriority.sort(((e,t)=>t.priority-e.priority)).map((({priority:s,count:a})=>e("div",{className:"flex justify-between items-center",children:[e("span",{className:"font-medium "+(s>=4?"text-red-400":s>=3?"text-orange-400":s>=2?"text-yellow-400":"text-green-400"),children:["Priority ",s," ",s>=4?"(Urgent)":s>=3?"(High)":s>=2?"(Medium)":"(Low)"]}),t("span",{className:"text-white font-semibold",children:a})]},s)))})})]})]}):t(l,{className:"cyber-gradient-card border border-green-muted/30",children:t(d,{className:"p-8 text-center",children:t("div",{className:"text-red-400",children:"Failed to load analytics"})})})},be=()=>{const[s,a]=b("7d"),i={totalSubmissions:1250,completionRate:78.5,avgRiskScore:65.2,leadConversionRate:12.8,submissionsByRisk:[{name:"High Risk",value:320,color:"#ef4444"},{name:"Medium Risk",value:580,color:"#f59e0b"},{name:"Low Risk",value:350,color:"#10b981"}],submissionsTrend:[{date:"2024-01-01",submissions:45,completions:35},{date:"2024-01-02",submissions:52,completions:41},{date:"2024-01-03",submissions:38,completions:30},{date:"2024-01-04",submissions:61,completions:48},{date:"2024-01-05",submissions:44,completions:35},{date:"2024-01-06",submissions:55,completions:43},{date:"2024-01-07",submissions:48,completions:37}],industryBreakdown:[{industry:"Technology",count:285,avgRisk:58.3},{industry:"Healthcare",count:195,avgRisk:72.1},{industry:"Finance",count:160,avgRisk:45.8},{industry:"Retail",count:140,avgRisk:68.9},{industry:"Manufacturing",count:125,avgRisk:61.4},{industry:"Other",count:345,avgRisk:64.7}],emailMetrics:{totalSent:1180,openRate:24.5,responseRate:8.2,pendingEmails:15}};return e("div",{className:"space-y-6 p-6",children:[e("div",{className:"flex justify-between items-center",children:[t("h2",{className:"text-2xl font-bold",children:"Analytics Dashboard"}),e("div",{className:"flex gap-4",children:[e(he,{value:s,onValueChange:a,children:[t(pe,{className:"w-32",children:t(ue,{})}),e(fe,{children:[t(ye,{value:"7d",children:"Last 7 days"}),t(ye,{value:"30d",children:"Last 30 days"}),t(ye,{value:"90d",children:"Last 90 days"}),t(ye,{value:"1y",children:"Last year"})]})]}),e(o,{variant:"outline",size:"sm",children:[t(B,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})]}),e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Total Submissions"}),t(A,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[t("div",{className:"text-2xl font-bold",children:i.totalSubmissions.toLocaleString()}),t("p",{className:"text-xs text-muted-foreground",children:"+12% from last period"})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Completion Rate"}),t(I,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[i.completionRate,"%"]}),t("p",{className:"text-xs text-muted-foreground",children:"+2.5% from last period"})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Avg Risk Score"}),t(E,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[t("div",{className:"text-2xl font-bold",children:i.avgRiskScore}),t("p",{className:"text-xs text-muted-foreground",children:"-3.2% from last period"})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Lead Conversion"}),t(T,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[i.leadConversionRate,"%"]}),t("p",{className:"text-xs text-muted-foreground",children:"+1.8% from last period"})]})]})]}),e("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e(l,{children:[t(n,{children:t(c,{children:"Submissions Trend"})}),t(d,{children:t(X,{width:"100%",height:300,children:e(Z,{data:i.submissionsTrend,children:[t(ee,{strokeDasharray:"3 3"}),t(te,{dataKey:"date"}),t(se,{}),t(ae,{}),t(ie,{type:"monotone",dataKey:"submissions",stroke:"#3b82f6",strokeWidth:2}),t(ie,{type:"monotone",dataKey:"completions",stroke:"#10b981",strokeWidth:2})]})})})]}),e(l,{children:[t(n,{children:t(c,{children:"Risk Level Distribution"})}),t(d,{children:t(X,{width:"100%",height:300,children:e(re,{children:[t(le,{data:i.submissionsByRisk,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percent:t})=>`${e}: ${(100*t).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:i.submissionsByRisk.map(((e,s)=>t(ne,{fill:e.color},`cell-${s}`)))}),t(ae,{})]})})})]})]}),e(l,{children:[t(n,{children:t(c,{children:"Industry Breakdown"})}),t(d,{children:t(X,{width:"100%",height:400,children:e(ce,{data:i.industryBreakdown,children:[t(ee,{strokeDasharray:"3 3"}),t(te,{dataKey:"industry"}),t(se,{yAxisId:"left"}),t(se,{yAxisId:"right",orientation:"right"}),t(ae,{}),t(de,{yAxisId:"left",dataKey:"count",fill:"#3b82f6"}),t(de,{yAxisId:"right",dataKey:"avgRisk",fill:"#f59e0b"})]})})})]}),e(l,{children:[t(n,{children:t(c,{children:"Email Campaign Metrics"})}),t(d,{children:e("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e("div",{className:"text-center",children:[t("div",{className:"text-2xl font-bold text-blue-600",children:i.emailMetrics.totalSent}),t("div",{className:"text-sm text-muted-foreground",children:"Total Sent"})]}),e("div",{className:"text-center",children:[e("div",{className:"text-2xl font-bold text-green-600",children:[i.emailMetrics.openRate,"%"]}),t("div",{className:"text-sm text-muted-foreground",children:"Open Rate"})]}),e("div",{className:"text-center",children:[e("div",{className:"text-2xl font-bold text-orange-600",children:[i.emailMetrics.responseRate,"%"]}),t("div",{className:"text-sm text-muted-foreground",children:"Response Rate"})]}),e("div",{className:"text-center",children:[t("div",{className:"text-2xl font-bold text-red-600",children:i.emailMetrics.pendingEmails}),t("div",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})]})]})},we=()=>{const{tests:a,loading:i,error:r,getTestResults:u,createTest:p,refreshData:x}=(()=>{const[e,t]=b([]),[s,a]=b([]),[i,r]=b([]),[l,n]=b(!0),[c,d]=b(null);w((()=>{o()}),[]);const o=async()=>{try{n(!0),d(null),t([]),a([]),r([])}catch(e){d("Failed to load A/B testing data"),t([]),a([]),r([])}finally{n(!1)}};return{tests:e,variants:s,participations:i,loading:l,error:c,getTestResults:e=>{const t=i.filter((t=>t.test_id===e));return s.filter((t=>t.test_id===e)).map((e=>{const s=t.filter((t=>t.variant_id===e.id)),a=s.filter((e=>e.converted)).length,i=s.length>0?a/s.length*100:0;return{variant:e,participations:s.length,conversions:a,conversionRate:Math.round(100*i)/100}}))},createTest:async e=>{try{return null}catch(t){throw t}},participateInTest:async(e,t,s)=>{try{return null}catch(a){throw a}},recordConversion:async(e,t)=>{try{return null}catch(s){throw s}},refreshData:o}})(),[g,f]=b(!1),[y,N]=b({name:"",description:"",test_type:"cta_button",hypothesis:"",success_metric:"conversion_rate"}),v=()=>a.filter((e=>e.is_active));return i?t("div",{className:"flex items-center justify-center p-8",children:t("div",{className:"text-muted-foreground",children:"Loading A/B tests..."})}):r?t("div",{className:"flex items-center justify-center p-8",children:e("div",{className:"text-red-600",children:["Error: ",r]})}):e("div",{className:"space-y-6",children:[e("div",{className:"flex justify-between items-center",children:[t("h2",{className:"text-2xl font-bold",children:"A/B Testing Dashboard"}),e(o,{onClick:()=>f(!0),children:[t(P,{className:"h-4 w-4 mr-2"}),"Create New Test"]})]}),g&&e(l,{children:[e(n,{children:[t(c,{children:"Create New A/B Test"}),t(h,{children:"Set up a new A/B test to measure conversion improvements"})]}),e(d,{className:"space-y-4",children:[e("div",{children:[t(oe,{htmlFor:"test-name",children:"Test Name"}),t(_,{id:"test-name",value:y.name,onChange:e=>N((t=>({...t,name:e.target.value}))),placeholder:"e.g., Homepage CTA Button Test"})]}),e("div",{children:[t(oe,{htmlFor:"test-description",children:"Description"}),t(me,{id:"test-description",value:y.description,onChange:e=>N((t=>({...t,description:e.target.value}))),placeholder:"Brief description of what you're testing"})]}),e("div",{children:[t(oe,{htmlFor:"test-type",children:"Test Type"}),e(he,{value:y.test_type,onValueChange:e=>N((t=>({...t,test_type:e}))),children:[t(pe,{children:t(ue,{placeholder:"Select test type"})}),e(fe,{children:[t(ye,{value:"cta_button",children:"CTA Button"}),t(ye,{value:"headline",children:"Headline"}),t(ye,{value:"layout",children:"Page Layout"}),t(ye,{value:"form",children:"Form Design"})]})]})]}),e("div",{children:[t(oe,{htmlFor:"hypothesis",children:"Hypothesis"}),t(me,{id:"hypothesis",value:y.hypothesis,onChange:e=>N((t=>({...t,hypothesis:e.target.value}))),placeholder:"What do you expect to happen and why?"})]}),e("div",{className:"flex justify-end space-x-2",children:[t(o,{variant:"outline",onClick:()=>f(!1),children:"Cancel"}),t(o,{onClick:async()=>{try{await p({...y,is_active:!0,start_date:(new Date).toISOString()}),f(!1),N({name:"",description:"",test_type:"cta_button",hypothesis:"",success_metric:"conversion_rate"}),x()}catch(e){}},children:"Create Test"})]})]})]}),t("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:0===a.length?t(l,{className:"col-span-full",children:e(d,{className:"p-8 text-center",children:[t(P,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),t("h3",{className:"text-lg font-semibold mb-2",children:"No A/B Tests Yet"}),t("p",{className:"text-muted-foreground mb-4",children:"Create your first A/B test to start measuring conversion improvements."}),t(o,{onClick:()=>f(!0),children:"Create Your First Test"})]})}):a.map((a=>{const i=u(a.id);return e(l,{children:[t(n,{children:e("div",{className:"flex justify-between items-start",children:[e("div",{children:[t(c,{className:"text-lg",children:a.name}),t(h,{children:a.description})]}),t(m,{variant:a.is_active?"default":"secondary",children:a.is_active?"Active":"Inactive"})]})}),t(d,{children:e("div",{className:"space-y-3",children:[e("div",{children:[t("div",{className:"text-sm font-medium",children:"Test Type"}),t("div",{className:"text-sm text-muted-foreground capitalize",children:a.test_type.replace("_"," ")})]}),e("div",{children:[t("div",{className:"text-sm font-medium",children:"Success Metric"}),t("div",{className:"text-sm text-muted-foreground",children:a.success_metric})]}),i.length>0&&e("div",{children:[t("div",{className:"text-sm font-medium mb-2",children:"Results"}),i.map(((s,a)=>e("div",{className:"text-xs space-y-1",children:[e("div",{className:"flex justify-between",children:[t("span",{children:s.variant.name}),e("span",{children:[s.conversionRate,"%"]})]}),e("div",{className:"text-muted-foreground",children:[s.participations," participants, ",s.conversions," conversions"]})]},a)))]}),e("div",{className:"flex justify-between items-center pt-2",children:[e(o,{variant:"outline",size:"sm",children:[t(q,{className:"h-3 w-3 mr-1"}),"View Details"]}),t(o,{variant:"outline",size:"sm",children:a.is_active?e(s,{children:[t(H,{className:"h-3 w-3 mr-1"}),"Pause"]}):e(s,{children:[t(z,{className:"h-3 w-3 mr-1"}),"Resume"]})})]})]})})]},a.id)}))}),v().length>0&&e(l,{children:[e(n,{children:[t(c,{children:"Active Tests Summary"}),e(h,{children:["Currently running ",v().length," active test",1!==v().length?"s":""]})]}),t(d,{children:e("div",{className:"text-center py-4",children:[t("div",{className:"text-2xl font-bold text-blue-600",children:v().length}),t("div",{className:"text-sm text-muted-foreground",children:"Active A/B Tests"})]})})]})]})},_e=()=>{const{radarData:s,loading:a,error:i}=u();if(a)return t("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[...Array(4)].map(((s,a)=>e(l,{className:"animate-pulse",children:[t(n,{className:"pb-2",children:t("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),e(d,{children:[t("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),t("div",{className:"h-3 bg-gray-200 rounded w-full"})]})]},a)))});if(i)return t(l,{className:"border-red-200 bg-red-50",children:t(d,{className:"pt-6",children:e("div",{className:"flex items-center gap-2 text-red-600",children:[t(M,{className:"h-5 w-5"}),t("span",{children:"Failed to load threat intelligence data"})]})})});if(!s)return null;const r=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),o=e=>e>0?t(I,{className:"h-4 w-4 text-red-500"}):e<0?t($,{className:"h-4 w-4 text-green-500"}):null,p=e=>e>0?"text-red-600":e<0?"text-green-600":"text-gray-600";return e("div",{className:"space-y-6",children:[e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Global Phishing"}),t(T,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[t("div",{className:"text-2xl font-bold",children:r(s.phishing.total)}),e("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o(s.phishing.trend),e("span",{className:p(s.phishing.trend),children:[Math.abs(s.phishing.trend),"% this week"]})]})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Spoofing Attempts"}),t(O,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[t("div",{className:"text-2xl font-bold",children:r(s.spoofing.total)}),e("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o(s.spoofing.trend),e("span",{className:p(s.spoofing.trend),children:[Math.abs(s.spoofing.trend),"% this week"]})]})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"DMARC Adoption"}),t(U,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[s.dmarc.adoptionRate,"%"]}),e("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o(s.dmarc.trend),e("span",{className:p(s.dmarc.trend),children:[Math.abs(s.dmarc.trend),"% this week"]})]})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"DMARC Compliance"}),t(O,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[s.dmarc.compliance,"%"]}),t("div",{className:"text-xs text-muted-foreground",children:"Strict policy enforcement"})]})]})]}),e("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e(l,{children:[e(n,{children:[t(c,{className:"text-lg",children:"Top Phishing Targets"}),t(h,{children:"Most targeted industries this week"})]}),t(d,{children:t("div",{className:"space-y-3",children:s.phishing.topTargets.map(((s,a)=>e("div",{className:"flex items-center justify-between",children:[t("span",{className:"text-sm font-medium",children:s}),e(m,{variant:0===a?"destructive":1===a?"secondary":"outline",children:["#",a+1]})]},s)))})})]}),e(l,{children:[e(n,{children:[t(c,{className:"text-lg",children:"Industry Risk Levels"}),t(h,{children:"Risk scoring by industry sector"})]}),t(d,{children:t("div",{className:"space-y-3",children:Object.entries(s.industryRisks).sort((([,e],[,t])=>t-e)).slice(0,6).map((([s,a])=>e("div",{className:"flex items-center justify-between",children:[t("span",{className:"text-sm font-medium",children:s}),e("div",{className:"flex items-center gap-2",children:[t("div",{className:"w-20 h-2 bg-gray-200 rounded-full overflow-hidden",children:t("div",{className:"h-full rounded-full "+(a>=8?"bg-red-500":a>=6?"bg-orange-500":"bg-yellow-500"),style:{width:a/10*100+"%"}})}),e("span",{className:"text-sm text-muted-foreground w-8",children:[a,"/10"]})]})]},s)))})})]})]}),e(l,{children:[e(n,{children:[t(c,{className:"text-lg",children:"Common Spoofing Methods"}),t(h,{children:"Most prevalent spoofing techniques detected globally"})]}),t(d,{children:t("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:s.spoofing.topMethods.map(((s,a)=>e("div",{className:"text-center p-3 border rounded-lg",children:[t("div",{className:"text-sm font-medium",children:s}),e(m,{variant:"outline",className:"mt-1",children:["Top ",a+1]})]},s)))})})]}),e("div",{className:"text-xs text-muted-foreground text-center",children:["Last updated: ",new Date(s.lastUpdated).toLocaleString()]})]})},ke=()=>{const{radarData:s,loading:a,error:i}=u();if(a)return t("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[...Array(4)].map(((s,a)=>e(l,{className:"animate-pulse",children:[t(n,{className:"pb-2",children:t("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),e(d,{children:[t("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),t("div",{className:"h-3 bg-gray-200 rounded w-full"})]})]},a)))});if(i||!s?.emailSecurity)return t(l,{className:"border-orange-200 bg-orange-50",children:t(d,{className:"pt-6",children:e("div",{className:"flex items-center gap-2 text-orange-600",children:[t(M,{className:"h-5 w-5"}),t("span",{children:"Email security data temporarily unavailable"})]})})});const{emailSecurity:r}=s,o=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),p=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",x=e=>e<=5?"text-green-600":e<=15?"text-yellow-600":"text-red-600";return e("div",{className:"space-y-6",children:[e("div",{className:"flex items-center gap-2",children:[t(T,{className:"h-5 w-5 text-blue-600"}),t("h3",{className:"text-lg font-semibold",children:"Email Security Analytics"}),t(m,{variant:"outline",className:"text-xs",children:"Enhanced"})]}),e("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Malicious Detection"}),t(O,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[r.malicious.detectionRate.toFixed(1),"%"]}),e("div",{className:"text-xs text-muted-foreground",children:[o(r.malicious.detected)," detected of"," ",o(r.malicious.detected+r.malicious.clean)," total"]}),t("div",{className:"mt-2",children:t(m,{variant:r.malicious.detectionRate<=5?"default":"destructive",className:"text-xs",children:r.malicious.detectionRate<=5?"Low threat":"High threat"})})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"DKIM Authentication"}),t(G,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[r.dkim.adoptionRate.toFixed(1),"%"]}),e("div",{className:"text-xs text-muted-foreground",children:[o(r.dkim.pass)," pass / ",o(r.dkim.fail)," fail"]}),t("div",{className:"mt-2",children:t(m,{variant:r.dkim.adoptionRate>=80?"default":"secondary",className:"text-xs",children:r.dkim.adoptionRate>=80?"Good adoption":"Needs improvement"})})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"SPF Validation"}),t(G,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[r.spf.adoptionRate.toFixed(1),"%"]}),e("div",{className:"text-xs text-muted-foreground",children:[o(r.spf.pass)," pass / ",o(r.spf.fail)," fail"]}),t("div",{className:"mt-2",children:t(m,{variant:r.spf.adoptionRate>=80?"default":"secondary",className:"text-xs",children:r.spf.adoptionRate>=80?"Good adoption":"Needs improvement"})})]})]}),e(l,{children:[e(n,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t(c,{className:"text-sm font-medium",children:"Spam Detection"}),t(K,{className:"h-4 w-4 text-muted-foreground"})]}),e(d,{children:[e("div",{className:"text-2xl font-bold",children:[r.spam.detectionRate.toFixed(1),"%"]}),e("div",{className:"text-xs text-muted-foreground",children:[o(r.spam.detected)," spam of"," ",o(r.spam.detected+r.spam.clean)," total"]}),t("div",{className:"mt-2",children:t(m,{variant:r.spam.detectionRate<=10?"default":"destructive",className:"text-xs",children:r.spam.detectionRate<=10?"Low spam":"High spam"})})]})]})]}),e(l,{children:[e(n,{children:[t(c,{className:"text-lg",children:"Email Security Summary"}),t(h,{children:"Global email authentication and threat detection metrics"})]}),t(d,{children:e("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e("div",{children:[t("h4",{className:"font-semibold mb-3",children:"Authentication Protocols"}),e("div",{className:"space-y-2",children:[e("div",{className:"flex justify-between items-center",children:[t("span",{className:"text-sm",children:"DKIM Adoption"}),e("span",{className:`text-sm font-medium ${p(r.dkim.adoptionRate)}`,children:[r.dkim.adoptionRate.toFixed(1),"%"]})]}),e("div",{className:"flex justify-between items-center",children:[t("span",{className:"text-sm",children:"SPF Adoption"}),e("span",{className:`text-sm font-medium ${p(r.spf.adoptionRate)}`,children:[r.spf.adoptionRate.toFixed(1),"%"]})]})]})]}),e("div",{children:[t("h4",{className:"font-semibold mb-3",children:"Threat Detection"}),e("div",{className:"space-y-2",children:[e("div",{className:"flex justify-between items-center",children:[t("span",{className:"text-sm",children:"Malicious Rate"}),e("span",{className:`text-sm font-medium ${x(r.malicious.detectionRate)}`,children:[r.malicious.detectionRate.toFixed(1),"%"]})]}),e("div",{className:"flex justify-between items-center",children:[t("span",{className:"text-sm",children:"Spam Rate"}),e("span",{className:`text-sm font-medium ${x(r.spam.detectionRate)}`,children:[r.spam.detectionRate.toFixed(1),"%"]})]})]})]})]})})]})]})},Re=()=>{const{radarData:s,loading:a,refetch:i}=u();return e("div",{className:"space-y-6",children:[e("div",{className:"flex justify-between items-center",children:[e("div",{children:[e("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[t(U,{className:"h-6 w-6"}),"Global Threat Intelligence"]}),t("p",{className:"text-gray-600 mt-1",children:"Real-time cybersecurity insights powered by Cloudflare Radar"})]}),e("div",{className:"flex gap-2",children:[e(o,{onClick:()=>{i()},disabled:a,variant:"outline",size:"sm",children:[t(V,{className:"h-4 w-4 mr-2 "+(a?"animate-spin":"")}),"Refresh"]}),e(o,{onClick:()=>{if(!s)return;const e={generated:(new Date).toISOString(),summary:{totalPhishingAttacks:s.phishing.total,totalSpoofingAttempts:s.spoofing.total,dmarcAdoptionRate:s.dmarc.adoptionRate,industryRisks:s.industryRisks,emailSecurity:s.emailSecurity?{maliciousDetectionRate:s.emailSecurity.malicious.detectionRate,dkimAdoptionRate:s.emailSecurity.dkim.adoptionRate,spfAdoptionRate:s.emailSecurity.spf.adoptionRate,spamDetectionRate:s.emailSecurity.spam.detectionRate}:null},recommendations:["Implement DMARC with strict policy to reduce spoofing attempts","Conduct regular phishing awareness training for employees","Monitor industry-specific threat trends for proactive defense","Consider implementing additional email security measures",...s.emailSecurity?[`Improve DKIM adoption (currently ${s.emailSecurity.dkim.adoptionRate.toFixed(1)}%)`,`Enhance SPF validation (currently ${s.emailSecurity.spf.adoptionRate.toFixed(1)}%)`,"Monitor malicious email detection trends for emerging threats"]:[]]},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),a=URL.createObjectURL(t),i=document.createElement("a");i.href=a,i.download=`threat-intelligence-report-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a)},disabled:!s,variant:"outline",size:"sm",children:[t(W,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})]}),t(_e,{}),t(ke,{}),e(l,{children:[e(n,{children:[t(c,{children:"Threat Intelligence Insights"}),t(h,{children:"How this data enhances your security assessments"})]}),t(d,{children:e("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e("div",{children:[t("h4",{className:"font-semibold mb-2",children:"Assessment Enhancement"}),e("ul",{className:"text-sm space-y-1 text-gray-600",children:[t("li",{children:"• Real-time threat context for risk scoring"}),t("li",{children:"• Industry-specific risk benchmarking"}),t("li",{children:"• Current attack trend awareness"}),t("li",{children:"• DMARC compliance comparison"}),t("li",{children:"• Email authentication protocol analysis"}),t("li",{children:"• Malicious email detection insights"})]})]}),e("div",{children:[t("h4",{className:"font-semibold mb-2",children:"Client Value"}),e("ul",{className:"text-sm space-y-1 text-gray-600",children:[t("li",{children:"• Demonstrate current threat landscape"}),t("li",{children:"• Provide industry-relative risk positioning"}),t("li",{children:"• Support recommendations with live data"}),t("li",{children:"• Show urgency of security measures"}),t("li",{children:"• Validate email security implementations"}),t("li",{children:"• Benchmark authentication protocol adoption"})]})]})]})})]})]})},Se=()=>{const[s,a]=b([]),[u,p]=b(null),[x,g]=b(!1),[f,y]=b(!0),{toast:N}=i(),v=async()=>{try{const{data:e,error:t}=await r.from("email_queue").select("*").order("created_at",{ascending:!1}).limit(50);if(t)throw t;a(e||[]);const{data:s,error:i}=await r.rpc("get_email_queue_status");i||s&&s.length>0&&p(s[0])}catch(e){N({title:"Error",description:"Failed to fetch email queue data",variant:"destructive"})}finally{y(!1)}};w((()=>{v();const e=setInterval(v,3e4);return()=>clearInterval(e)}),[]);const _=(s,a)=>{switch(s){case"pending":return e(m,{variant:"secondary",className:"flex items-center gap-1",children:[t(Q,{className:"h-3 w-3"}),"Pending"]});case"sent":return e(m,{variant:"default",className:"flex items-center gap-1 bg-green-600",children:[t(G,{className:"h-3 w-3"}),"Sent"]});case"failed":return e(m,{variant:"destructive",className:"flex items-center gap-1",children:[t(K,{className:"h-3 w-3"}),"Failed (",a,"/3)"]});default:return t(m,{variant:"outline",children:s})}},k=e=>new Date(e).toLocaleString();return f?t(l,{children:e(d,{className:"flex items-center justify-center py-8",children:[t(V,{className:"h-6 w-6 animate-spin mr-2"}),"Loading email automation data..."]})}):e("div",{className:"space-y-6",children:[e("div",{className:"flex items-center justify-between",children:[e("div",{children:[t("h2",{className:"text-2xl font-bold tracking-tight",children:"Email Automation"}),t("p",{className:"text-muted-foreground",children:"Monitor and manage automated email campaigns"})]}),e("div",{className:"flex items-center gap-2",children:[e(o,{onClick:v,variant:"outline",size:"sm",disabled:f,children:[t(V,{className:"h-4 w-4 mr-2 "+(f?"animate-spin":"")}),"Refresh"]}),e(o,{onClick:async()=>{g(!0);try{const{data:e,error:t}=await r.functions.invoke("process-email-queue",{body:{manual:!0}});if(t)throw t;N({title:"Email Queue Processed",description:`Processed ${e.results?.processed||0} emails. ${e.results?.successful||0} successful, ${e.results?.failed||0} failed.`}),await v()}catch(e){N({title:"Processing Failed",description:e.message||"Failed to process email queue",variant:"destructive"})}finally{g(!1)}},disabled:x,size:"sm",children:[x?t(V,{className:"h-4 w-4 mr-2 animate-spin"}):t(z,{className:"h-4 w-4 mr-2"}),"Process Queue"]})]})]}),u&&e("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t(l,{children:e(d,{className:"flex items-center p-6",children:[t(T,{className:"h-8 w-8 text-blue-600 mr-3"}),e("div",{children:[t("p",{className:"text-2xl font-bold",children:u.total_queued}),t("p",{className:"text-xs text-muted-foreground",children:"Total Queued"})]})]})}),t(l,{children:e(d,{className:"flex items-center p-6",children:[t(Q,{className:"h-8 w-8 text-yellow-600 mr-3"}),e("div",{children:[t("p",{className:"text-2xl font-bold",children:u.pending}),t("p",{className:"text-xs text-muted-foreground",children:"Pending"})]})]})}),t(l,{children:e(d,{className:"flex items-center p-6",children:[t(G,{className:"h-8 w-8 text-green-600 mr-3"}),e("div",{children:[t("p",{className:"text-2xl font-bold",children:u.sent}),t("p",{className:"text-xs text-muted-foreground",children:"Sent"})]})]})}),t(l,{children:e(d,{className:"flex items-center p-6",children:[t(K,{className:"h-8 w-8 text-red-600 mr-3"}),e("div",{children:[t("p",{className:"text-2xl font-bold",children:u.failed}),t("p",{className:"text-xs text-muted-foreground",children:"Failed"})]})]})})]}),e(l,{children:[e(n,{children:[t(c,{children:"Email Queue"}),t(h,{children:"Recent emails in the automation queue"})]}),t(d,{children:0===s.length?t("div",{className:"text-center py-8 text-muted-foreground",children:"No emails in queue"}):t("div",{className:"space-y-4",children:s.map((s=>e("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[e("div",{className:"flex-1",children:[e("div",{className:"flex items-center gap-2 mb-1",children:[t("h4",{className:"font-medium",children:s.recipient_email}),_(s.status,s.attempts)]}),e("p",{className:"text-sm text-muted-foreground",children:[s.company_name," • ",s.email_type," • Scheduled: ",k(s.scheduled_at)]}),s.error_message&&e("p",{className:"text-sm text-red-600 mt-1",children:["Error: ",s.error_message]})]}),e("div",{className:"flex items-center gap-2",children:["pending"===s.status&&e(o,{onClick:()=>(async e=>{try{const{data:t,error:s}=await r.functions.invoke("send-lead-email",{body:{emailQueueId:e}});if(s)throw s;N({title:"Test Email Sent",description:"Email delivery test completed successfully"}),await v()}catch(t){N({title:"Test Failed",description:t.message||"Failed to send test email",variant:"destructive"})}})(s.id),variant:"outline",size:"sm",children:[t(T,{className:"h-4 w-4 mr-1"}),"Test Send"]}),t("div",{className:"text-xs text-muted-foreground text-right",children:s.sent_at?e("div",{children:["Sent: ",k(s.sent_at)]}):e("div",{children:["Attempts: ",s.attempts,"/3"]})})]})]},s.id)))})})]})]})},Ce=()=>{const[s,a]=b(!1),{toast:m}=i();return e(l,{children:[t(n,{children:t(c,{children:"Data Population Utility"})}),e(d,{className:"space-y-4",children:[t("p",{className:"text-sm text-muted-foreground",children:"This utility helps you populate the database with sample data for testing purposes."}),e("div",{className:"flex gap-4",children:[t(o,{onClick:async()=>{a(!0);try{const{data:e}=await r.from("assessment_types").select("*").limit(1);if(e&&e.length>0)return void m({title:"Data Already Exists",description:"Sample data has already been populated.",variant:"default"});const t=await(async()=>{const{data:e,error:t}=await r.from("assessment_types").insert({name:"Cybersecurity Risk Assessment",title:"Comprehensive Cybersecurity Risk Assessment",description:"Evaluate your organization's cybersecurity posture and identify potential vulnerabilities.",category:"Security",slug:"cybersecurity-risk-assessment",estimated_time_minutes:15,is_active:!0,order_index:1}).select().single();if(t)throw t;return e.id})(),s=await(async e=>{const t=[{assessment_type_id:e,question_text:"Does your organization have a formal cybersecurity policy?",category:"Policy & Governance",order_index:1,is_active:!0},{assessment_type_id:e,question_text:"Do you use multi-factor authentication for critical systems?",category:"Access Control",order_index:2,is_active:!0},{assessment_type_id:e,question_text:"How frequently do you backup your critical data?",category:"Data Protection",order_index:3,is_active:!0},{assessment_type_id:e,question_text:"Do you conduct regular security awareness training?",category:"Training & Awareness",order_index:4,is_active:!0},{assessment_type_id:e,question_text:"Do you have an incident response plan?",category:"Incident Management",order_index:5,is_active:!0}],{data:s}=await r.from("assessment_questions").select("id").order("id",{ascending:!1}).limit(1);let a=s&&s.length>0?s[0].id+1:1;const i=t.map(((e,t)=>({...e,id:a+t}))),{data:l,error:n}=await r.from("assessment_questions").insert(i).select();if(n)throw n;return l})(t);await(async e=>{const t=[];e.forEach(((e,s)=>{let a=[];switch(s){case 0:a=[{option_text:"Yes, comprehensive and regularly updated",risk_score:1,order_index:1},{option_text:"Yes, but needs updating",risk_score:3,order_index:2},{option_text:"Basic policy exists",risk_score:5,order_index:3},{option_text:"No formal policy",risk_score:8,order_index:4}];break;case 1:a=[{option_text:"Yes, for all critical systems",risk_score:1,order_index:1},{option_text:"Yes, for some systems",risk_score:4,order_index:2},{option_text:"Planning to implement",risk_score:6,order_index:3},{option_text:"No MFA implemented",risk_score:9,order_index:4}];break;case 2:a=[{option_text:"Daily automated backups",risk_score:1,order_index:1},{option_text:"Weekly backups",risk_score:3,order_index:2},{option_text:"Monthly backups",risk_score:5,order_index:3},{option_text:"No regular backups",risk_score:8,order_index:4}];break;case 3:a=[{option_text:"Quarterly training sessions",risk_score:1,order_index:1},{option_text:"Annual training",risk_score:3,order_index:2},{option_text:"Ad-hoc training",risk_score:5,order_index:3},{option_text:"No formal training",risk_score:7,order_index:4}];break;case 4:a=[{option_text:"Comprehensive tested plan",risk_score:1,order_index:1},{option_text:"Plan exists, not tested",risk_score:4,order_index:2},{option_text:"Basic plan in development",risk_score:6,order_index:3},{option_text:"No incident response plan",risk_score:8,order_index:4}]}a.forEach((s=>{t.push({...s,question_id:e.id})}))}));const{data:s,error:a}=await r.from("assessment_question_options").insert(t);if(a)throw a;return s})(s);const a=await(async e=>{const t=[{assessment_type_id:e,company_name:"TechCorp Ltd",industry:"Technology",employee_count:"50-100",contact_name:"John Smith",email:"<EMAIL>",phone:"+64 21 123 4567",status:"completed"},{assessment_type_id:e,company_name:"HealthPlus Medical",industry:"Healthcare",employee_count:"100-500",contact_name:"Sarah Johnson",email:"<EMAIL>",phone:"+64 21 987 6543",status:"completed"},{assessment_type_id:e,company_name:"RetailMax",industry:"Retail",employee_count:"10-50",contact_name:"Mike Brown",email:"<EMAIL>",phone:"+64 21 555 0123",status:"in_progress"}],{data:s,error:a}=await r.from("assessment_submissions").insert(t).select();if(a)throw a;return s})(t);await(async e=>{const t=e.map(((e,t)=>{const s=["HIGH","MEDIUM","LOW"][t%3];return{submission_id:e.id,risk_level:s,risk_percentage:"HIGH"===s?85:"MEDIUM"===s?55:25,lead_priority:"HIGH"===s?1:"MEDIUM"===s?3:5,total_risk_score:"HIGH"===s?34:"MEDIUM"===s?22:10,max_possible_score:40,follow_up_urgency:"HIGH"===s?"Immediate":"MEDIUM"===s?"Within 24 hours":"Within 1 week"}})),{data:s,error:a}=await r.from("lead_scores").insert(t);if(a)throw a;return s})(a),m({title:"Success",description:"Sample data has been populated successfully!",variant:"default"})}catch(e){m({title:"Error",description:"Failed to populate sample data. Check console for details.",variant:"destructive"})}finally{a(!1)}},disabled:s,variant:"default",children:s?"Populating...":"Populate Sample Data"}),t(o,{onClick:async()=>{a(!0);try{await r.from("lead_scores").delete().neq("id","00000000-0000-0000-0000-000000000000"),await r.from("assessment_question_options").delete().neq("id","00000000-0000-0000-0000-000000000000"),await r.from("assessment_questions").delete().neq("id",0),await r.from("assessment_submissions").delete().neq("id","00000000-0000-0000-0000-000000000000"),await r.from("assessment_types").delete().neq("id","00000000-0000-0000-0000-000000000000"),m({title:"Success",description:"All sample data has been cleared.",variant:"default"})}catch(e){m({title:"Error",description:"Failed to clear sample data. Check console for details.",variant:"destructive"})}finally{a(!1)}},disabled:s,variant:"destructive",children:s?"Clearing...":"Clear All Data"})]}),e("div",{className:"text-sm text-muted-foreground",children:[t("strong",{children:"What this creates:"}),e("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[t("li",{children:"1 Assessment Type (Cybersecurity Risk Assessment)"}),t("li",{children:"5 Sample Questions with multiple choice options"}),t("li",{children:"3 Sample Company Submissions"}),t("li",{children:"Lead Scores for each submission"})]})]})]})]})},Ae=()=>e("div",{className:"min-h-screen bg-black",children:[t(p,{title:"Admin Dashboard | Business Intelligence | BlackVeil",description:"Comprehensive business intelligence dashboard with analytics, lead management, and A/B testing for BlackVeil Security.",canonicalUrl:"https://blackveil.co.nz/admin"}),t(x,{className:"pt-16 pb-8",children:e("div",{className:"max-w-7xl mx-auto",children:[t("h1",{className:"text-3xl font-bold mb-8 text-white",children:"Business Intelligence Dashboard"}),e(g,{defaultValue:"leads",className:"space-y-6",children:[e(f,{className:"grid w-full grid-cols-6 bg-gray-900 border-gray-700",children:[e(y,{value:"leads",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t(A,{className:"h-4 w-4"}),"Lead Management"]}),e(y,{value:"analytics",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t(q,{className:"h-4 w-4"}),"Analytics"]}),e(y,{value:"threats",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t(O,{className:"h-4 w-4"}),"Threat Intel"]}),e(y,{value:"email",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t(T,{className:"h-4 w-4"}),"Email Automation"]}),e(y,{value:"abtesting",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t(P,{className:"h-4 w-4"}),"A/B Testing"]}),e(y,{value:"settings",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t(Y,{className:"h-4 w-4"}),"Settings"]})]}),t(N,{value:"leads",className:"space-y-6",children:e("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[t("div",{className:"lg:col-span-2",children:t(Ne,{})}),t("div",{children:t(ve,{})})]})}),t(N,{value:"analytics",className:"space-y-6",children:t("div",{className:"bg-white rounded-lg p-6",children:t(be,{})})}),t(N,{value:"threats",className:"space-y-6",children:t("div",{className:"bg-white rounded-lg p-6",children:t(Re,{})})}),t(N,{value:"email",className:"space-y-6",children:t("div",{className:"bg-white rounded-lg p-6",children:t(Se,{})})}),t(N,{value:"abtesting",className:"space-y-6",children:t("div",{className:"bg-white rounded-lg p-6",children:t(we,{})})}),t(N,{value:"settings",className:"space-y-6",children:e("div",{className:"space-y-6",children:[t(Ce,{}),t("div",{className:"bg-white rounded-lg p-6",children:e("div",{className:"text-center py-12",children:[t(Y,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),t("h3",{className:"text-lg font-semibold mb-2",children:"Additional Settings"}),t("p",{className:"text-muted-foreground",children:"Additional system configuration and preferences will be available here."})]})})]})})]})]})})]});export{Ae as default};
