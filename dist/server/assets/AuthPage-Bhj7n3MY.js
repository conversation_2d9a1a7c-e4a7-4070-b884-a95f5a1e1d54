import{jsxs as e,jsx as t,Fragment as a}from"react/jsx-runtime";import{useState as i,useEffect as r}from"react";import{useNavigate as s}from"react-router-dom";import{k as n,u as l,C as c,c as o,d,a as m,b as u,P as h,S as g}from"../entry-server.js";import{I as p}from"./input-Bha7rE9x.js";import{L as v}from"./label-BdpnMMwV.js";import{Mail as b,Lock as y,Loader2 as w}from"lucide-react";import"react-helmet";import"react-dom/server";import"react-router-dom/server.mjs";import"framer-motion";import"@supabase/supabase-js";import"@radix-ui/react-slot";import"class-variance-authority";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";import"./security-D6XyL6Yo.js";import"@radix-ui/react-label";const f=({mode:r,onModeChange:s})=>{const[h,g]=i(""),[f,x]=i(""),[N,k]=i(!1),{signUp:P,signIn:C}=n(),{toast:S}=l();return e(c,{className:"cyber-gradient-card border border-green-muted/30 w-full max-w-md",children:[e(o,{className:"text-center",children:[t(d,{className:"text-white text-2xl",children:"login"===r?"Sign In":"Create Account"}),t("p",{className:"text-white/60",children:"login"===r?"Welcome back to BlackVeil Security":"Join BlackVeil Security Platform"})]}),e(m,{children:[e("form",{onSubmit:async e=>{e.preventDefault(),k(!0);try{if(!h.trim())return S({title:"Validation Error",description:"Please enter your email address.",variant:"destructive"}),void k(!1);if(!f.trim())return S({title:"Validation Error",description:"Please enter your password.",variant:"destructive"}),void k(!1);if(f.length<6)return S({title:"Validation Error",description:"Password must be at least 6 characters long.",variant:"destructive"}),void k(!1);if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(h))return S({title:"Validation Error",description:"Please enter a valid email address.",variant:"destructive"}),void k(!1);const{error:e}="login"===r?await C(h,f):await P(h,f);if(e){let t=e.message||"An unknown error occurred";t.includes("Invalid login credentials")?t="Invalid email or password. Please check your credentials and try again.":t.includes("User already registered")?t="An account with this email already exists. Try signing in instead.":t.includes("Password should be at least")?t="Password must be at least 6 characters long.":t.includes("Unable to validate email")?t="Please enter a valid email address.":t.includes("fetch")||t.includes("Network")?t="Connection failed. Please check your internet connection and try again.":t.includes("authentication service")&&(t="Authentication service is currently unavailable. Please try again in a moment."),S({title:"Authentication Error",description:t,variant:"destructive"})}else S("signup"===r?{title:"Account created successfully!",description:"Please check your email to verify your account before signing in."}:{title:"Welcome back!",description:"You have successfully signed in."})}catch(t){S({title:"Unexpected Error",description:"Something went wrong. Please try again.",variant:"destructive"})}finally{k(!1)}},className:"space-y-4",children:[e("div",{className:"space-y-2",children:[t(v,{htmlFor:"email",className:"text-white",children:"Email"}),e("div",{className:"relative",children:[t(b,{className:"absolute left-3 top-3 h-4 w-4 text-white/60"}),t(p,{id:"email",type:"email",placeholder:"<EMAIL>",value:h,onChange:e=>g(e.target.value),required:!0,className:"pl-10 bg-black-soft border-green-muted/30 text-white",disabled:N})]})]}),e("div",{className:"space-y-2",children:[t(v,{htmlFor:"password",className:"text-white",children:"Password"}),e("div",{className:"relative",children:[t(y,{className:"absolute left-3 top-3 h-4 w-4 text-white/60"}),t(p,{id:"password",type:"password",placeholder:"••••••••",value:f,onChange:e=>x(e.target.value),required:!0,minLength:6,className:"pl-10 bg-black-soft border-green-muted/30 text-white",disabled:N})]})]}),t(u,{type:"submit",disabled:N,className:"w-full bg-green-bright hover:bg-green-muted text-black font-semibold",children:N?e(a,{children:[t(w,{className:"mr-2 h-4 w-4 animate-spin"}),"login"===r?"Signing In...":"Creating Account..."]}):"login"===r?"Sign In":"Create Account"})]}),e("div",{className:"mt-6 text-center",children:[t("p",{className:"text-white/60",children:"login"===r?"Don't have an account?":"Already have an account?"}),t(u,{variant:"link",onClick:()=>s("login"===r?"signup":"login"),className:"text-green-bright hover:text-green-muted p-0 h-auto",disabled:N,children:"login"===r?"Create one here":"Sign in instead"})]})]})]})},x=()=>{const[a,l]=i("login"),{user:c,isLoading:o}=n(),d=s();return r((()=>{!o&&c&&d("/")}),[c,o,d]),o?t("div",{className:"min-h-screen bg-black flex items-center justify-center",children:t("div",{className:"text-white",children:"Loading..."})}):e("div",{className:"min-h-screen bg-black",children:[t(h,{title:("login"===a?"Sign In":"Create Account")+" | BlackVeil Security",description:"Access your BlackVeil Security account or create a new one to manage phishing assessments and security tools.",canonicalUrl:"https://blackveil.co.nz/auth"}),t(g,{className:"py-20",children:t("div",{className:"max-w-md mx-auto",children:t(f,{mode:a,onModeChange:l})})})]})};export{x as default};
