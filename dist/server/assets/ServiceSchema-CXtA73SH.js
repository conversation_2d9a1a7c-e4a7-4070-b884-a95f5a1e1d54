import{jsx as e}from"react/jsx-runtime";import{Helmet as r}from"react-helmet";const t=({name:t,description:i,url:c,provider:a="BlackVeil",areaServed:n="New Zealand",serviceType:o="Cybersecurity Service"})=>{const p={"@context":"https://schema.org","@type":"Service",name:t,description:i,provider:{"@type":"Organization",name:a,url:"https://blackveil.co.nz"},serviceType:o,areaServed:n,url:c};return e(r,{children:e("script",{type:"application/ld+json",children:JSON.stringify(p)})})};export{t as S};
