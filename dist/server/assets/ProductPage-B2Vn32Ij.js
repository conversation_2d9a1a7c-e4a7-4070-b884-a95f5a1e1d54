import{jsxs as e,jsx as t,Fragment as a}from"react/jsx-runtime";import{S as r,h as i,P as s,i as n}from"../entry-server.js";import{Shield as l,CheckCircle as d,<PERSON>an as o,Search as c,Cloud as m,FileText as h,Bell as u,MessageCircle as g,Send as x,AlertCircle as p,Info as f,Check as b}from"lucide-react";import{motion as N}from"framer-motion";import{A as y}from"./animated-reveal-CRgSwRYY.js";import{C as w}from"./cyber-card-Bx9QCtGd.js";import v,{useState as k}from"react";import"react-dom/server";import"react-router-dom/server.mjs";import"react-router-dom";import"@supabase/supabase-js";import"@radix-ui/react-slot";import"class-variance-authority";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"react-helmet";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";const S=({className:a=""})=>e(r,{background:"soft",className:`py-16 xs:py-20 md:py-24 overflow-hidden relative ${a}`,children:[t("div",{className:"absolute top-20 right-10 w-64 h-64 bg-green-bright/5 rounded-full blur-3xl"}),t("div",{className:"absolute bottom-10 left-10 w-96 h-96 bg-green-bright/5 rounded-full blur-3xl"}),t("div",{className:"container relative z-10",children:e("div",{className:"flex flex-col items-center text-center max-w-4xl mx-auto mb-8 md:mb-12",children:[t(y,{animation:"fade-down",delay:100,children:e("div",{className:"flex items-center gap-2 mb-6 bg-black-soft border border-green-muted/30 px-4 py-2 rounded-full",children:[t(l,{className:"w-4 h-4 text-green-bright"}),t("span",{className:"text-sm font-medium text-green-bright",children:"NEW PRODUCT"})]})}),t(y,{animation:"fade-up",delay:200,children:e("h1",{className:"text-4xl xs:text-5xl md:text-6xl font-bold mb-6",children:[t("span",{className:"text-white",children:"Modern Domain Security,"}),t("br",{}),t("span",{className:"text-green-bright",children:"Automated."})]})}),t(y,{animation:"fade-up",delay:300,children:t("p",{className:"text-lg xs:text-xl text-white/80 max-w-3xl mb-8",children:"A sleek, automated platform that helps you secure your domain and email systems in seconds. Whether you're a solo operator or running an enterprise, BlackVault Lite delivers instant insights, actionable advice, and zero guesswork."})}),t(y,{animation:"fade-up",delay:400,children:e("div",{className:"flex flex-col xs:flex-row gap-4 mt-6",children:[t(N.a,{href:"#features",className:"cyber-button text-base px-8 py-3",whileHover:{scale:1.03},whileTap:{scale:.97},children:"Get Started"}),t(N.a,{href:"#demo",className:"bg-transparent border border-green-muted/50 text-white hover:text-green-bright px-8 py-3 rounded-md text-base font-mono uppercase tracking-wider transition-colors duration-300",whileHover:{scale:1.03},whileTap:{scale:.97},children:"Watch Demo"})]})})]})})]}),D=({icon:a,text:r,className:i=""})=>e("span",{className:`inline-flex items-center text-[9px] xs:text-[10px] sm:text-xs whitespace-nowrap bg-green-dark/30 text-green-light px-2 py-1 rounded-full border border-green-muted/30 ${i}`,children:[t(a,{className:"h-2.5 w-2.5 xs:h-3 xs:w-3 mr-1 text-green-bright"}),r]}),C=()=>{const a=[{icon:t(o,{className:"w-8 h-8 text-green-bright"}),title:"Scans Your Domain",description:"Our platform checks for SPF, DKIM, DMARC issues, DNS & MX misconfigurations, and DNSSEC & deliverability risks.",tags:["SPF","DKIM","DMARC","DNS","MX","DNSSEC"]},{icon:t(l,{className:"w-8 h-8 text-green-bright"}),title:"Modern Security Standards",description:"We ensure your domain meets modern security standards to protect your organization from the latest email security threats.",tags:["Anti-Phishing","Anti-Spoofing","Compliance"]},{icon:t(d,{className:"w-8 h-8 text-green-bright"}),title:"Fast Implementation",description:"Get started in seconds with our streamlined setup process and receive immediate insights about your domain security.",tags:["Instant Setup","Quick Results"]}];return t(r,{id:"features",className:"py-16 md:py-20",background:"grid",children:e("div",{className:"container",children:[e("div",{className:"max-w-3xl mx-auto text-center mb-12 md:mb-16",children:[t(y,{animation:"fade-up",children:t("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Domain Security Made Simple"})}),t(y,{animation:"fade-up",delay:100,children:t("p",{className:"text-white/70 text-lg",children:"BlackVault Lite automates complex security tasks that typically require expert knowledge, delivering clarity and peace of mind for your organization."})})]}),t("div",{className:"grid md:grid-cols-3 gap-6 md:gap-8",children:a.map(((a,r)=>t(y,{animation:"fade-up",delay:100+100*r,children:t(w,{className:"h-full",children:e("div",{className:"flex flex-col h-full",children:[t("div",{className:"mb-4",children:a.icon}),t("h3",{className:"text-xl font-bold mb-2 text-green-bright",children:a.title}),t("p",{className:"text-white/70 mb-6",children:a.description}),t("div",{className:"mt-auto flex flex-wrap gap-2",children:a.tags.map((e=>t(D,{icon:d,text:e},e)))})]})})},a.title)))})]})})},M=()=>t(r,{className:"py-16 md:py-20",children:t("div",{className:"container",children:e("div",{className:"flex flex-col lg:flex-row gap-12 lg:gap-16 items-center",children:[e("div",{className:"w-full lg:w-1/2",children:[t(y,{animation:"fade-right",children:e("div",{className:"inline-flex items-center gap-2 mb-4 bg-green-dark/30 text-green-light px-3 py-1 rounded-full text-sm",children:[t(c,{className:"w-4 h-4"}),t("span",{children:"Intelligent Detection"})]})}),t(y,{animation:"fade-right",delay:100,children:t("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Detects Your Providers"})}),t(y,{animation:"fade-right",delay:200,children:e("div",{className:"space-y-4",children:[t("p",{className:"text-white/80",children:"BlackVault Lite automatically identifies the platforms your organization is using, from email services to DNS providers."}),e("ul",{className:"space-y-2 text-white/70",children:[e("li",{className:"flex items-start",children:[t("span",{className:"text-green-bright mr-2",children:"✓"}),t("span",{children:"Instantly identifies platforms like Microsoft 365, Google Workspace, Cloudflare, AWS, and more"})]}),e("li",{className:"flex items-start",children:[t("span",{className:"text-green-bright mr-2",children:"✓"}),t("span",{children:"Delivers context-specific advice tailored to your actual setup"})]}),e("li",{className:"flex items-start",children:[t("span",{className:"text-green-bright mr-2",children:"✓"}),t("span",{children:"Provides clear instructions based on your specific provider ecosystem"})]})]})]})})]}),t("div",{className:"w-full lg:w-1/2",children:t(y,{animation:"fade-left",delay:300,children:e("div",{className:"relative border border-green-muted/30 bg-black-soft rounded-lg p-6",children:[t("div",{className:"absolute -top-3 -left-3 h-6 w-6 border-t-2 border-l-2 border-green-bright/70"}),t("div",{className:"absolute -bottom-3 -right-3 h-6 w-6 border-b-2 border-r-2 border-green-bright/70"}),e("div",{className:"flex items-center justify-center mb-6",children:[t(m,{className:"w-12 h-12 text-green-bright mr-3"}),t("div",{className:"text-xl font-medium",children:"Provider Detection"})]}),t("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-4",children:[{name:"Microsoft 365",icon:"M"},{name:"Google Workspace",icon:"G"},{name:"Cloudflare",icon:"C"},{name:"AWS",icon:"A"},{name:"Azure",icon:"Az"},{name:"GoDaddy",icon:"GD"}].map((a=>e("div",{className:"flex items-center p-3 bg-black-muted border border-green-muted/20 rounded-md",children:[t("div",{className:"w-8 h-8 rounded-full bg-green-dark/50 text-green-bright flex items-center justify-center mr-3",children:a.icon}),t("span",{className:"text-sm",children:a.name})]},a.name)))})]})})})]})})}),A=()=>t(r,{className:"py-16 md:py-20",background:"soft",children:t("div",{className:"container",children:e("div",{className:"flex flex-col-reverse lg:flex-row gap-12 lg:gap-16 items-center",children:[t("div",{className:"w-full lg:w-1/2",children:t(y,{animation:"fade-right",delay:300,children:e("div",{className:"border border-green-muted/30 bg-black-soft rounded-lg overflow-hidden shadow-lg",children:[t("div",{className:"border-b border-green-muted/20 p-4",children:e("div",{className:"flex items-center",children:[t(h,{className:"w-5 h-5 text-green-bright mr-2"}),t("h3",{className:"text-lg font-medium",children:"Weekly Security Report"})]})}),e("div",{className:"p-5",children:[e("div",{className:"mb-5",children:[t("h4",{className:"text-sm uppercase text-green-muted/70 mb-2",children:"Domain Status"}),e("div",{className:"flex justify-between items-center mb-2",children:[t("span",{className:"text-white/70",children:"SPF Record"}),e("span",{className:"text-green-bright flex items-center",children:[t(d,{className:"w-4 h-4 mr-1"})," Valid"]})]}),e("div",{className:"flex justify-between items-center mb-2",children:[t("span",{className:"text-white/70",children:"DKIM Configuration"}),e("span",{className:"text-green-bright flex items-center",children:[t(d,{className:"w-4 h-4 mr-1"})," Valid"]})]}),e("div",{className:"flex justify-between items-center mb-2",children:[t("span",{className:"text-white/70",children:"DMARC Policy"}),e("span",{className:"text-yellow-500 flex items-center",children:[t(u,{className:"w-4 h-4 mr-1"})," Monitoring"]})]}),e("div",{className:"flex justify-between items-center",children:[t("span",{className:"text-white/70",children:"DNSSEC"}),e("span",{className:"text-red-500 flex items-center",children:[t(u,{className:"w-4 h-4 mr-1"})," Not Configured"]})]})]}),e("div",{className:"mb-5",children:[e("div",{className:"flex justify-between mb-1",children:[t("span",{className:"text-sm text-white/70",children:"Security Score"}),t("span",{className:"text-sm text-green-bright",children:"78%"})]}),t("div",{className:"h-2 bg-black-muted rounded-full overflow-hidden",children:t("div",{className:"h-full bg-green-bright rounded-full",style:{width:"78%"}})})]}),e("div",{children:[t("h4",{className:"text-sm uppercase text-green-muted/70 mb-2",children:"Recommendations"}),e("ul",{className:"space-y-2",children:[t("li",{className:"bg-black-muted p-2 rounded text-sm text-white/70",children:"Enable DNSSEC on your domain for added security"}),t("li",{className:"bg-black-muted p-2 rounded text-sm text-white/70",children:"Consider upgrading DMARC policy from monitoring to quarantine"})]})]})]})]})})}),e("div",{className:"w-full lg:w-1/2",children:[t(y,{animation:"fade-left",children:e("div",{className:"inline-flex items-center gap-2 mb-4 bg-green-dark/30 text-green-light px-3 py-1 rounded-full text-sm",children:[t(h,{className:"w-4 h-4"}),t("span",{children:"Automated Intelligence"})]})}),t(y,{animation:"fade-left",delay:100,children:t("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Automated Reports & Alerts"})}),t(y,{animation:"fade-left",delay:200,children:e("div",{className:"space-y-4",children:[t("p",{className:"text-white/80",children:"Stay informed without the overhead. BlackVault Lite continuously monitors your domain security and delivers actionable insights directly to your inbox."}),e("ul",{className:"space-y-3",children:[e("li",{className:"flex items-start",children:[t(d,{className:"w-5 h-5 text-green-bright mr-3 mt-0.5 flex-shrink-0"}),t("span",{className:"text-white/70",children:"Weekly domain security summaries show your security posture at a glance"})]}),e("li",{className:"flex items-start",children:[t(d,{className:"w-5 h-5 text-green-bright mr-3 mt-0.5 flex-shrink-0"}),t("span",{className:"text-white/70",children:"Real-time alerts notify you of critical changes or potential security issues"})]}),e("li",{className:"flex items-start",children:[t(d,{className:"w-5 h-5 text-green-bright mr-3 mt-0.5 flex-shrink-0"}),t("span",{className:"text-white/70",children:"Clean, shareable reports for teams, clients, or compliance documentation"})]}),e("li",{className:"flex items-start",children:[t(d,{className:"w-5 h-5 text-green-bright mr-3 mt-0.5 flex-shrink-0"}),t("span",{className:"text-white/70",children:"Track security improvements over time with historical data and trend analysis"})]})]})]})})]})]})})}),I=v.forwardRef((({className:e,variant:a="default",size:r="md",children:s,...n},l)=>t("button",{className:i("relative overflow-hidden font-mono uppercase tracking-wider transition-all duration-300","hover:shadow-[0_0_15px_rgba(0,255,140,0.5)] focus:outline-none focus:ring-2 focus:ring-green-bright/50",{"bg-black border border-green-muted text-green-bright hover:bg-green-dark hover:text-white":"default"===a,"bg-transparent border border-green-muted/50 text-white/80 hover:text-green-bright":"outline"===a,"px-2 py-1.5 text-[10px]":"sm"===r,"px-3 py-2 text-xs":"md"===r,"px-4 py-2.5 text-sm":"lg"===r},e),ref:l,...n,children:s})));I.displayName="CyberButton";const B=()=>{const[a,i]=k([{content:"Can you explain what's wrong with my DMARC record?",sender:"user"},{content:"I found an issue with your DMARC record. It's currently set to `p=none`, which means:\n\nYour domain is only monitoring for email authentication but not taking any action when someone impersonates your domain.\n\nThis leaves you vulnerable to email spoofing - when scammers send emails that appear to come from your domain.\n\nI recommend upgrading to `p=quarantine` to tell email providers to send suspicious emails to the spam folder instead.",sender:"ai"},{content:"How do I fix this?",sender:"user"},{content:"Since you're using Microsoft 365, here's how to fix it:\n\n1. Log into the Microsoft 365 admin portal\n2. Go to Settings → Domains\n3. Select your domain and click \"DNS records\"\n4. Find your DMARC TXT record\n5. Change `p=none` to `p=quarantine`\n6. Save your changes\n\nWould you like me to guide you through each step with screenshots?",sender:"ai"}]),[s,n]=k(""),[l,d]=k(!1),o={"yes please":"Great, I'll prepare a step-by-step guide with screenshots for you. Check your email in the next few minutes!","what is dmarc?":"DMARC (Domain-based Message Authentication, Reporting & Conformance) is an email authentication protocol that helps protect your domain from email spoofing and phishing attacks. It works with SPF and DKIM to verify that emails claiming to be from your domain are actually authorized by you.","what's spf?":"SPF (Sender Policy Framework) is an email authentication method that specifies which mail servers are allowed to send email on behalf of your domain. It helps prevent spammers from sending messages with forged 'From' addresses at your domain.","explain dkim":"DKIM (DomainKeys Identified Mail) adds a digital signature to your emails that verifies they actually came from your domain and weren't altered in transit. It's like a digital wax seal for your emails.","is my website secure?":"I'm specialized in email and domain security, not general website security. However, I can tell you that website security involves different protocols like HTTPS, proper CSP headers, and regular updates. Would you like me to add a website security scan to your next report?"};return t(r,{className:"py-16 md:py-20",children:t("div",{className:"container",children:e("div",{className:"max-w-4xl mx-auto",children:[e("div",{className:"text-center mb-12",children:[t(y,{animation:"fade-up",children:e("div",{className:"inline-flex items-center gap-2 mb-4 bg-green-dark/30 text-green-light px-3 py-1.5 rounded-full",children:[t(g,{className:"w-4 h-4"}),t("span",{children:"Coming Soon"})]})}),t(y,{animation:"fade-up",delay:100,children:t("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Meet Buck"})}),t(y,{animation:"fade-up",delay:200,children:t("p",{className:"text-white/80 text-lg mx-auto max-w-2xl",children:"Say hello to your new domain security sidekick. Buck is our AI-powered agent who speaks human and makes security accessible for everyone."})})]}),t(y,{animation:"fade-up",delay:300,children:e("div",{className:"bg-black-soft border border-green-muted/30 rounded-lg overflow-hidden shadow-lg",children:[e("div",{className:"border-b border-green-muted/20 p-4 flex items-center",children:[t("div",{className:"w-10 h-10 rounded-full bg-green-dark/50 text-green-bright flex items-center justify-center mr-3",children:"B"}),e("div",{children:[t("h3",{className:"font-medium",children:"Buck"}),t("div",{className:"text-xs text-white/60",children:"AI Security Assistant"})]})]}),e("div",{className:"p-5 space-y-6 max-h-[400px] overflow-y-auto",children:[a.map(((e,a)=>"user"===e.sender?t("div",{className:"flex justify-end",children:t("div",{className:"bg-green-dark/30 text-white/90 rounded-2xl rounded-tr-none px-4 py-2 max-w-xs sm:max-w-sm",children:e.content})},a):t("div",{className:"flex",children:t("div",{className:"bg-black-muted text-white/80 rounded-2xl rounded-tl-none px-4 py-2 max-w-xs sm:max-w-md whitespace-pre-line",children:e.content})},a))),l&&t("div",{className:"flex",children:t("div",{className:"bg-black-muted text-white/80 rounded-2xl rounded-tl-none px-4 py-2",children:e("span",{className:"flex space-x-1",children:[t("span",{className:"animate-pulse",children:"."}),t("span",{className:"animate-pulse",style:{animationDelay:"0.2s"},children:"."}),t("span",{className:"animate-pulse",style:{animationDelay:"0.4s"},children:"."})]})})})]}),t("div",{className:"border-t border-green-muted/20 p-4",children:t("form",{onSubmit:e=>{if(e.preventDefault(),!s.trim())return;const t={content:s,sender:"user"};i((e=>[...e,t])),n(""),d(!0),setTimeout((()=>{const e=s.toLowerCase();let t="I'm still learning about that. Can you ask me something about DMARC, SPF, or DKIM instead?";for(const[r,i]of Object.entries(o))if(e.includes(r)){t=i;break}const a={content:t,sender:"ai"};i((e=>[...e,a])),d(!1)}),1500)},className:"flex items-center",children:e("div",{className:"relative w-full",children:[t("input",{type:"text",value:s,onChange:e=>n(e.target.value),placeholder:"Try asking Buck something about domain security...",className:"w-full bg-black-muted border border-green-muted/30 rounded-full px-4 py-2 pr-28 text-white/90 focus:border-green-bright/50 focus:outline-none"}),t("div",{className:"absolute right-2 top-1/2 -translate-y-1/2",children:e(I,{type:"submit",size:"sm",disabled:!s.trim(),children:[t(x,{size:16,className:"mr-1"})," Send"]})})]})})})]})}),e("div",{className:"grid md:grid-cols-3 gap-6 md:gap-8 mt-12",children:[t(y,{animation:"fade-up",delay:400,children:e("div",{className:"bg-black-soft border border-green-muted/30 p-5 rounded-lg",children:[e("div",{className:"flex items-center mb-3",children:[t(p,{className:"w-5 h-5 text-green-bright mr-2"}),t("h3",{className:"font-medium",children:"Explains Issues"})]}),t("p",{className:"text-white/70 text-sm",children:"Buck explains technical security issues in plain, jargon-free language anyone can understand."})]})}),t(y,{animation:"fade-up",delay:500,children:e("div",{className:"bg-black-soft border border-green-muted/30 p-5 rounded-lg",children:[e("div",{className:"flex items-center mb-3",children:[t(f,{className:"w-5 h-5 text-green-bright mr-2"}),t("h3",{className:"font-medium",children:"Provider-Aware"})]}),t("p",{className:"text-white/70 text-sm",children:"Get specific instructions tailored to the exact platforms and services your organization uses."})]})}),t(y,{animation:"fade-up",delay:600,children:e("div",{className:"bg-black-soft border border-green-muted/30 p-5 rounded-lg",children:[e("div",{className:"flex items-center mb-3",children:[t(g,{className:"w-5 h-5 text-green-bright mr-2"}),t("h3",{className:"font-medium",children:"Human Language"})]}),t("p",{className:"text-white/70 text-sm",children:"Buck is perfect for busy founders, marketers, and IT leads who need straight answers fast."})]})})]})]})})})},j=()=>t(r,{background:"grid",className:"py-20 md:py-24",children:e("div",{className:"container",children:[t(y,{animation:"fade-up",children:e("div",{className:"max-w-4xl mx-auto text-center mb-10 md:mb-12",children:[e("div",{className:"inline-flex items-center gap-2 mb-6",children:[t(l,{className:"w-6 h-6 text-green-bright"}),t("span",{className:"text-lg font-mono uppercase tracking-wider text-green-bright",children:"BlackVault Lite"})]}),t("h2",{className:"text-3xl md:text-4xl lg:text-5xl font-bold mb-6",children:"Ready to Secure Your Domain?"}),t("p",{className:"text-white/80 text-lg mb-8 max-w-2xl mx-auto",children:"Shut the front door on email threats. Get started with BlackVault Lite today and see what your domain is really exposing."}),t(N.a,{href:"#signup",className:"cyber-button text-base px-10 py-4 inline-block",whileHover:{scale:1.03},whileTap:{scale:.97},children:"Get Started Now"})]})}),t(y,{animation:"fade-up",delay:200,children:e("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-3xl mx-auto",children:[e("div",{className:"bg-black-soft/80 border border-green-muted/30 rounded-xl p-6 flex items-start shadow-lg transition-transform duration-200 hover:scale-[1.03]",children:[t(b,{className:"w-5 h-5 text-green-bright mr-3 flex-shrink-0 mt-0.5"}),e("div",{children:[t("h3",{className:"font-semibold mb-1 text-lg",children:"Provider-aware precision"}),t("p",{className:"text-white/70 text-sm",children:"Tailored to your exact tech stack"})]})]}),e("div",{className:"bg-black-soft/80 border border-green-muted/30 rounded-xl p-6 flex items-start shadow-lg transition-transform duration-200 hover:scale-[1.03]",children:[t(b,{className:"w-5 h-5 text-green-bright mr-3 flex-shrink-0 mt-0.5"}),e("div",{children:[t("h3",{className:"font-semibold mb-1 text-lg",children:"Layman-friendly insights"}),t("p",{className:"text-white/70 text-sm",children:"No technical expertise required"})]})]}),e("div",{className:"bg-black-soft/80 border border-green-muted/30 rounded-xl p-6 flex items-start shadow-lg transition-transform duration-200 hover:scale-[1.03]",children:[t(b,{className:"w-5 h-5 text-green-bright mr-3 flex-shrink-0 mt-0.5"}),e("div",{children:[t("h3",{className:"font-semibold mb-1 text-lg",children:"Automated protection"}),t("p",{className:"text-white/70 text-sm",children:"Works continuously in the background"})]})]})]})})]})}),P=()=>e(a,{children:[t(s,{title:"BlackVault Lite - Modern Domain Security, Automated",description:"BlackVault Lite is a sleek, automated platform that helps you secure your domain and email systems in seconds. Get instant insights, actionable advice, and zero guesswork.",keywords:"email security, domain security, DMARC, SPF, DKIM, DNSSEC, cybersecurity, email authentication, phishing protection"}),e("div",{className:"mx-auto my-8 max-w-3xl rounded-xl border-2 border-indigo-500 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-500 p-6 shadow-xl flex items-center gap-4",children:[t("span",{className:"flex-shrink-0",children:e("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none","aria-hidden":"true",children:[t("circle",{cx:"24",cy:"24",r:"24",fill:"#5865F2"}),t("path",{d:"M34.2 17.2a1.2 1.2 0 0 0-.7-.6A19.7 19.7 0 0 0 29.7 16a1 1 0 0 0-.5.1c-.2.1-.3.3-.3.5a13.7 13.7 0 0 0-.3 1.3c-2.2-.3-4.4-.3-6.6 0a13.7 13.7 0 0 0-.3-1.3c0-.2-.1-.4-.3-.5a1 1 0 0 0-.5-.1c-1.6.2-3.1.6-4.6 1.2a1.2 1.2 0 0 0-.7.6c-2.1 3.2-2.7 6.3-2.4 9.3 0 .*******.5a20.2 20.2 0 0 0 6.1 2.2c.2 0 .4-.1.5-.3l.9-1.3c-1.5-.4-2.8-1-4-1.8-.1-.1 0-.2 0-.3l.3-.5c0-.1.2-.2.3-.1 8.3 3.8 10.8 0 10.9 0 .1-.1.2 0 .3.1l.3.5c0 .1 0 .2 0 .3-1.2.8-2.5 1.4-4 1.8l.9 1.3c.1.2.3.3.5.3a20.2 20.2 0 0 0 6.1-2.2c.2-.1.4-.3.4-.5.4-3-.3-6.1-2.4-9.3ZM19.2 25.7c-.9 0-1.6-.8-1.6-1.7 0-.9.7-1.7 1.6-1.7.9 0 1.6.8 1.6 1.7 0 .9-.7 1.7-1.6 1.7Zm9.6 0c-.9 0-1.6-.8-1.6-1.7 0-.9.7-1.7 1.6-1.7.9 0 1.6.8 1.6 1.7 0 .9-.7 1.7-1.6 1.7Z",fill:"#fff"})]})}),e("div",{className:"flex-1",children:[t("h2",{className:"text-white text-xl font-bold mb-1 drop-shadow",children:"Become Part of Our Security Community on Discord—Collaborate and Influence Blackvault's Future"}),t("p",{className:"text-white/90 mb-2 drop-shadow-sm",children:"Help us improve by joining the conversation and letting us know your thoughts."}),t("a",{href:"https://discord.gg/MQ4B9jT4hU",target:"_blank",rel:"noopener noreferrer",className:"inline-block rounded-lg bg-white px-5 py-2 font-semibold text-indigo-700 shadow hover:bg-indigo-100 transition",children:"Join & Connect"})]})]}),e("div",{className:"mx-auto my-8 max-w-3xl rounded-xl border-2 border-green-600 bg-gradient-to-r from-green-600 via-green-500 to-green-400 p-6 shadow-xl flex items-center gap-4",children:[t("span",{className:"flex-shrink-0",children:e("svg",{width:"48",height:"48",viewBox:"0 0 48 48",fill:"none","aria-hidden":"true",children:[t("circle",{cx:"24",cy:"24",r:"24",fill:"#059669"}),t("path",{d:"M24 14l7 7-7 7-7-7 7-7zm0 18a9 9 0 1 0 0-18 9 9 0 0 0 0 18z",fill:"#fff"})]})}),e("div",{className:"flex-1",children:[t("h2",{className:"text-white text-xl font-bold mb-1 drop-shadow",children:"BlackVault Lite is Live!"}),t("p",{className:"text-white/90 mb-2 drop-shadow-sm",children:"Automated domain security for people who have better things to do. Scan your domain in seconds and get protected today."}),t("a",{href:"https://blackvault.co.nz",target:"_blank",rel:"noopener noreferrer",className:"inline-block rounded-lg bg-white px-5 py-2 font-semibold text-green-700 shadow hover:bg-green-100 transition",children:"Visit BlackVault"})]})]}),t(S,{}),t(C,{}),t(M,{}),t(A,{}),t(B,{}),t(n,{}),t(j,{})]});export{P as default};
