import{jsx as e,jsxs as t}from"react/jsx-runtime";import{use<PERSON>arams as a,Link as r}from"react-router-dom";import{u as s,C as i,a as l,b as n,P as o,S as m}from"../entry-server.js";import{useState as c}from"react";import{I as d}from"./input-Bha7rE9x.js";import{L as u}from"./label-BdpnMMwV.js";import{AlertCircle as h,ArrowLeft as p,Building2 as b,Shield as x}from"lucide-react";import{u as g,a as v,A as N,b as y}from"./use-assessment-by-slug-DxKEk9qA.js";import"react-helmet";import"react-dom/server";import"react-router-dom/server.mjs";import"framer-motion";import"@supabase/supabase-js";import"@radix-ui/react-slot";import"class-variance-authority";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";import"./security-D6XyL6Yo.js";import"@radix-ui/react-label";import"@radix-ui/react-progress";const f=()=>{const{slug:o}=a(),{assessmentType:m,questions:x,loading:f,error:w}=g(o||""),{createSubmission:k,isSubmitting:C,submissionId:S}=v(m?.id||""),{toast:q}=s(),[j,A]=c("info"),[F,T]=c({companyName:"",industry:"",employeeCount:"",contactName:"",email:"",phone:""}),E=()=>{A("results")},Y=(e,t)=>{T((a=>({...a,[e]:t})))};return e("div",f?{className:"max-w-2xl mx-auto",children:e(i,{className:"cyber-gradient-card border border-green-muted/30",children:e(l,{className:"p-8 text-center",children:t("div",{className:"animate-pulse",children:[e("div",{className:"h-8 bg-green-muted/20 rounded w-3/4 mx-auto mb-4"}),e("div",{className:"h-4 bg-green-muted/20 rounded w-full mb-2"}),e("div",{className:"h-4 bg-green-muted/20 rounded w-2/3 mx-auto"})]})})})}:w||!m?{className:"max-w-2xl mx-auto",children:e(i,{className:"cyber-gradient-card border border-red-500/30",children:t(l,{className:"p-8 text-center",children:[e(h,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),e("h3",{className:"text-xl font-semibold text-white mb-2",children:"Assessment Not Available"}),e("p",{className:"text-red-400 mb-6",children:w||"The requested assessment could not be found."}),e(n,{asChild:!0,children:t(r,{to:"/assessments",children:[e(p,{className:"w-4 h-4 mr-2"}),"Back to Assessment Library"]})})]})})}:"questions"===j?{className:"max-w-4xl mx-auto",children:e(N,{questions:x,onComplete:E,assessmentType:m})}:"results"===j?{className:"max-w-4xl mx-auto",children:e(y,{submissionId:S,onStartNew:()=>A("info")})}:{className:"max-w-2xl mx-auto",children:e(i,{className:"cyber-gradient-card border border-green-muted/30",children:t(l,{className:"p-8",children:[t("div",{className:"text-center mb-8",children:[e(b,{className:"h-12 w-12 text-green-bright mx-auto mb-4"}),e("h2",{className:"text-2xl font-bold mb-2",children:m.title}),e("p",{className:"text-white/70 mb-4",children:m.description}),t("div",{className:"inline-flex items-center gap-2 bg-green-dark/30 px-3 py-1 rounded-full text-sm text-green-bright",children:["Estimated time: ",m.estimated_time_minutes," minutes"]})]}),t("form",{onSubmit:async e=>{e.preventDefault();try{await k(F);A("questions"),q({title:"Assessment Started",description:`Your ${m?.title} has been started. Let's continue with the questions.`})}catch(t){q({title:"Error",description:"Failed to start assessment. Please try again.",variant:"destructive"})}},className:"space-y-6",children:[t("div",{className:"grid md:grid-cols-2 gap-6",children:[t("div",{children:[e(u,{htmlFor:"companyName",className:"text-white mb-2 block",children:"Company Name *"}),e(d,{id:"companyName",type:"text",required:!0,value:F.companyName,onChange:e=>Y("companyName",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"Your company name"})]}),t("div",{children:[e(u,{htmlFor:"industry",className:"text-white mb-2 block",children:"Industry *"}),t("select",{id:"industry",required:!0,value:F.industry,onChange:e=>Y("industry",e.target.value),className:"w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white",children:[e("option",{value:"",children:"Select your industry"}),e("option",{value:"healthcare",children:"Healthcare"}),e("option",{value:"finance",children:"Financial Services"}),e("option",{value:"education",children:"Education"}),e("option",{value:"government",children:"Government"}),e("option",{value:"manufacturing",children:"Manufacturing"}),e("option",{value:"retail",children:"Retail"}),e("option",{value:"technology",children:"Technology"}),e("option",{value:"professional-services",children:"Professional Services"}),e("option",{value:"other",children:"Other"})]})]})]}),t("div",{children:[e(u,{htmlFor:"employeeCount",className:"text-white mb-2 block",children:"Number of Employees *"}),t("select",{id:"employeeCount",required:!0,value:F.employeeCount,onChange:e=>Y("employeeCount",e.target.value),className:"w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white",children:[e("option",{value:"",children:"Select employee count"}),e("option",{value:"1-10",children:"1-10 employees"}),e("option",{value:"11-50",children:"11-50 employees"}),e("option",{value:"51-200",children:"51-200 employees"}),e("option",{value:"201-500",children:"201-500 employees"}),e("option",{value:"500+",children:"500+ employees"})]})]}),t("div",{className:"grid md:grid-cols-2 gap-6",children:[t("div",{children:[e(u,{htmlFor:"contactName",className:"text-white mb-2 block",children:"Your Name *"}),e(d,{id:"contactName",type:"text",required:!0,value:F.contactName,onChange:e=>Y("contactName",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"Your full name"})]}),t("div",{children:[e(u,{htmlFor:"phone",className:"text-white mb-2 block",children:"Phone Number"}),e(d,{id:"phone",type:"tel",value:F.phone,onChange:e=>Y("phone",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"Your phone number"})]})]}),t("div",{children:[e(u,{htmlFor:"email",className:"text-white mb-2 block",children:"Email Address *"}),e(d,{id:"email",type:"email",required:!0,value:F.email,onChange:e=>Y("email",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"<EMAIL>"})]}),e(n,{type:"submit",disabled:C,className:"w-full bg-green-bright hover:bg-green-muted text-black font-semibold py-3",children:C?"Starting Assessment...":`Start ${m.title}`})]})]})})})},w=()=>{const{slug:r}=a();return r?t("div",{className:"min-h-screen bg-black",children:[e(o,{title:"Security Assessment | BlackVeil",description:"Take a comprehensive cybersecurity assessment to identify vulnerabilities and improve your security posture.",canonicalUrl:`https://blackveil.co.nz/assessment/${r}`}),e(m,{className:"pt-16 pb-20",children:e("div",{className:"max-w-4xl mx-auto",children:e(f,{})})})]}):e("div",{className:"min-h-screen bg-black flex items-center justify-center",children:t("div",{className:"text-center",children:[e(x,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),e("h1",{className:"text-2xl font-bold text-white mb-2",children:"Assessment Not Found"}),e("p",{className:"text-white/70",children:"The requested assessment could not be found."})]})})};export{w as default};
