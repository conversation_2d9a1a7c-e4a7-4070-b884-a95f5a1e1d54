import{jsx as e,jsxs as r,Fragment as t}from"react/jsx-runtime";import{H as a,U as s,h as n,s as i,u as o,b as c,P as l,B as m,S as d}from"../entry-server.js";import{Calendar as u,Mail as h,Linkedin as f,ShieldAlert as p,Loader2 as g,Send as b,ExternalLink as v,FileText as y}from"lucide-react";import*as x from"react";import w,{useState as N,useEffect as k,useRef as S}from"react";import{Slot as F}from"@radix-ui/react-slot";import{useFormContext as C,FormProvider as E,Controller as I,useForm as _}from"react-hook-form";import{L as j}from"./label-BdpnMMwV.js";import{cva as B}from"class-variance-authority";import{zodResolver as P}from"@hookform/resolvers/zod";import*as R from"zod";import{i as T,s as L}from"./security-D6XyL6Yo.js";import{I as z}from"./input-Bha7rE9x.js";import{T as M}from"./textarea-CpkNdKXR.js";import"react-helmet";import"react-dom/server";import"react-router-dom/server.mjs";import"react-router-dom";import"framer-motion";import"@supabase/supabase-js";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";import"@radix-ui/react-label";const D=()=>e(a,{title:"Contact Us",description:"Get in touch with our team to discuss your cybersecurity needs, arrange a consultation, or learn more about our services."}),V=()=>r("div",{className:"cyber-card",children:[e("h2",{className:"text-2xl font-bold mb-6",children:"Get in Touch"}),e("p",{className:"text-white/70 mb-8",children:"Have questions about our cybersecurity services? Want to discuss how we can help protect your business? Our team is ready to provide the security expertise your organization needs."}),r("div",{className:"space-y-6",children:[r("div",{className:"flex items-start",children:[e(u,{className:"h-6 w-6 mr-4 text-green-muted"}),r("div",{children:[e("h3",{className:"font-bold mb-1",children:"Schedule a Consultation"}),e("p",{className:"text-white/70 mb-3",children:"Book a 30-minute call with our security experts"}),e(s,{variant:"contact",layout:"horizontal",showDmarc:!1,showEmergency:!1})]})]}),r("div",{className:"flex items-start",children:[e(h,{className:"h-6 w-6 mr-4 text-green-muted"}),r("div",{children:[e("h3",{className:"font-bold mb-1",children:"Connect With Us"}),r("p",{className:"text-white/70",children:["Email us at ",e("a",{href:"mailto:<EMAIL>",className:"text-green-bright hover:text-green-bright/80 transition-colors",children:"<EMAIL>"})]})]})]}),r("div",{className:"flex items-start",children:[e(f,{className:"h-6 w-6 mr-4 text-green-muted"}),r("div",{children:[e("h3",{className:"font-bold mb-1",children:"Follow Us"}),e("a",{href:"https://www.linkedin.com/company/blackveil-limited",target:"_blank",rel:"noopener noreferrer",className:"text-green-bright hover:text-green-bright/80 transition-colors",children:"Follow BlackVeil on LinkedIn"})]})]}),r("div",{className:"cyber-card-highlight mt-8 p-4",children:[e("h3",{className:"font-bold mb-2",children:"Remote Security Solutions"}),e("p",{className:"text-white/70",children:"BlackVeil provides enterprise-grade cybersecurity services remotely to businesses worldwide."})]})]})]}),A=E,U=x.createContext({}),$=({...r})=>e(U.Provider,{value:{name:r.name},children:e(I,{...r})}),W=()=>{const e=x.useContext(U),r=x.useContext(H),{getFieldState:t,formState:a}=C(),s=t(e.name,a);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:n}=r;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...s}},H=x.createContext({}),Y=x.forwardRef((({className:r,...t},a)=>{const s=x.useId();return e(H.Provider,{value:{id:s},children:e("div",{ref:a,className:n("space-y-2",r),...t})})}));Y.displayName="FormItem";const G=x.forwardRef((({className:r,...t},a)=>{const{error:s,formItemId:i}=W();return e(j,{ref:a,className:n(s&&"text-destructive",r),htmlFor:i,...t})}));G.displayName="FormLabel";const O=x.forwardRef((({...r},t)=>{const{error:a,formItemId:s,formDescriptionId:n,formMessageId:i}=W();return e(F,{ref:t,id:s,"aria-describedby":a?`${n} ${i}`:`${n}`,"aria-invalid":!!a,...r})}));O.displayName="FormControl";x.forwardRef((({className:r,...t},a)=>{const{formDescriptionId:s}=W();return e("p",{ref:a,id:s,className:n("text-sm text-muted-foreground",r),...t})})).displayName="FormDescription";const q=x.forwardRef((({className:r,children:t,...a},s)=>{const{error:i,formMessageId:o}=W(),c=i?String(i?.message).replace(/[<>&"']/g,(e=>{switch(e){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"'":return"&#39;";default:return e}})):t;return c?e("p",{ref:s,id:o,className:n("text-sm font-medium text-destructive",r),...a,children:c}):null}));q.displayName="FormMessage";const X=B("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),Z=x.forwardRef((({className:r,variant:t,...a},s)=>e("div",{ref:s,role:"alert",className:n(X({variant:t}),r),...a})));Z.displayName="Alert";const J=x.forwardRef((({className:r,...t},a)=>e("h5",{ref:a,className:n("mb-1 font-medium leading-none tracking-tight",r),...t})));J.displayName="AlertTitle";const K=x.forwardRef((({className:r,...t},a)=>e("div",{ref:a,className:n("text-sm [&_p]:leading-relaxed",r),...t})));K.displayName="AlertDescription";const Q="X-CSRF-Token";async function ee(){try{const{data:e,error:r}=await i.functions.invoke("csrf-token");if(r)throw new Error("CSRF token generation failed");return e.token}catch(e){throw new Error("CSRF token generation failed")}}function re(){const[e,r]=w.useState(null),[t,a]=w.useState(!0);w.useEffect((()=>{(async()=>{try{const e=await ee();r(e)}catch(e){}finally{a(!1)}})()}),[]);const s=w.useCallback((async e=>await async function(e){try{const{data:r,error:t}=await i.functions.invoke("validate-csrf",{headers:{[Q]:e}});return!t&&!0===r.valid}catch(r){return!1}}(e)),[]);return{csrfToken:e,isLoading:t,validateFormSubmission:s,refreshToken:w.useCallback((async()=>{try{a(!0);const e=await ee();return r(e),e}catch(e){return null}finally{a(!1)}}),[])}}const te=void 0,ae=R.object({name:R.string().min(2,{message:"Name must be at least 2 characters."}).max(100,{message:"Name must not exceed 100 characters."}).refine((e=>!/[<>]/.test(e)),{message:"Name contains invalid characters."}),email:R.string().email({message:"Please enter a valid email address."}).max(255,{message:"Email address is too long."}).refine((e=>T(e)),{message:"Please enter a valid email format."}),company:R.string().max(100,{message:"Company name must not exceed 100 characters."}).refine((e=>!e||!/[<>]/.test(e)),{message:"Company name contains invalid characters."}).optional(),message:R.string().min(10,{message:"Message must be at least 10 characters."}).max(1e3,{message:"Message must not exceed 1000 characters."}).refine((e=>!/[<>]/.test(e)),{message:"Message contains invalid characters."}),_csrf:R.string(),access_key:R.string().optional(),subject:R.string().optional(),from_name:R.string().optional()});const se=({form:a,onEasterEggTrigger:s})=>{const n=e=>"speculative execution"===e.trim().toLowerCase();return r(t,{children:[e("input",{type:"hidden",name:"_csrf",...a.register("_csrf")}),e("input",{type:"hidden",name:"access_key",...a.register("access_key")}),e("input",{type:"hidden",name:"subject",...a.register("subject")}),e("input",{type:"hidden",name:"from_name",...a.register("from_name")}),e("div",{className:"hidden",children:e("input",{type:"checkbox",name:"botcheck",id:"botcheck",className:"hidden"})}),e($,{control:a.control,name:"name",render:({field:t})=>{const{onBlur:a,value:i,...o}=t;return r(Y,{children:[e(G,{children:"Name"}),e(O,{children:e(z,{placeholder:"Your name",className:"cyber-input",maxLength:100,value:i,onBlur:e=>{a&&a(),s&&n(e.target.value)&&s()},...o})}),e(q,{})]})}}),e($,{control:a.control,name:"email",render:({field:t})=>{const{onBlur:a,value:i,...o}=t;return r(Y,{children:[e(G,{children:"Email"}),e(O,{children:e(z,{type:"email",placeholder:"<EMAIL>",className:"cyber-input",maxLength:255,value:i,onBlur:e=>{a&&a(),s&&n(e.target.value)&&s()},...o})}),e(q,{})]})}}),e($,{control:a.control,name:"company",render:({field:t})=>{const{onBlur:a,value:i,...o}=t;return r(Y,{children:[r(G,{children:["Company ",e("span",{className:"text-gray-400",children:"(Optional)"})]}),e(O,{children:e(z,{placeholder:"Your company",className:"cyber-input",maxLength:100,value:i,onBlur:e=>{a&&a(),s&&n(e.target.value)&&s()},...o})}),e(q,{})]})}}),e($,{control:a.control,name:"message",render:({field:t})=>{const{onBlur:a,value:i,...o}=t;return r(Y,{children:[e(G,{children:"Message"}),e(O,{children:e(M,{placeholder:"How can we help you?",className:"cyber-input min-h-32",maxLength:1e3,value:i,onBlur:e=>{a&&a(),s&&n(e.target.value)&&s()},...o})}),e(q,{})]})}})]})},ne=()=>{const{form:a,isSubmitting:s,formError:n,onSubmit:i}=function(){const[e,r]=N(!1),[t,a]=N(null),{toast:s}=o(),{csrfToken:n,validateFormSubmission:i}=re(),c=_({resolver:P(ae),defaultValues:{name:"",email:"",company:"",message:"",_csrf:n,access_key:te,subject:"New message from BlackVeil website",from_name:"BlackVeil Contact Form"}});return k((()=>{c.setValue("_csrf",n)}),[n,c]),{form:c,isSubmitting:e,formError:t,onSubmit:c.handleSubmit((async e=>{r(!0),a(null);try{if(!i(e._csrf))throw new Error("Security validation failed. Please refresh the page and try again.");throw L(e.name),L(e.email),e.company&&L(e.company),L(e.message),new Error("Web3Forms configuration is missing. Please check environment variables.")}catch(t){let e="An unexpected error occurred. Please try again later.";t instanceof Error&&(t.message,e=t.message.includes("Security validation failed")?"Security check failed. Please refresh the page and try again.":t.message.includes("Web3Forms configuration is missing")?"Form configuration error. Please contact support.":t.message.includes("Failed to submit the form")?t.message||"Failed to send message. Please check your details and try again.":!1===navigator.onLine?"Network connection error. Please check your internet connection.":"There was a problem sending your message. Please try again."),a(e),s({title:"Submission Failed",description:e,variant:"destructive"})}finally{r(!1)}}))}}(),[l,m]=N(!1),[d,u]=N(!1),h=S(null),f=()=>{d&&u(!1)};return w.useEffect((()=>()=>{h.current&&clearTimeout(h.current)}),[]),r("div",{className:"cyber-card relative overflow-visible",children:[e("h2",{className:"text-2xl font-bold mb-6",children:"Send a Message"}),n&&r(Z,{variant:"destructive",className:"mb-6",children:[e(p,{className:"h-4 w-4"}),e(J,{children:"Error"}),e(K,{children:n})]}),e(A,{...a,children:r("form",{onSubmit:i,className:"space-y-4 "+(l?"spectre-glitch":""),autoComplete:"off",onFocus:f,onInput:f,children:[e(se,{form:a,onEasterEggTrigger:()=>{l||d||(m(!0),h.current=setTimeout((()=>{m(!1),u(!0)}),400))}}),e(c,{type:"submit",className:"cyber-button w-full flex items-center justify-center",disabled:s,children:r(t,s?{children:[e(g,{className:"h-4 w-4 mr-2 animate-spin"}),"Sending..."]}:{children:[e(b,{className:"h-4 w-4 mr-2"}),"Schedule Your Free Consultation"]})})]})}),d&&r("div",{className:"spectre-tooltip",children:["Careful, your secrets might leak!"," ",e("a",{href:"https://example.com/spectre-meltdown-writeup",target:"_blank",rel:"noopener noreferrer",className:"underline text-blue-400",children:"Learn more"})]}),l&&e("div",{className:"spectre-glitch-overlay pointer-events-none"})]})},ie=()=>r("div",{children:[e(l,{title:"Contact Us | BlackVeil",description:"Let's Strengthen Your Cybersecurity Together—Connect With Our Experts Now",canonicalUrl:"https://blackveil.co.nz/contact",keywords:"contact, cybersecurity consultation, security experts, New Zealand, email security"}),e(m,{items:[{name:"Home",url:"https://blackveil.co.nz/"},{name:"Contact",url:"https://blackveil.co.nz/contact"}]}),e(D,{}),r(d,{className:"pb-6 xs:pb-8 sm:pb-10",children:[e("div",{className:"max-w-4xl mx-auto mb-12 p-5 xs:p-6 sm:p-8 rounded-lg border border-green-muted/30 bg-gradient-to-br from-black-soft to-green-dark/10",children:r("div",{className:"flex flex-col md:flex-row gap-6 items-center",children:[r("div",{className:"md:w-3/4",children:[e("h2",{className:"text-xl xs:text-2xl font-bold mb-3 cyber-glow-text",children:"Cyber Incident Response Guide"}),e("p",{className:"text-white/80 mb-4",children:"Download our comprehensive guide to help your business prepare for and respond to cyber security incidents effectively."}),r("div",{className:"flex flex-wrap gap-3",children:[e(s,{variant:"contact",layout:"horizontal",showDmarc:!1,showEmergency:!1}),r("a",{href:"https://alexanderpr.co.nz/blog/contribution-emergency-cyber-security-response-guide-for-businesses",target:"_blank",rel:"noopener noreferrer",className:"text-white/80 hover:text-green-bright flex items-center transition-colors px-4 py-2",children:[e("span",{children:"As featured on Alexander PR"}),e(v,{className:"ml-2 w-4 h-4"})]})]})]}),e("div",{className:"md:w-1/4 flex justify-center",children:e("div",{className:"w-20 h-20 sm:w-24 sm:h-24 bg-black-soft rounded-full flex items-center justify-center border border-green-muted/30",children:e(y,{className:"w-10 h-10 sm:w-12 sm:h-12 text-green-bright"})})})]})}),r("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8 xs:gap-10 sm:gap-12 max-w-5xl mx-auto",children:[e(V,{}),e(ne,{})]})]})]});export{ie as default};
