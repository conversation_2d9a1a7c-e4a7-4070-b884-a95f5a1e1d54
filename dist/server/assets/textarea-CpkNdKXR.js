import{jsx as e}from"react/jsx-runtime";import*as r from"react";import{s as o}from"./security-D6XyL6Yo.js";import{h as s}from"../entry-server.js";const a=r.forwardRef((({className:r,sanitize:a=!0,onChange:t,...i},n)=>e("textarea",{className:s("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:n,onChange:e=>{if(a){const r=e.target.value,s=o(r);r!==s&&(e.target.value=s)}t&&t(e)},...i})));a.displayName="Textarea";export{a as T};
