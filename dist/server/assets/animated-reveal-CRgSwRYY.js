import{jsx as a}from"react/jsx-runtime";import{j as t,h as e}from"../entry-server.js";const r=({children:r,animation:o="fade-up",delay:s=0,className:i,threshold:n,rootMargin:l})=>{const{ref:c,isVisible:y}=t({threshold:n,rootMargin:l,delay:s});return a("div",{ref:c,className:e("transform-gpu transition-all duration-700 ease-out",y?"translate-y-0 translate-x-0 scale-100 opacity-100 rotateX-0":{"fade-up":"translate-y-10 opacity-0","fade-down":"-translate-y-10 opacity-0","fade-left":"translate-x-10 opacity-0","fade-right":"-translate-x-10 opacity-0",zoom:"scale-95 opacity-0",flip:"rotateX-90 opacity-0"}[o],i),style:{transitionDelay:`${s}ms`},children:r})};export{r as A};
