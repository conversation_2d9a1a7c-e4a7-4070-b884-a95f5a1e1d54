import{jsx as e,jsxs as t}from"react/jsx-runtime";import{useLocation as a,useNavigate as r,Link as l}from"react-router-dom";import{useState as s,useEffect as i}from"react";import{AlertTriangle as n,<PERSON><PERSON>ef<PERSON> as o}from"lucide-react";import{b as c}from"../entry-server.js";import{g as m,f as d,e as h,d as f,c as p,b as x,a as b,p as g}from"./post-8-DXCQdmT5.js";import"react-dom/server";import"react-router-dom/server.mjs";import"framer-motion";import"@supabase/supabase-js";import"@radix-ui/react-slot";import"class-variance-authority";import"clsx";import"tailwind-merge";import"@radix-ui/react-toast";import"@radix-ui/react-dialog";import"react-helmet";import"@radix-ui/react-accordion";import"@radix-ui/react-tabs";const u=[m,d,h,f,p,x,b,g],v=()=>{const m=a(),d=r(),[h,f]=s(null),[p,x]=s(!1),b=(()=>{const e=m.pathname.replace(/\/+/g,"/").toLowerCase();return"/../../blackvault"===e||"/..%2f..%2fblackvault"===e||"/..%2F..%2Fblackvault"===e||"/..//..//blackvault"===e})();return i((()=>{if(!b&&m.pathname.startsWith("/blog/")){const e=m.pathname.split("/blog/")[1];if(isNaN(parseInt(e))||!e)f({type:"blog list",path:"/blog"});else{const t=parseInt(e);u.some((e=>e.id===t))||f({type:"blog list",path:"/blog"})}}}),[m.pathname,b]),e("div",b?{className:"min-h-screen flex items-center justify-center bg-gradient-to-b from-black via-zinc-900 to-black px-4 py-8",children:t("div",{className:"cyber-card max-w-lg w-full text-center p-6 sm:p-10 relative overflow-hidden",children:[e("h1",{className:"text-2xl sm:text-3xl font-bold mb-2 text-green-bright animate-pulse",children:"You found the rabbit hole!"}),e("p",{className:"text-white/80 mb-6",children:"Welcome to the Blackvault. Explore the file tree below..."}),e(y,{showFlag:p,setShowFlag:x}),e("style",{children:"\n            .filetree-branch {\n              stroke: #4ade80;\n              stroke-width: 2;\n              stroke-dasharray: 6 4;\n              animation: dashmove 2s linear infinite;\n            }\n            @keyframes dashmove {\n              to { stroke-dashoffset: 20; }\n            }\n            .filetree-folder, .filetree-file {\n              transition: color 0.2s;\n              cursor: pointer;\n              user-select: none;\n            }\n            .filetree-file:hover {\n              color: #facc15;\n              text-shadow: 0 0 8px #facc15;\n            }\n            .flag-reveal {\n              background: #18181b;\n              border: 1px solid #4ade80;\n              color: #facc15;\n              font-family: monospace;\n              padding: 1rem 1.5rem;\n              border-radius: 0.5rem;\n              margin-top: 1.5rem;\n              word-break: break-all;\n              animation: fadein 0.7s;\n            }\n            @keyframes fadein {\n              from { opacity: 0; transform: translateY(20px);}\n              to { opacity: 1; transform: translateY(0);}\n            }\n          "})]})}:{className:"min-h-screen flex items-center justify-center bg-black-soft px-4 py-8",children:t("div",{className:"cyber-card max-w-md w-full text-center p-4 xs:p-6 sm:p-8",children:[e("div",{className:"flex justify-center mb-4 sm:mb-6",children:e(n,{className:"h-12 w-12 sm:h-16 sm:w-16 text-green-bright"})}),e("h1",{className:"text-3xl sm:text-4xl font-bold mb-2 sm:mb-4",children:"404"}),e("p",{className:"text-lg sm:text-xl text-white/70 mb-4 sm:mb-6",children:"Oops! Page not found"}),h&&t("div",{className:"mb-6 text-sm sm:text-base text-white/70 p-3 border border-white/10 rounded bg-black-muted",children:[t("p",{children:["It seems you're looking for a ",h.type," that doesn't exist."]}),t(c,{variant:"link",className:"text-green-bright mt-1",onClick:()=>d(h.path),children:[e(o,{className:"h-4 w-4 mr-1"}),"View all available ","blog list"===h.type?"blog posts":"content"]})]}),e("p",{className:"text-sm sm:text-base text-white/60 mb-6 sm:mb-8",children:"The page you are looking for might have been removed, had its name changed, or is temporarily unavailable."}),t("div",{className:"flex flex-col xs:flex-row gap-3 justify-center",children:[t(c,{variant:"outline",onClick:()=>d(-1),className:"order-2 xs:order-1",children:[e(o,{className:"h-4 w-4 mr-2"}),"Go Back"]}),e(l,{to:"/",className:"cyber-button inline-block py-2 px-4 sm:py-3 sm:px-6 text-sm sm:text-base order-1 xs:order-2",children:"Return Home"})]})]})})},y=({showFlag:a,setShowFlag:r})=>t("div",{className:"flex flex-col items-center mt-2",children:[t("svg",{width:"220",height:"180",viewBox:"0 0 220 180",fill:"none",className:"mb-2",children:[e("path",{className:"filetree-branch",d:"M30 30 V60 H60 V90 H100"}),e("path",{className:"filetree-branch",d:"M60 60 V120 H140"}),e("path",{className:"filetree-branch",d:"M100 90 V150 H180"}),t("g",{children:[e("text",{x:"20",y:"28",className:"filetree-folder",fill:"#4ade80",fontSize:"16",children:"/blackvault"}),e("text",{x:"60",y:"58",className:"filetree-folder",fill:"#4ade80",fontSize:"15",children:"secrets/"}),e("text",{x:"100",y:"88",className:"filetree-folder",fill:"#4ade80",fontSize:"15",children:"hidden/"}),e("text",{x:"140",y:"118",className:"filetree-folder",fill:"#4ade80",fontSize:"15",children:"deep/"})]}),e("g",{children:e("text",{x:"180",y:"148",className:"filetree-file",fill:"#facc15",fontSize:"15",style:{cursor:"pointer",fontWeight:600,textDecoration:"underline"},onClick:()=>r(!0),children:"flag.txt"})})]}),t("div",{className:"text-xs text-white/60 mb-2",children:["Click ",e("span",{className:"text-yellow-400 font-mono",children:"flag.txt"})," to reveal the secret"]}),a&&e("div",{className:"flag-reveal",children:"VGhlIHJlYWwgdHJlYXN1cmUgaXMgY3VyaW9zaXR5LiB7cmFiYml0X2hvbGVfYmxhY2t2YXVsdH0="})]});export{v as default};
