import{jsx as r}from"react/jsx-runtime";import{h as e}from"../entry-server.js";const a=({variant:a="default",className:t,children:o,...d})=>r("div",{className:e("relative p-6 rounded-lg border border-green-muted/30 transition-all duration-300","hover:border-green-muted/70 hover:shadow-[0_0_15px_rgba(0,255,140,0.2)]",{"bg-black-soft/90 backdrop-blur-sm":"default"===a,"bg-gradient-to-br from-black-soft to-green-dark/10":"gradient"===a},t),...d,children:o});export{a as C};
