import{jsxs as e,jsx as t}from"react/jsx-runtime";const s=({title:s,lastUpdated:l})=>e("div",{className:"mb-8",children:[t("h1",{className:"text-3xl md:text-4xl font-bold mb-4 cyber-glow-text",children:s}),e("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[e("p",{className:"text-white/70 mb-2 sm:mb-0",children:["Last Updated: ",l]}),t("div",{className:"cyber-tag",children:"Official Document"})]}),t("div",{className:"cyber-divider mb-8"})]});export{s as L};
