import{jsx as e}from"react/jsx-runtime";import*as r from"react";import{s as t}from"./security-D6XyL6Yo.js";import{h as o}from"../entry-server.js";const i=r.forwardRef((({className:r,type:i,sanitize:s=!0,onChange:a,...n},f)=>e("input",{type:i,className:o("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:f,onChange:e=>{if(s&&("text"===i||"search"===i||"email"===i||"url"===i)){const r=e.target.value,o=t(r);r!==o&&(e.target.value=o)}a&&a(e)},...n})));i.displayName="Input";export{i as I};
