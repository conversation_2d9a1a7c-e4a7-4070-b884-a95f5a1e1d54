import{j as e,r as t,L as s,f as a}from"./vendor-DEz8SL3m.js";import{H as r,C as i,a as n,u as l,P as o,B as c}from"./index-DeIkHZyv.js";import{S as d}from"./core-ClMcqRvE.js";import"./ui-BD6Ux9Dl.js";const m=()=>e.jsx(r,{title:"About BlackVeil",description:"Learn about our journey from discovering email security vulnerabilities to creating innovative cybersecurity solutions."});const x=()=>{const[s,a]=t.useState(!1),r=t.useRef(null),l=function(e,s={}){const[a,r]=t.useState(!1),{once:i=!0,threshold:n=0,rootMargin:l="0px"}=s;return t.useEffect((()=>{const t=e.current;if(!t)return;const s=new IntersectionObserver((([e])=>{e.isIntersecting?(r(!0),i&&s&&t&&s.unobserve(t)):i||r(!1)}),{threshold:n,rootMargin:l});return s.observe(t),()=>{s&&t&&s.unobserve(t)}}),[e,i,n,l]),a}(r,{once:!0,threshold:.2});return t.useEffect((()=>{a(!0)}),[]),e.jsx(d,{className:"py-20 sm:py-24 md:py-28 relative",id:"team",children:e.jsxs("div",{ref:r,className:"max-w-5xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-12 sm:mb-16",children:[e.jsx("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light \n            px-4 py-1.5 rounded-full cyber-glow inline-block mb-4 opacity-0 "+(l?"animate-fade-in":""),children:"Our Team"}),e.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4 cyber-glow-text opacity-0 "+(l?"animate-fade-in":""),style:{animationDelay:"0.2s"},children:"Meet The Experts"}),e.jsx("div",{className:"w-0 h-1 bg-gradient-to-r from-green-bright to-transparent mx-auto rounded-full mb-6 \n            transition-all duration-1000 opacity-0 "+(l?"w-24 opacity-100":"")}),e.jsx("p",{className:"text-white/80 max-w-2xl mx-auto leading-relaxed mb-6 opacity-0 "+(l?"animate-fade-in":""),style:{animationDelay:"0.3s"},children:"Our team combines expertise in cybersecurity, software development, and compliance to deliver innovative email security solutions."})]}),e.jsx(i,{className:"cyber-gradient-card border-green-muted/40 overflow-hidden",children:e.jsxs(n,{className:"p-8",children:[e.jsxs("div",{className:"flex flex-col items-center mb-8",children:[e.jsx("div",{className:"inline-block p-4 bg-green-dark/40 rounded-full mb-6",children:e.jsx("span",{role:"img","aria-label":"team",className:"text-green-bright text-3xl",children:"👥"})}),e.jsx("h3",{className:"text-xl font-bold mb-4 text-center",children:"A Team of Security Experts"}),e.jsx("p",{className:"text-white/80 max-w-2xl mx-auto mb-4 text-center",children:"Our team combines expertise in cybersecurity, software development, and data analysis to deliver cutting-edge protection for businesses of all sizes."})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"p-5 bg-black-soft rounded-lg border border-green-muted/30 hover:border-green-muted/60 transition-all duration-300",children:[e.jsx("h4",{className:"font-bold text-green-light mb-2",children:"Research Team"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Dedicated professionals constantly monitoring emerging threats and improving detection algorithms"})]}),e.jsxs("div",{className:"p-5 bg-black-soft rounded-lg border border-green-muted/30 hover:border-green-muted/60 transition-all duration-300",children:[e.jsx("h4",{className:"font-bold text-green-light mb-2",children:"Development Team"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Expert engineers building robust and scalable security solutions"})]})]}),e.jsxs("div",{className:"mt-8 text-center",children:[e.jsx("div",{className:"w-16 h-px bg-green-muted/50 mx-auto my-6"}),e.jsx("p",{className:"text-white/60 text-sm italic",children:"Our complete team roster will be updated soon"})]})]})})]})})},u=()=>e.jsx(d,{className:"py-16 xs:py-20",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsx("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full",children:"Looking Forward"}),e.jsx("h2",{className:"text-2xl xs:text-3xl font-bold mt-5 mb-4",children:"Our Vision for the Future"}),e.jsx("p",{className:"text-white/70 max-w-2xl mx-auto mb-8",children:"We're building a future where businesses can communicate with confidence, free from the threat of email fraud and impersonation."}),e.jsxs(s,{to:"/services",className:"cyber-button inline-flex items-center gap-2",children:[e.jsx("span",{children:"Explore Our Services"}),e.jsx(a,{className:"w-4 h-4"})]})]})}),h=()=>e.jsx(d,{className:"py-12 xs:py-16",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("span",{className:"font-mono uppercase tracking-wider text-xs bg-green-dark/70 text-green-light px-4 py-1.5 rounded-full",children:"Our Journey"}),e.jsx("h2",{className:"text-2xl xs:text-3xl font-bold mt-5 mb-4",children:"The BlackVeil Story"}),e.jsx("p",{className:"text-white/70",children:"From research to revolution: How we're changing the landscape of email security."})]}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[e.jsxs("div",{className:"cyber-gradient-card p-8 rounded-lg",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"The Beginning"}),e.jsx("p",{className:"text-white/80 leading-relaxed",children:"Our journey began with a simple question: How vulnerable are New Zealand businesses to email fraud? This led us to develop specialized crawlers to analyze email security across thousands of domains."})]}),e.jsxs("div",{className:"cyber-gradient-card p-8 rounded-lg",children:[e.jsx("h3",{className:"text-xl font-bold mb-4",children:"The Discovery"}),e.jsx("p",{className:"text-white/80 leading-relaxed",children:"The results were startling. Our research revealed widespread vulnerabilities in email systems, with many businesses completely unprotected against sophisticated impersonation attacks."})]})]})]})}),b=()=>{const{toast:s}=l();t.useEffect((()=>{localStorage.getItem("visited-about")||setTimeout((()=>{s({title:"Welcome to our story",description:"Learn about our journey in email security innovation",variant:"default"}),localStorage.setItem("visited-about","true")}),1500);["/path/to/team-member-1.jpg","/path/to/team-member-2.jpg"].forEach((e=>{(new Image).src=e}))}),[s]);return e.jsxs("div",{className:"about-page",children:[e.jsx(o,{title:"About Blackveil - Our Story & Team",description:"Learn about Blackveil's journey from discovering email security vulnerabilities to creating innovative cybersecurity solutions. Featured on Firetail.ai Modern Cyber Podcast.",canonicalUrl:"https://blackveil.co.nz/about",keywords:"Blackveil, cybersecurity, email security, New Zealand, Firetail.ai, Modern Cyber Podcast, company story"}),e.jsx(c,{items:[{name:"Home",url:"https://blackveil.co.nz/"},{name:"About",url:"https://blackveil.co.nz/about"}]}),e.jsx(m,{}),e.jsx(h,{}),e.jsx(x,{}),e.jsx(u,{})]})};export{b as default};
