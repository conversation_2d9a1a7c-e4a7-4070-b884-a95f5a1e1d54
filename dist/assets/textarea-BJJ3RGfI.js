import{r as e,j as r}from"./vendor-DEz8SL3m.js";import{s as o}from"./security-D6XyL6Yo.js";import{c as s}from"./core-ClMcqRvE.js";const a=e.forwardRef((({className:e,sanitize:a=!0,onChange:i,...t},n)=>r.jsx("textarea",{className:s("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,onChange:e=>{if(a){const r=e.target.value,s=o(r);r!==s&&(e.target.value=s)}i&&i(e)},...t})));a.displayName="Textarea";export{a as T};
