import{r as e,j as t,a as n,l as r,aw as i,k as o,U as a,q as s,p as c,z as l,P as u,ax as f,T as d,m as p,d as h,K as y,e as m,R as v,C as g,ay as b,az as x,ap as w,aA as j,aB as O,S,G as P,aC as A,a1 as k,a2 as _,av as E,aD as N,V as M,a7 as T}from"./vendor-DEz8SL3m.js";import{c as C,s as D,b as I,S as R}from"./core-ClMcqRvE.js";import{u as B,C as L,c as z,d as F,a as $,b as U,o as W,p as q,q as H,P as V,e as Y,f as X,g as G,T as K}from"./index-DeIkHZyv.js";import{I as Z}from"./input-DEr6FcyC.js";import{c as Q,a as J,s as ee,u as te,A as ne,P as re,f as ie,h as oe,t as ae,v as se,w as ce,S as le,F as ue,x as fe,y as de,z as pe,E as he,G as ye,V as me,e as ve,g as ge,J as be,b as xe,K as we}from"./ui-BD6Ux9Dl.js";import{L as je}from"./label-BOElYI-h.js";import{T as Oe}from"./textarea-BJJ3RGfI.js";import"./security-D6XyL6Yo.js";function Se(e,[t,n]){return Math.min(n,Math.max(t,e))}var Pe=[" ","Enter","ArrowUp","ArrowDown"],Ae=[" ","Enter"],ke="Select",[_e,Ee,Ne]=Q(ke),[Me,Te]=J(ke,[Ne,ee]),Ce=ee(),[De,Ie]=Me(ke),[Re,Be]=Me(ke),Le=n=>{const{__scopeSelect:r,children:i,open:o,defaultOpen:a,onOpenChange:s,value:c,defaultValue:l,onValueChange:u,dir:f,name:d,autoComplete:p,disabled:h,required:y,form:m}=n,v=Ce(r),[g,b]=e.useState(null),[x,w]=e.useState(null),[j,O]=e.useState(!1),S=be(f),[P=!1,A]=xe({prop:o,defaultProp:a,onChange:s}),[k,_]=xe({prop:c,defaultProp:l,onChange:u}),E=e.useRef(null),N=!g||(m||!!g.closest("form")),[M,T]=e.useState(new Set),C=Array.from(M).map((e=>e.props.value)).join(";");return t.jsx(we,{...v,children:t.jsxs(De,{required:y,scope:r,trigger:g,onTriggerChange:b,valueNode:x,onValueNodeChange:w,valueNodeHasChildren:j,onValueNodeHasChildrenChange:O,contentId:pe(),value:k,onValueChange:_,open:P,onOpenChange:A,dir:S,triggerPointerDownPosRef:E,disabled:h,children:[t.jsx(_e.Provider,{scope:r,children:t.jsx(Re,{scope:n.__scopeSelect,onNativeOptionAdd:e.useCallback((e=>{T((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:e.useCallback((e=>{T((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:i})}),N?t.jsxs(St,{"aria-hidden":!0,required:y,tabIndex:-1,name:d,autoComplete:p,value:k,onChange:e=>_(e.target.value),disabled:h,form:m,children:[void 0===k?t.jsx("option",{value:""}):null,Array.from(M)]},C):null]})})};Le.displayName=ke;var ze="SelectTrigger",Fe=e.forwardRef(((n,r)=>{const{__scopeSelect:i,disabled:o=!1,...a}=n,s=Ce(i),c=Ie(ze,i),l=c.disabled||o,u=te(r,c.onTriggerChange),f=Ee(i),d=e.useRef("touch"),[p,h,y]=Pt((e=>{const t=f().filter((e=>!e.disabled)),n=t.find((e=>e.value===c.value)),r=At(t,e,n);void 0!==r&&c.onValueChange(r.value)})),m=e=>{l||(c.onOpenChange(!0),y()),e&&(c.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return t.jsx(ne,{asChild:!0,...s,children:t.jsx(re.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Ot(c.value)?"":void 0,...a,ref:u,onClick:ie(a.onClick,(e=>{e.currentTarget.focus(),"mouse"!==d.current&&m(e)})),onPointerDown:ie(a.onPointerDown,(e=>{d.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(m(e),e.preventDefault())})),onKeyDown:ie(a.onKeyDown,(e=>{const t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),t&&" "===e.key||Pe.includes(e.key)&&(m(),e.preventDefault())}))})})}));Fe.displayName=ze;var $e="SelectValue",Ue=e.forwardRef(((e,n)=>{const{__scopeSelect:r,className:i,style:o,children:a,placeholder:s="",...c}=e,l=Ie($e,r),{onValueNodeHasChildrenChange:u}=l,f=void 0!==a,d=te(n,l.onValueNodeChange);return oe((()=>{u(f)}),[u,f]),t.jsx(re.span,{...c,ref:d,style:{pointerEvents:"none"},children:Ot(l.value)?t.jsx(t.Fragment,{children:s}):a})}));Ue.displayName=$e;var We=e.forwardRef(((e,n)=>{const{__scopeSelect:r,children:i,...o}=e;return t.jsx(re.span,{"aria-hidden":!0,...o,ref:n,children:i||"▼"})}));We.displayName="SelectIcon";var qe=e=>t.jsx(ge,{asChild:!0,...e});qe.displayName="SelectPortal";var He="SelectContent",Ve=e.forwardRef(((r,i)=>{const o=Ie(He,r.__scopeSelect),[a,s]=e.useState();if(oe((()=>{s(new DocumentFragment)}),[]),!o.open){const e=a;return e?n.createPortal(t.jsx(Xe,{scope:r.__scopeSelect,children:t.jsx(_e.Slot,{scope:r.__scopeSelect,children:t.jsx("div",{children:r.children})})}),e):null}return t.jsx(Ke,{...r,ref:i})}));Ve.displayName=He;var Ye=10,[Xe,Ge]=Me(He),Ke=e.forwardRef(((n,r)=>{const{__scopeSelect:i,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:s,onPointerDownOutside:c,side:l,sideOffset:u,align:f,alignOffset:d,arrowPadding:p,collisionBoundary:h,collisionPadding:y,sticky:m,hideWhenDetached:v,avoidCollisions:g,...b}=n,x=Ie(He,i),[w,j]=e.useState(null),[O,S]=e.useState(null),P=te(r,(e=>j(e))),[A,k]=e.useState(null),[_,E]=e.useState(null),N=Ee(i),[M,T]=e.useState(!1),C=e.useRef(!1);e.useEffect((()=>{if(w)return ae(w)}),[w]),se();const D=e.useCallback((e=>{const[t,...n]=N().map((e=>e.ref.current)),[r]=n.slice(-1),i=document.activeElement;for(const o of e){if(o===i)return;if(o?.scrollIntoView({block:"nearest"}),o===t&&O&&(O.scrollTop=0),o===r&&O&&(O.scrollTop=O.scrollHeight),o?.focus(),document.activeElement!==i)return}}),[N,O]),I=e.useCallback((()=>D([A,w])),[D,A,w]);e.useEffect((()=>{M&&I()}),[M,I]);const{onOpenChange:R,triggerPointerDownPosRef:B}=x;e.useEffect((()=>{if(w){let e={x:0,y:0};const t=t=>{e={x:Math.abs(Math.round(t.pageX)-(B.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(B.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():w.contains(n.target)||R(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[w,R,B]),e.useEffect((()=>{const e=()=>R(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[R]);const[L,z]=Pt((e=>{const t=N().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=At(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),F=e.useCallback(((e,t,n)=>{const r=!C.current&&!n;(void 0!==x.value&&x.value===t||r)&&(k(e),r&&(C.current=!0))}),[x.value]),$=e.useCallback((()=>w?.focus()),[w]),U=e.useCallback(((e,t,n)=>{const r=!C.current&&!n;(void 0!==x.value&&x.value===t||r)&&E(e)}),[x.value]),W="popper"===o?Qe:Ze,q=W===Qe?{side:l,sideOffset:u,align:f,alignOffset:d,arrowPadding:p,collisionBoundary:h,collisionPadding:y,sticky:m,hideWhenDetached:v,avoidCollisions:g}:{};return t.jsx(Xe,{scope:i,content:w,viewport:O,onViewportChange:S,itemRefCallback:F,selectedItem:A,onItemLeave:$,itemTextRefCallback:U,focusSelectedItem:I,selectedItemText:_,position:o,isPositioned:M,searchRef:L,children:t.jsx(ce,{as:le,allowPinchZoom:!0,children:t.jsx(ue,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:ie(a,(e=>{x.trigger?.focus({preventScroll:!0}),e.preventDefault()})),children:t.jsx(fe,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:t.jsx(W,{role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault(),...b,...q,onPlaced:()=>T(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...b.style},onKeyDown:ie(b.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||z(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=N().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>D(t))),e.preventDefault()}}))})})})})})}));Ke.displayName="SelectContentImpl";var Ze=e.forwardRef(((n,r)=>{const{__scopeSelect:i,onPlaced:o,...a}=n,s=Ie(He,i),c=Ge(He,i),[l,u]=e.useState(null),[f,d]=e.useState(null),p=te(r,(e=>d(e))),h=Ee(i),y=e.useRef(!1),m=e.useRef(!0),{viewport:v,selectedItem:g,selectedItemText:b,focusSelectedItem:x}=c,w=e.useCallback((()=>{if(s.trigger&&s.valueNode&&l&&f&&v&&g&&b){const e=s.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=s.valueNode.getBoundingClientRect(),r=b.getBoundingClientRect();if("rtl"!==s.dir){const i=r.left-t.left,o=n.left-i,a=e.left-o,s=e.width+a,c=Math.max(s,t.width),u=window.innerWidth-Ye,f=Se(o,[Ye,Math.max(Ye,u-c)]);l.style.minWidth=s+"px",l.style.left=f+"px"}else{const i=t.right-r.right,o=window.innerWidth-n.right-i,a=window.innerWidth-e.right-o,s=e.width+a,c=Math.max(s,t.width),u=window.innerWidth-Ye,f=Se(o,[Ye,Math.max(Ye,u-c)]);l.style.minWidth=s+"px",l.style.right=f+"px"}const i=h(),a=window.innerHeight-2*Ye,c=v.scrollHeight,u=window.getComputedStyle(f),d=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),m=parseInt(u.borderBottomWidth,10),x=d+p+c+parseInt(u.paddingBottom,10)+m,w=Math.min(5*g.offsetHeight,x),j=window.getComputedStyle(v),O=parseInt(j.paddingTop,10),S=parseInt(j.paddingBottom,10),P=e.top+e.height/2-Ye,A=a-P,k=g.offsetHeight/2,_=d+p+(g.offsetTop+k),E=x-_;if(_<=P){const e=i.length>0&&g===i[i.length-1].ref.current;l.style.bottom="0px";const t=f.clientHeight-v.offsetTop-v.offsetHeight,n=_+Math.max(A,k+(e?S:0)+t+m);l.style.height=n+"px"}else{const e=i.length>0&&g===i[0].ref.current;l.style.top="0px";const t=Math.max(P,d+v.offsetTop+(e?O:0)+k)+E;l.style.height=t+"px",v.scrollTop=_-P+v.offsetTop}l.style.margin=`${Ye}px 0`,l.style.minHeight=w+"px",l.style.maxHeight=a+"px",o?.(),requestAnimationFrame((()=>y.current=!0))}}),[h,s.trigger,s.valueNode,l,f,v,g,b,s.dir,o]);oe((()=>w()),[w]);const[j,O]=e.useState();oe((()=>{f&&O(window.getComputedStyle(f).zIndex)}),[f]);const S=e.useCallback((e=>{e&&!0===m.current&&(w(),x?.(),m.current=!1)}),[w,x]);return t.jsx(Je,{scope:i,contentWrapper:l,shouldExpandOnScrollRef:y,onScrollButtonChange:S,children:t.jsx("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:t.jsx(re.div,{...a,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})}));Ze.displayName="SelectItemAlignedPosition";var Qe=e.forwardRef(((e,n)=>{const{__scopeSelect:r,align:i="start",collisionPadding:o=Ye,...a}=e,s=Ce(r);return t.jsx(de,{...s,...a,ref:n,align:i,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));Qe.displayName="SelectPopperPosition";var[Je,et]=Me(He,{}),tt="SelectViewport",nt=e.forwardRef(((n,r)=>{const{__scopeSelect:i,nonce:o,...a}=n,s=Ge(tt,i),c=et(tt,i),l=te(r,s.onViewportChange),u=e.useRef(0);return t.jsxs(t.Fragment,{children:[t.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),t.jsx(_e.Slot,{scope:i,children:t.jsx(re.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:ie(a.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=c;if(r?.current&&n){const e=Math.abs(u.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*Ye,i=parseFloat(n.style.minHeight),o=parseFloat(n.style.height),a=Math.max(i,o);if(a<r){const i=a+e,o=Math.min(r,i),s=i-o;n.style.height=o+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}u.current=t.scrollTop}))})})]})}));nt.displayName=tt;var rt="SelectGroup",[it,ot]=Me(rt);e.forwardRef(((e,n)=>{const{__scopeSelect:r,...i}=e,o=pe();return t.jsx(it,{scope:r,id:o,children:t.jsx(re.div,{role:"group","aria-labelledby":o,...i,ref:n})})})).displayName=rt;var at="SelectLabel",st=e.forwardRef(((e,n)=>{const{__scopeSelect:r,...i}=e,o=ot(at,r);return t.jsx(re.div,{id:o.id,...i,ref:n})}));st.displayName=at;var ct="SelectItem",[lt,ut]=Me(ct),ft=e.forwardRef(((n,r)=>{const{__scopeSelect:i,value:o,disabled:a=!1,textValue:s,...c}=n,l=Ie(ct,i),u=Ge(ct,i),f=l.value===o,[d,p]=e.useState(s??""),[h,y]=e.useState(!1),m=te(r,(e=>u.itemRefCallback?.(e,o,a))),v=pe(),g=e.useRef("touch"),b=()=>{a||(l.onValueChange(o),l.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return t.jsx(lt,{scope:i,value:o,disabled:a,textId:v,isSelected:f,onItemTextChange:e.useCallback((e=>{p((t=>t||(e?.textContent??"").trim()))}),[]),children:t.jsx(_e.ItemSlot,{scope:i,value:o,disabled:a,textValue:d,children:t.jsx(re.div,{role:"option","aria-labelledby":v,"data-highlighted":h?"":void 0,"aria-selected":f&&h,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...c,ref:m,onFocus:ie(c.onFocus,(()=>y(!0))),onBlur:ie(c.onBlur,(()=>y(!1))),onClick:ie(c.onClick,(()=>{"mouse"!==g.current&&b()})),onPointerUp:ie(c.onPointerUp,(()=>{"mouse"===g.current&&b()})),onPointerDown:ie(c.onPointerDown,(e=>{g.current=e.pointerType})),onPointerMove:ie(c.onPointerMove,(e=>{g.current=e.pointerType,a?u.onItemLeave?.():"mouse"===g.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:ie(c.onPointerLeave,(e=>{e.currentTarget===document.activeElement&&u.onItemLeave?.()})),onKeyDown:ie(c.onKeyDown,(e=>{""!==u.searchRef?.current&&" "===e.key||(Ae.includes(e.key)&&b()," "===e.key&&e.preventDefault())}))})})})}));ft.displayName=ct;var dt="SelectItemText",pt=e.forwardRef(((r,i)=>{const{__scopeSelect:o,className:a,style:s,...c}=r,l=Ie(dt,o),u=Ge(dt,o),f=ut(dt,o),d=Be(dt,o),[p,h]=e.useState(null),y=te(i,(e=>h(e)),f.onItemTextChange,(e=>u.itemTextRefCallback?.(e,f.value,f.disabled))),m=p?.textContent,v=e.useMemo((()=>t.jsx("option",{value:f.value,disabled:f.disabled,children:m},f.value)),[f.disabled,f.value,m]),{onNativeOptionAdd:g,onNativeOptionRemove:b}=d;return oe((()=>(g(v),()=>b(v))),[g,b,v]),t.jsxs(t.Fragment,{children:[t.jsx(re.span,{id:f.textId,...c,ref:y}),f.isSelected&&l.valueNode&&!l.valueNodeHasChildren?n.createPortal(c.children,l.valueNode):null]})}));pt.displayName=dt;var ht="SelectItemIndicator",yt=e.forwardRef(((e,n)=>{const{__scopeSelect:r,...i}=e;return ut(ht,r).isSelected?t.jsx(re.span,{"aria-hidden":!0,...i,ref:n}):null}));yt.displayName=ht;var mt="SelectScrollUpButton",vt=e.forwardRef(((n,r)=>{const i=Ge(mt,n.__scopeSelect),o=et(mt,n.__scopeSelect),[a,s]=e.useState(!1),c=te(r,o.onScrollButtonChange);return oe((()=>{if(i.viewport&&i.isPositioned){let e=function(){const e=t.scrollTop>0;s(e)};const t=i.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[i.viewport,i.isPositioned]),a?t.jsx(xt,{...n,ref:c,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=i;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));vt.displayName=mt;var gt="SelectScrollDownButton",bt=e.forwardRef(((n,r)=>{const i=Ge(gt,n.__scopeSelect),o=et(gt,n.__scopeSelect),[a,s]=e.useState(!1),c=te(r,o.onScrollButtonChange);return oe((()=>{if(i.viewport&&i.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;s(n)};const t=i.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[i.viewport,i.isPositioned]),a?t.jsx(xt,{...n,ref:c,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=i;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));bt.displayName=gt;var xt=e.forwardRef(((n,r)=>{const{__scopeSelect:i,onAutoScroll:o,...a}=n,s=Ge("SelectScrollButton",i),c=e.useRef(null),l=Ee(i),u=e.useCallback((()=>{null!==c.current&&(window.clearInterval(c.current),c.current=null)}),[]);return e.useEffect((()=>()=>u()),[u]),oe((()=>{const e=l().find((e=>e.ref.current===document.activeElement));e?.ref.current?.scrollIntoView({block:"nearest"})}),[l]),t.jsx(re.div,{"aria-hidden":!0,...a,ref:r,style:{flexShrink:0,...a.style},onPointerDown:ie(a.onPointerDown,(()=>{null===c.current&&(c.current=window.setInterval(o,50))})),onPointerMove:ie(a.onPointerMove,(()=>{s.onItemLeave?.(),null===c.current&&(c.current=window.setInterval(o,50))})),onPointerLeave:ie(a.onPointerLeave,(()=>{u()}))})})),wt=e.forwardRef(((e,n)=>{const{__scopeSelect:r,...i}=e;return t.jsx(re.div,{"aria-hidden":!0,...i,ref:n})}));wt.displayName="SelectSeparator";var jt="SelectArrow";function Ot(e){return""===e||void 0===e}e.forwardRef(((e,n)=>{const{__scopeSelect:r,...i}=e,o=Ce(r),a=Ie(jt,r),s=Ge(jt,r);return a.open&&"popper"===s.position?t.jsx(he,{...o,...i,ref:n}):null})).displayName=jt;var St=e.forwardRef(((n,r)=>{const{value:i,...o}=n,a=e.useRef(null),s=te(r,a),c=ye(i);return e.useEffect((()=>{const e=a.current,t=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(c!==i&&n){const t=new Event("change",{bubbles:!0});n.call(e,i),e.dispatchEvent(t)}}),[c,i]),t.jsx(me,{asChild:!0,children:t.jsx("select",{...o,ref:s,defaultValue:i})})}));function Pt(t){const n=ve(t),r=e.useRef(""),i=e.useRef(0),o=e.useCallback((e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(i.current),""!==t&&(i.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),a=e.useCallback((()=>{r.current="",window.clearTimeout(i.current)}),[]);return e.useEffect((()=>()=>window.clearTimeout(i.current)),[]),[r,o,a]}function At(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,i=n?e.indexOf(n):-1;let o=function(e,t){return e.map(((n,r)=>e[(t+r)%e.length]))}(e,Math.max(i,0));1===r.length&&(o=o.filter((e=>e!==n)));const a=o.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return a!==n?a:void 0}St.displayName="BubbleSelect";var kt=Fe,_t=We,Et=qe,Nt=Ve,Mt=nt,Tt=st,Ct=ft,Dt=pt,It=yt,Rt=vt,Bt=bt,Lt=wt;const zt=Le,Ft=Ue,$t=e.forwardRef((({className:e,children:n,...i},o)=>t.jsxs(kt,{ref:o,className:C("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...i,children:[n,t.jsx(_t,{asChild:!0,children:t.jsx(r,{className:"h-4 w-4 opacity-50"})})]})));$t.displayName=kt.displayName;const Ut=e.forwardRef((({className:e,...n},r)=>t.jsx(Rt,{ref:r,className:C("flex cursor-default items-center justify-center py-1",e),...n,children:t.jsx(i,{className:"h-4 w-4"})})));Ut.displayName=Rt.displayName;const Wt=e.forwardRef((({className:e,...n},i)=>t.jsx(Bt,{ref:i,className:C("flex cursor-default items-center justify-center py-1",e),...n,children:t.jsx(r,{className:"h-4 w-4"})})));Wt.displayName=Bt.displayName;const qt=e.forwardRef((({className:e,children:n,position:r="popper",...i},o)=>t.jsx(Et,{children:t.jsxs(Nt,{ref:o,className:C("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...i,children:[t.jsx(Ut,{}),t.jsx(Mt,{className:C("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),t.jsx(Wt,{})]})})));qt.displayName=Nt.displayName;e.forwardRef((({className:e,...n},r)=>t.jsx(Tt,{ref:r,className:C("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...n}))).displayName=Tt.displayName;const Ht=e.forwardRef((({className:e,children:n,...r},i)=>t.jsxs(Ct,{ref:i,className:C("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(It,{children:t.jsx(o,{className:"h-4 w-4"})})}),t.jsx(Dt,{children:n})]})));Ht.displayName=Ct.displayName;function Vt(e){const t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):"number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?new Date(e):new Date(NaN)}function Yt(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}e.forwardRef((({className:e,...n},r)=>t.jsx(Lt,{ref:r,className:C("-mx-1 my-1 h-px bg-muted",e),...n}))).displayName=Lt.displayName;const Xt=6048e5;let Gt={};function Kt(){return Gt}function Zt(e,t){const n=Kt(),r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,i=Vt(e),o=i.getDay(),a=(o<r?7:0)+o-r;return i.setDate(i.getDate()-a),i.setHours(0,0,0,0),i}function Qt(e){return Zt(e,{weekStartsOn:1})}function Jt(e){const t=Vt(e),n=t.getFullYear(),r=Yt(e,0);r.setFullYear(n+1,0,4),r.setHours(0,0,0,0);const i=Qt(r),o=Yt(e,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const a=Qt(o);return t.getTime()>=i.getTime()?n+1:t.getTime()>=a.getTime()?n:n-1}function en(e){const t=Vt(e);return t.setHours(0,0,0,0),t}function tn(e){const t=Vt(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function nn(e){if(!(t=e,t instanceof Date||"object"==typeof t&&"[object Date]"===Object.prototype.toString.call(t)||"number"==typeof e))return!1;var t;const n=Vt(e);return!isNaN(Number(n))}const rn={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function on(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const an={date:on({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:on({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:on({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},sn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function cn(e){return(t,n)=>{let r;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth,i=n?.width?String(n.width):t;r=e.formattingValues[i]||e.formattingValues[t]}else{const t=e.defaultWidth,i=n?.width?String(n.width):e.defaultWidth;r=e.values[i]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}const ln={ordinalNumber:(e,t)=>{const n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:cn({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:cn({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:cn({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:cn({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:cn({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})};function un(e){return(t,n={})=>{const r=n.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],o=t.match(i);if(!o)return null;const a=o[0],s=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],c=Array.isArray(s)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n;return}(s,(e=>e.test(a))):function(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n;return}(s,(e=>e.test(a)));let l;l=e.valueCallback?e.valueCallback(c):c,l=n.valueCallback?n.valueCallback(l):l;return{value:l,rest:t.slice(a.length)}}}const fn={ordinalNumber:(dn={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},(e,t={})=>{const n=e.match(dn.matchPattern);if(!n)return null;const r=n[0],i=e.match(dn.parsePattern);if(!i)return null;let o=dn.valueCallback?dn.valueCallback(i[0]):i[0];return o=t.valueCallback?t.valueCallback(o):o,{value:o,rest:e.slice(r.length)}}),era:un({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:un({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:un({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:un({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:un({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})};var dn;const pn={code:"en-US",formatDistance:(e,t,n)=>{let r;const i=rn[e];return r="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",t.toString()),n?.addSuffix?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:an,formatRelative:(e,t,n,r)=>sn[e],localize:ln,match:fn,options:{weekStartsOn:0,firstWeekContainsDate:1}};function hn(e){const t=Vt(e),n=function(e,t){const n=en(e),r=en(t),i=+n-tn(n),o=+r-tn(r);return Math.round((i-o)/864e5)}(t,function(e){const t=Vt(e),n=Yt(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}(t));return n+1}function yn(e){const t=Vt(e),n=+Qt(t)-+function(e){const t=Jt(e),n=Yt(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),Qt(n)}(t);return Math.round(n/Xt)+1}function mn(e,t){const n=Vt(e),r=n.getFullYear(),i=Kt(),o=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??i.firstWeekContainsDate??i.locale?.options?.firstWeekContainsDate??1,a=Yt(e,0);a.setFullYear(r+1,0,o),a.setHours(0,0,0,0);const s=Zt(a,t),c=Yt(e,0);c.setFullYear(r,0,o),c.setHours(0,0,0,0);const l=Zt(c,t);return n.getTime()>=s.getTime()?r+1:n.getTime()>=l.getTime()?r:r-1}function vn(e,t){const n=Vt(e),r=+Zt(n,t)-+function(e,t){const n=Kt(),r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,i=mn(e,t),o=Yt(e,0);return o.setFullYear(i,0,r),o.setHours(0,0,0,0),Zt(o,t)}(n,t);return Math.round(r/Xt)+1}function gn(e,t){return(e<0?"-":"")+Math.abs(e).toString().padStart(t,"0")}const bn={y(e,t){const n=e.getFullYear(),r=n>0?n:1-n;return gn("yy"===t?r%100:r,t.length)},M(e,t){const n=e.getMonth();return"M"===t?String(n+1):gn(n+1,2)},d:(e,t)=>gn(e.getDate(),t.length),a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(e,t)=>gn(e.getHours()%12||12,t.length),H:(e,t)=>gn(e.getHours(),t.length),m:(e,t)=>gn(e.getMinutes(),t.length),s:(e,t)=>gn(e.getSeconds(),t.length),S(e,t){const n=t.length,r=e.getMilliseconds();return gn(Math.trunc(r*Math.pow(10,n-3)),t.length)}},xn="midnight",wn="noon",jn="morning",On="afternoon",Sn="evening",Pn="night",An={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if("yo"===t){const t=e.getFullYear(),r=t>0?t:1-t;return n.ordinalNumber(r,{unit:"year"})}return bn.y(e,t)},Y:function(e,t,n,r){const i=mn(e,r),o=i>0?i:1-i;if("YY"===t){return gn(o%100,2)}return"Yo"===t?n.ordinalNumber(o,{unit:"year"}):gn(o,t.length)},R:function(e,t){return gn(Jt(e),t.length)},u:function(e,t){return gn(e.getFullYear(),t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return gn(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return gn(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return bn.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return gn(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const i=vn(e,r);return"wo"===t?n.ordinalNumber(i,{unit:"week"}):gn(i,t.length)},I:function(e,t,n){const r=yn(e);return"Io"===t?n.ordinalNumber(r,{unit:"week"}):gn(r,t.length)},d:function(e,t,n){return"do"===t?n.ordinalNumber(e.getDate(),{unit:"date"}):bn.d(e,t)},D:function(e,t,n){const r=hn(e);return"Do"===t?n.ordinalNumber(r,{unit:"dayOfYear"}):gn(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const i=e.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return gn(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const i=e.getDay(),o=(i-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return gn(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(i,{width:"narrow",context:"standalone"});case"cccccc":return n.day(i,{width:"short",context:"standalone"});default:return n.day(i,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay(),i=0===r?7:r;switch(t){case"i":return String(i);case"ii":return gn(i,t.length);case"io":return n.ordinalNumber(i,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let i;switch(i=12===r?wn:0===r?xn:r/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let i;switch(i=r>=17?Sn:r>=12?On:r>=4?jn:Pn,t){case"B":case"BB":case"BBB":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(e,t,n){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),n.ordinalNumber(t,{unit:"hour"})}return bn.h(e,t)},H:function(e,t,n){return"Ho"===t?n.ordinalNumber(e.getHours(),{unit:"hour"}):bn.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;return"Ko"===t?n.ordinalNumber(r,{unit:"hour"}):gn(r,t.length)},k:function(e,t,n){let r=e.getHours();return 0===r&&(r=24),"ko"===t?n.ordinalNumber(r,{unit:"hour"}):gn(r,t.length)},m:function(e,t,n){return"mo"===t?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):bn.m(e,t)},s:function(e,t,n){return"so"===t?n.ordinalNumber(e.getSeconds(),{unit:"second"}):bn.s(e,t)},S:function(e,t){return bn.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(0===r)return"Z";switch(t){case"X":return _n(r);case"XXXX":case"XX":return En(r);default:return En(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return _n(r);case"xxxx":case"xx":return En(r);default:return En(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+kn(r,":");default:return"GMT"+En(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+kn(r,":");default:return"GMT"+En(r,":")}},t:function(e,t,n){return gn(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,n){return gn(e.getTime(),t.length)}};function kn(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),i=Math.trunc(r/60),o=r%60;return 0===o?n+String(i):n+String(i)+t+gn(o,2)}function _n(e,t){if(e%60==0){return(e>0?"-":"+")+gn(Math.abs(e)/60,2)}return En(e,t)}function En(e,t=""){const n=e>0?"-":"+",r=Math.abs(e);return n+gn(Math.trunc(r/60),2)+t+gn(r%60,2)}const Nn=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},Mn=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},Tn={p:Mn,P:(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],r=n[1],i=n[2];if(!i)return Nn(e,t);let o;switch(r){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;default:o=t.dateTime({width:"full"})}return o.replace("{{date}}",Nn(r,t)).replace("{{time}}",Mn(i,t))}},Cn=/^D+$/,Dn=/^Y+$/,In=["D","DD","YY","YYYY"];const Rn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,Bn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ln=/^'([^]*?)'?$/,zn=/''/g,Fn=/[a-zA-Z]/;function $n(e,t,n){const r=Kt(),i=r.locale??pn,o=r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,a=r.weekStartsOn??r.locale?.options?.weekStartsOn??0,s=Vt(e);if(!nn(s))throw new RangeError("Invalid time value");let c=t.match(Bn).map((e=>{const t=e[0];if("p"===t||"P"===t){return(0,Tn[t])(e,i.formatLong)}return e})).join("").match(Rn).map((e=>{if("''"===e)return{isToken:!1,value:"'"};const t=e[0];if("'"===t)return{isToken:!1,value:Un(e)};if(An[t])return{isToken:!0,value:e};if(t.match(Fn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}}));i.localize.preprocessor&&(c=i.localize.preprocessor(s,c));const l={firstWeekContainsDate:o,weekStartsOn:a,locale:i};return c.map((n=>{if(!n.isToken)return n.value;const r=n.value;(function(e){return Dn.test(e)}(r)||function(e){return Cn.test(e)}(r))&&function(e,t,n){const r=function(e,t,n){const r="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,n);if(In.includes(e))throw new RangeError(r)}(r,t,String(e));return(0,An[r[0]])(s,r,i.localize,l)})).join("")}function Un(e){const t=e.match(Ln);return t?t[1].replace(zn,"'"):e}const Wn=()=>{const[n,r]=e.useState([]),[i,o]=e.useState(!0),[p,h]=e.useState(null),[y,m]=e.useState(""),[v,g]=e.useState("all"),[b,x]=e.useState("all"),{toast:w}=B();e.useEffect((()=>{j()}),[]);const j=async()=>{try{o(!0),h(null);const{data:e,error:t}=await D.from("assessment_submissions").select("\n          id,\n          company_name,\n          contact_name,\n          email,\n          phone,\n          industry,\n          employee_count,\n          status,\n          created_at,\n          completed_at,\n          assessment_type_id,\n          lead_scores (\n            risk_level,\n            risk_percentage,\n            lead_priority,\n            follow_up_urgency,\n            total_risk_score,\n            max_possible_score\n          ),\n          assessment_types (\n            title,\n            name\n          )\n        ").order("created_at",{ascending:!1});if(t)return h(`Failed to load leads: ${t.message}`),void w({title:"Error",description:"Failed to load leads. Please try again.",variant:"destructive"});r(e||[])}catch(e){h("An unexpected error occurred while loading leads."),w({title:"Error",description:"An unexpected error occurred. Please try again.",variant:"destructive"})}finally{o(!1)}},O=n.filter((e=>{const t=e.company_name.toLowerCase().includes(y.toLowerCase())||e.contact_name.toLowerCase().includes(y.toLowerCase())||e.email.toLowerCase().includes(y.toLowerCase())||e.industry.toLowerCase().includes(y.toLowerCase()),n="all"===v||e.status===v,r="all"===b||e.lead_scores?.risk_level?.toLowerCase()===b.toLowerCase();return t&&n&&r})),S=e=>{switch(e?.toLowerCase()){case"high":return"destructive";case"medium":return"default";case"low":return"secondary";default:return"outline"}},P=e=>{switch(e.toLowerCase()){case"completed":return"default";case"in_progress":return"secondary";default:return"outline"}};return i?t.jsxs(L,{children:[t.jsx(z,{children:t.jsxs(F,{className:"flex items-center gap-2",children:[t.jsx(a,{className:"h-5 w-5"}),"Loading Leads..."]})}),t.jsx($,{children:t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})})]}):p?t.jsxs(L,{children:[t.jsx(z,{children:t.jsxs(F,{className:"flex items-center gap-2 text-red-600",children:[t.jsx(s,{className:"h-5 w-5"}),"Error Loading Leads"]})}),t.jsx($,{children:t.jsxs("div",{className:"text-center py-8",children:[t.jsx("p",{className:"text-red-600 mb-4",children:p}),t.jsx(U,{onClick:j,variant:"outline",children:"Try Again"})]})})]}):t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsxs(F,{className:"flex items-center gap-2",children:[t.jsx(a,{className:"h-5 w-5"}),"Lead Management (",O.length,")"]}),t.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 mt-4",children:[t.jsxs("div",{className:"relative flex-1",children:[t.jsx(c,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),t.jsx(Z,{placeholder:"Search leads...",value:y,onChange:e=>m(e.target.value),className:"pl-10"})]}),t.jsxs(zt,{value:v,onValueChange:g,children:[t.jsx($t,{className:"w-full sm:w-40",children:t.jsx(Ft,{placeholder:"Status"})}),t.jsxs(qt,{children:[t.jsx(Ht,{value:"all",children:"All Status"}),t.jsx(Ht,{value:"completed",children:"Completed"}),t.jsx(Ht,{value:"in_progress",children:"In Progress"}),t.jsx(Ht,{value:"started",children:"Started"})]})]}),t.jsxs(zt,{value:b,onValueChange:x,children:[t.jsx($t,{className:"w-full sm:w-40",children:t.jsx(Ft,{placeholder:"Risk Level"})}),t.jsxs(qt,{children:[t.jsx(Ht,{value:"all",children:"All Risk"}),t.jsx(Ht,{value:"high",children:"High Risk"}),t.jsx(Ht,{value:"medium",children:"Medium Risk"}),t.jsx(Ht,{value:"low",children:"Low Risk"})]})]})]})]}),t.jsx($,{children:0===O.length?t.jsxs("div",{className:"text-center py-12",children:[t.jsx(a,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No leads found"}),t.jsx("p",{className:"text-gray-600",children:y||"all"!==v||"all"!==b?"Try adjusting your filters":"Leads will appear here once assessments are completed"})]}):t.jsx("div",{className:"space-y-4",children:O.map((e=>{return t.jsx("div",{className:"border rounded-lg p-4 hover:bg-gray-50 transition-colors",children:t.jsxs("div",{className:"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4",children:[t.jsxs("div",{className:"flex-1 space-y-2",children:[t.jsxs("div",{className:"flex items-center gap-3 flex-wrap",children:[t.jsx("h3",{className:"font-semibold text-lg",children:e.company_name}),t.jsx(W,{variant:P(e.status),children:e.status.replace("_"," ")}),e.lead_scores?.risk_level&&t.jsxs(W,{variant:S(e.lead_scores.risk_level),children:[e.lead_scores.risk_level," Risk"]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(l,{className:"h-4 w-4"}),e.contact_name," - ",e.email]}),e.phone&&t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(u,{className:"h-4 w-4"}),e.phone]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(f,{className:"h-4 w-4"}),e.industry]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(a,{className:"h-4 w-4"}),e.employee_count," employees"]})]}),e.assessment_types&&t.jsxs("div",{className:"text-sm text-gray-600",children:["Assessment: ",e.assessment_types.title]})]}),t.jsxs("div",{className:"flex flex-col items-end gap-2",children:[t.jsx("div",{className:"text-sm text-gray-500",children:$n(new Date(e.created_at),"MMM dd, yyyy")}),e.lead_scores&&t.jsxs("div",{className:"text-right space-y-1",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(d,{className:"h-4 w-4"}),t.jsxs("span",{className:"text-sm font-medium",children:[e.lead_scores.risk_percentage,"% Risk"]})]}),t.jsxs("div",{className:"text-xs text-gray-600",children:["Priority: ",(n=e.lead_scores.lead_priority,n?n<=3?"High":n<=6?"Medium":"Low":"Pending")]}),e.lead_scores.total_risk_score&&e.lead_scores.max_possible_score&&t.jsxs("div",{className:"text-xs text-gray-600",children:["Score: ",e.lead_scores.total_risk_score,"/",e.lead_scores.max_possible_score]})]})]})]})},e.id);var n}))})})]})},qn=()=>{const[n,r]=e.useState(null),[i,o]=e.useState(!0);e.useEffect((()=>{c()}),[]);const c=async()=>{try{const{count:e,error:t}=await D.from("assessment_submissions").select("*",{count:"exact",head:!0});if(t)throw t;const{count:n,error:i}=await D.from("assessment_submissions").select("*",{count:"exact",head:!0}).eq("status","completed");if(i)throw i;const{count:o,error:a}=await D.from("lead_scores").select("*",{count:"exact",head:!0}).eq("risk_level","HIGH"),{count:s,error:c}=await D.from("assessment_submissions").select("*",{count:"exact",head:!0}).gte("created_at",new Date(Date.now()-6048e5).toISOString());if(c)throw c;const{data:l}=await D.from("assessment_submissions").select("industry").eq("status","completed"),{data:u}=await D.from("lead_scores").select("risk_level, risk_percentage, lead_priority"),{count:f}=await D.from("assessment_submissions").select("*",{count:"exact",head:!0}).gte("created_at",new Date(Date.now()-12096e5).toISOString()).lt("created_at",new Date(Date.now()-6048e5).toISOString()),d=l?.reduce(((e,t)=>(e[t.industry]=(e[t.industry]||0)+1,e)),{})||{},p=u?.reduce(((e,t)=>(e[t.risk_level]=(e[t.risk_level]||0)+1,e)),{})||{},h=u?.reduce(((e,t)=>(e[t.lead_priority]=(e[t.lead_priority]||0)+1,e)),{})||{},y=u?.length>0?u.reduce(((e,t)=>e+(t.risk_percentage||0)),0)/u.length:0,m=f>0?Math.round(((s||0)-f)/f*100):0;r({totalSubmissions:e||0,completedAssessments:n||0,highRiskLeads:o||0,conversionRate:e?Math.round((n||0)/e*100):0,industryBreakdown:Object.entries(d).map((([e,t])=>({industry:e,count:t}))),riskDistribution:Object.entries(p).map((([e,t])=>({risk_level:e,count:t}))),averageRiskScore:Math.round(100*y)/100,leadsByPriority:Object.entries(h).map((([e,t])=>({priority:parseInt(e),count:t}))),recentSubmissions:s||0,completionTrend:m})}catch(e){}finally{o(!1)}};return i?t.jsx(L,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsx($,{className:"p-8 text-center",children:t.jsx("div",{className:"text-white",children:"Loading analytics..."})})}):n?t.jsxs("div",{className:"space-y-6",children:[t.jsxs(L,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsx(z,{children:t.jsx(F,{className:"text-white",children:"Key Metrics"})}),t.jsxs($,{className:"space-y-4",children:[t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-green-dark/20 rounded-lg",children:[t.jsx(a,{className:"h-8 w-8 text-green-bright"}),t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl font-bold text-white",children:n.totalSubmissions}),t.jsx("div",{className:"text-white/60 text-sm",children:"Total Submissions"})]})]}),t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-blue-500/20 rounded-lg",children:[t.jsx(p,{className:"h-8 w-8 text-blue-400"}),t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl font-bold text-white",children:n.completedAssessments}),t.jsx("div",{className:"text-white/60 text-sm",children:"Completed Assessments"})]})]}),t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-red-500/20 rounded-lg",children:[t.jsx(s,{className:"h-8 w-8 text-red-400"}),t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl font-bold text-white",children:n.highRiskLeads}),t.jsx("div",{className:"text-white/60 text-sm",children:"High Risk Leads"})]})]}),t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-yellow-500/20 rounded-lg",children:[t.jsx(d,{className:"h-8 w-8 text-yellow-400"}),t.jsxs("div",{children:[t.jsxs("div",{className:"text-2xl font-bold text-white",children:[n.conversionRate,"%"]}),t.jsx("div",{className:"text-white/60 text-sm",children:"Completion Rate"})]})]}),t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-purple-500/20 rounded-lg",children:[t.jsx(p,{className:"h-8 w-8 text-purple-400"}),t.jsxs("div",{children:[t.jsxs("div",{className:"text-2xl font-bold text-white",children:[n.averageRiskScore,"%"]}),t.jsx("div",{className:"text-white/60 text-sm",children:"Average Risk Score"})]})]}),t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-cyan-500/20 rounded-lg",children:[t.jsx(a,{className:"h-8 w-8 text-cyan-400"}),t.jsxs("div",{children:[t.jsx("div",{className:"text-2xl font-bold text-white",children:n.recentSubmissions}),t.jsx("div",{className:"text-white/60 text-sm",children:"Recent Submissions (7d)"})]})]}),t.jsxs("div",{className:"flex items-center gap-3 p-3 bg-orange-500/20 rounded-lg",children:[t.jsx(d,{className:"h-8 w-8 "+(n.completionTrend>=0?"text-green-400":"text-red-400")}),t.jsxs("div",{children:[t.jsxs("div",{className:"text-2xl font-bold "+(n.completionTrend>=0?"text-green-400":"text-red-400"),children:[n.completionTrend>=0?"+":"",n.completionTrend,"%"]}),t.jsx("div",{className:"text-white/60 text-sm",children:"Weekly Trend"})]})]})]})]}),t.jsxs(L,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsx(z,{children:t.jsx(F,{className:"text-white",children:"Industry Breakdown"})}),t.jsx($,{children:t.jsx("div",{className:"space-y-3",children:n.industryBreakdown.map((({industry:e,count:n})=>t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-white capitalize",children:e}),t.jsx("span",{className:"text-green-bright font-semibold",children:n})]},e)))})})]}),t.jsxs(L,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsx(z,{children:t.jsx(F,{className:"text-white",children:"Risk Distribution"})}),t.jsx($,{children:t.jsx("div",{className:"space-y-3",children:n.riskDistribution.map((({risk_level:e,count:n})=>t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("span",{className:"font-medium "+("HIGH"===e?"text-red-400":"MEDIUM"===e?"text-yellow-400":"text-green-400"),children:[e," Risk"]}),t.jsx("span",{className:"text-white font-semibold",children:n})]},e)))})})]}),t.jsxs(L,{className:"cyber-gradient-card border border-green-muted/30",children:[t.jsx(z,{children:t.jsx(F,{className:"text-white",children:"Lead Priority"})}),t.jsx($,{children:t.jsx("div",{className:"space-y-3",children:n.leadsByPriority.sort(((e,t)=>t.priority-e.priority)).map((({priority:e,count:n})=>t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("span",{className:"font-medium "+(e>=4?"text-red-400":e>=3?"text-orange-400":e>=2?"text-yellow-400":"text-green-400"),children:["Priority ",e," ",e>=4?"(Urgent)":e>=3?"(High)":e>=2?"(Medium)":"(Low)"]}),t.jsx("span",{className:"text-white font-semibold",children:n})]},e)))})})]})]}):t.jsx(L,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsx($,{className:"p-8 text-center",children:t.jsx("div",{className:"text-red-400",children:"Failed to load analytics"})})})};var Hn=Array.isArray,Vn="object"==typeof h&&h&&h.Object===Object&&h,Yn=Vn,Xn="object"==typeof self&&self&&self.Object===Object&&self,Gn=Yn||Xn||Function("return this")(),Kn=Gn.Symbol,Zn=Kn,Qn=Object.prototype,Jn=Qn.hasOwnProperty,er=Qn.toString,tr=Zn?Zn.toStringTag:void 0;var nr=function(e){var t=Jn.call(e,tr),n=e[tr];try{e[tr]=void 0;var r=!0}catch(_o){}var i=er.call(e);return r&&(t?e[tr]=n:delete e[tr]),i},rr=Object.prototype.toString;var ir=nr,or=function(e){return rr.call(e)},ar=Kn?Kn.toStringTag:void 0;var sr=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":ar&&ar in Object(e)?ir(e):or(e)};var cr=function(e){return null!=e&&"object"==typeof e},lr=sr,ur=cr;var fr=function(e){return"symbol"==typeof e||ur(e)&&"[object Symbol]"==lr(e)},dr=Hn,pr=fr,hr=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,yr=/^\w*$/;var mr=function(e,t){if(dr(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!pr(e))||(yr.test(e)||!hr.test(e)||null!=t&&e in Object(t))};var vr=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};const gr=y(vr);var br=sr,xr=vr;var wr=function(e){if(!xr(e))return!1;var t=br(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t};const jr=y(wr);var Or,Sr=Gn["__core-js_shared__"],Pr=(Or=/[^.]+$/.exec(Sr&&Sr.keys&&Sr.keys.IE_PROTO||""))?"Symbol(src)_1."+Or:"";var Ar=function(e){return!!Pr&&Pr in e},kr=Function.prototype.toString;var _r=function(e){if(null!=e){try{return kr.call(e)}catch(_o){}try{return e+""}catch(_o){}}return""},Er=wr,Nr=Ar,Mr=vr,Tr=_r,Cr=/^\[object .+?Constructor\]$/,Dr=Function.prototype,Ir=Object.prototype,Rr=Dr.toString,Br=Ir.hasOwnProperty,Lr=RegExp("^"+Rr.call(Br).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var zr=function(e,t){return null==e?void 0:e[t]},Fr=function(e){return!(!Mr(e)||Nr(e))&&(Er(e)?Lr:Cr).test(Tr(e))},$r=zr;var Ur=function(e,t){var n=$r(e,t);return Fr(n)?n:void 0},Wr=Ur(Object,"create"),qr=Wr;var Hr=function(){this.__data__=qr?qr(null):{},this.size=0};var Vr=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Yr=Wr,Xr=Object.prototype.hasOwnProperty;var Gr=function(e){var t=this.__data__;if(Yr){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Xr.call(t,e)?t[e]:void 0},Kr=Wr,Zr=Object.prototype.hasOwnProperty;var Qr=Wr;var Jr=Hr,ei=Vr,ti=Gr,ni=function(e){var t=this.__data__;return Kr?void 0!==t[e]:Zr.call(t,e)},ri=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Qr&&void 0===t?"__lodash_hash_undefined__":t,this};function ii(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}ii.prototype.clear=Jr,ii.prototype.delete=ei,ii.prototype.get=ti,ii.prototype.has=ni,ii.prototype.set=ri;var oi=ii;var ai=function(){this.__data__=[],this.size=0};var si=function(e,t){return e===t||e!=e&&t!=t},ci=si;var li=function(e,t){for(var n=e.length;n--;)if(ci(e[n][0],t))return n;return-1},ui=li,fi=Array.prototype.splice;var di=li;var pi=li;var hi=li;var yi=ai,mi=function(e){var t=this.__data__,n=ui(t,e);return!(n<0)&&(n==t.length-1?t.pop():fi.call(t,n,1),--this.size,!0)},vi=function(e){var t=this.__data__,n=di(t,e);return n<0?void 0:t[n][1]},gi=function(e){return pi(this.__data__,e)>-1},bi=function(e,t){var n=this.__data__,r=hi(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function xi(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}xi.prototype.clear=yi,xi.prototype.delete=mi,xi.prototype.get=vi,xi.prototype.has=gi,xi.prototype.set=bi;var wi=xi,ji=Ur(Gn,"Map"),Oi=oi,Si=wi,Pi=ji;var Ai=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var ki=function(e,t){var n=e.__data__;return Ai(t)?n["string"==typeof t?"string":"hash"]:n.map},_i=ki;var Ei=ki;var Ni=ki;var Mi=ki;var Ti=function(){this.size=0,this.__data__={hash:new Oi,map:new(Pi||Si),string:new Oi}},Ci=function(e){var t=_i(this,e).delete(e);return this.size-=t?1:0,t},Di=function(e){return Ei(this,e).get(e)},Ii=function(e){return Ni(this,e).has(e)},Ri=function(e,t){var n=Mi(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function Bi(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Bi.prototype.clear=Ti,Bi.prototype.delete=Ci,Bi.prototype.get=Di,Bi.prototype.has=Ii,Bi.prototype.set=Ri;var Li=Bi,zi=Li;function Fi(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var a=e.apply(this,r);return n.cache=o.set(i,a)||o,a};return n.cache=new(Fi.Cache||zi),n}Fi.Cache=zi;var $i=Fi;const Ui=y($i);var Wi=$i;var qi=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Hi=/\\(\\)?/g,Vi=function(e){var t=Wi(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(qi,(function(e,n,r,i){t.push(r?i.replace(Hi,"$1"):n||e)})),t})),Yi=Vi;var Xi=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i},Gi=Xi,Ki=Hn,Zi=fr,Qi=Kn?Kn.prototype:void 0,Ji=Qi?Qi.toString:void 0;var eo=function e(t){if("string"==typeof t)return t;if(Ki(t))return Gi(t,e)+"";if(Zi(t))return Ji?Ji.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n},to=eo;var no=function(e){return null==e?"":to(e)},ro=Hn,io=mr,oo=Yi,ao=no;var so=function(e,t){return ro(e)?e:io(e,t)?[e]:oo(ao(e))},co=fr;var lo=function(e){if("string"==typeof e||co(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t},uo=so,fo=lo;var po=function(e,t){for(var n=0,r=(t=uo(t,e)).length;null!=e&&n<r;)e=e[fo(t[n++])];return n&&n==r?e:void 0},ho=po;var yo=function(e,t,n){var r=null==e?void 0:ho(e,t);return void 0===r?n:r};const mo=y(yo);const vo=y((function(e){return null==e}));var go=sr,bo=Hn,xo=cr;const wo=y((function(e){return"string"==typeof e||!bo(e)&&xo(e)&&"[object String]"==go(e)}));var jo,Oo={exports:{}},So={},Po=Symbol.for("react.element"),Ao=Symbol.for("react.portal"),ko=Symbol.for("react.fragment"),_o=Symbol.for("react.strict_mode"),Eo=Symbol.for("react.profiler"),No=Symbol.for("react.provider"),Mo=Symbol.for("react.context"),To=Symbol.for("react.server_context"),Co=Symbol.for("react.forward_ref"),Do=Symbol.for("react.suspense"),Io=Symbol.for("react.suspense_list"),Ro=Symbol.for("react.memo"),Bo=Symbol.for("react.lazy"),Lo=Symbol.for("react.offscreen");function zo(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case Po:switch(e=e.type){case ko:case Eo:case _o:case Do:case Io:return e;default:switch(e=e&&e.$$typeof){case To:case Mo:case Co:case Bo:case Ro:case No:return e;default:return t}}case Ao:return t}}}jo=Symbol.for("react.module.reference"),So.ContextConsumer=Mo,So.ContextProvider=No,So.Element=Po,So.ForwardRef=Co,So.Fragment=ko,So.Lazy=Bo,So.Memo=Ro,So.Portal=Ao,So.Profiler=Eo,So.StrictMode=_o,So.Suspense=Do,So.SuspenseList=Io,So.isAsyncMode=function(){return!1},So.isConcurrentMode=function(){return!1},So.isContextConsumer=function(e){return zo(e)===Mo},So.isContextProvider=function(e){return zo(e)===No},So.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===Po},So.isForwardRef=function(e){return zo(e)===Co},So.isFragment=function(e){return zo(e)===ko},So.isLazy=function(e){return zo(e)===Bo},So.isMemo=function(e){return zo(e)===Ro},So.isPortal=function(e){return zo(e)===Ao},So.isProfiler=function(e){return zo(e)===Eo},So.isStrictMode=function(e){return zo(e)===_o},So.isSuspense=function(e){return zo(e)===Do},So.isSuspenseList=function(e){return zo(e)===Io},So.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===ko||e===Eo||e===_o||e===Do||e===Io||e===Lo||"object"==typeof e&&null!==e&&(e.$$typeof===Bo||e.$$typeof===Ro||e.$$typeof===No||e.$$typeof===Mo||e.$$typeof===Co||e.$$typeof===jo||void 0!==e.getModuleId)},So.typeOf=zo,Oo.exports=So;var Fo=Oo.exports,$o=sr,Uo=cr;var Wo=function(e){return"number"==typeof e||Uo(e)&&"[object Number]"==$o(e)};const qo=y(Wo);var Ho=Wo;const Vo=y((function(e){return Ho(e)&&e!=+e}));var Yo=function(e){return 0===e?0:e>0?1:-1},Xo=function(e){return wo(e)&&e.indexOf("%")===e.length-1},Go=function(e){return qo(e)&&!Vo(e)},Ko=function(e){return Go(e)||wo(e)},Zo=0,Qo=function(e){var t=++Zo;return"".concat(e||"").concat(t)},Jo=function(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!Go(e)&&!wo(e))return r;if(Xo(e)){var o=e.indexOf("%");n=t*parseFloat(e.slice(0,o))/100}else n=+e;return Vo(n)&&(n=r),i&&n>t&&(n=t),n},ea=function(e){if(!e)return null;var t=Object.keys(e);return t&&t.length?e[t[0]]:null},ta=function(e,t){return Go(e)&&Go(t)?function(n){return e+n*(t-e)}:function(){return t}};function na(e,t,n){return e&&e.length?e.find((function(e){return e&&("function"==typeof t?t(e):mo(e,t))===n})):null}function ra(e,t){for(var n in e)if({}.hasOwnProperty.call(e,n)&&(!{}.hasOwnProperty.call(t,n)||e[n]!==t[n]))return!1;for(var r in t)if({}.hasOwnProperty.call(t,r)&&!{}.hasOwnProperty.call(e,r))return!1;return!0}function ia(e){return ia="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ia(e)}var oa=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],aa=["points","pathLength"],sa={svg:["viewBox","children"],polygon:aa,polyline:aa},ca=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],la=function(t,n){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if(e.isValidElement(t)&&(r=t.props),!gr(r))return null;var i={};return Object.keys(r).forEach((function(e){ca.includes(e)&&(i[e]=n||function(t){return r[e](r,t)})})),i},ua=function(e,t,n){if(!gr(e)||"object"!==ia(e))return null;var r=null;return Object.keys(e).forEach((function(i){var o=e[i];ca.includes(i)&&"function"==typeof o&&(r||(r={}),r[i]=function(e,t,n){return function(r){return e(t,n,r),null}}(o,t,n))})),r},fa=["children"],da=["children"];function pa(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ha(e){return ha="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ha(e)}var ya={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},ma=function(e){return"string"==typeof e?e:e?e.displayName||e.name||"Component":""},va=null,ga=null,ba=function t(n){if(n===va&&Array.isArray(ga))return ga;var r=[];return e.Children.forEach(n,(function(e){vo(e)||(Fo.isFragment(e)?r=r.concat(t(e.props.children)):r.push(e))})),ga=r,va=n,r};function xa(e,t){var n=[],r=[];return r=Array.isArray(t)?t.map((function(e){return ma(e)})):[ma(t)],ba(e).forEach((function(e){var t=mo(e,"type.displayName")||mo(e,"type.name");-1!==r.indexOf(t)&&n.push(e)})),n}function wa(e,t){var n=xa(e,t);return n&&n[0]}var ja=function(e){if(!e||!e.props)return!1;var t=e.props,n=t.width,r=t.height;return!(!Go(n)||n<=0||!Go(r)||r<=0)},Oa=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Sa=function(t,n,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var i=t;if(e.isValidElement(t)&&(i=t.props),!gr(i))return null;var o={};return Object.keys(i).forEach((function(e){var t;(function(e,t,n,r){var i,o=null!==(i=null==sa?void 0:sa[r])&&void 0!==i?i:[];return!jr(e)&&(r&&o.includes(t)||oa.includes(t))||n&&ca.includes(t)})(null===(t=i)||void 0===t?void 0:t[e],e,n,r)&&(o[e]=i[e])})),o},Pa=function t(n,r){if(n===r)return!0;var i=e.Children.count(n);if(i!==e.Children.count(r))return!1;if(0===i)return!0;if(1===i)return Aa(Array.isArray(n)?n[0]:n,Array.isArray(r)?r[0]:r);for(var o=0;o<i;o++){var a=n[o],s=r[o];if(Array.isArray(a)||Array.isArray(s)){if(!t(a,s))return!1}else if(!Aa(a,s))return!1}return!0},Aa=function(e,t){if(vo(e)&&vo(t))return!0;if(!vo(e)&&!vo(t)){var n=e.props||{},r=n.children,i=pa(n,fa),o=t.props||{},a=o.children,s=pa(o,da);return r&&a?ra(i,s)&&Pa(r,a):!r&&!a&&ra(i,s)}return!1},ka=function(e,t){var n=[],r={};return ba(e).forEach((function(e,i){if(function(e){return e&&e.type&&wo(e.type)&&Oa.indexOf(e.type)>=0}(e))n.push(e);else if(e){var o=ma(e.type),a=t[o]||{},s=a.handler,c=a.once;if(s&&(!c||!r[o])){var l=s(e,o,i);n.push(l),r[o]=!0}}})),n},_a=["children","width","height","viewBox","className","style","title","desc"];function Ea(){return Ea=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ea.apply(this,arguments)}function Na(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ma(e){var t=e.children,n=e.width,r=e.height,i=e.viewBox,o=e.className,a=e.style,s=e.title,c=e.desc,l=Na(e,_a),u=i||{width:n,height:r,x:0,y:0},f=m("recharts-surface",o);return v.createElement("svg",Ea({},Sa(l,!0,"svg"),{className:f,width:n,height:r,style:a,viewBox:"".concat(u.x," ").concat(u.y," ").concat(u.width," ").concat(u.height)}),v.createElement("title",null,s),v.createElement("desc",null,c),t)}var Ta=["children","className"];function Ca(){return Ca=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ca.apply(this,arguments)}function Da(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var Ia=v.forwardRef((function(e,t){var n=e.children,r=e.className,i=Da(e,Ta),o=m("recharts-layer",r);return v.createElement("g",Ca({className:o},Sa(i,!0),{ref:t}),n)})),Ra=function(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i]};var Ba=function(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var o=Array(i);++r<i;)o[r]=e[r+t];return o},La=Ba;var za=function(e,t,n){var r=e.length;return n=void 0===n?r:n,!t&&n>=r?e:La(e,t,n)},Fa=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var $a=function(e){return Fa.test(e)};var Ua=function(e){return e.split("")},Wa="\\ud800-\\udfff",qa="["+Wa+"]",Ha="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",Va="\\ud83c[\\udffb-\\udfff]",Ya="[^"+Wa+"]",Xa="(?:\\ud83c[\\udde6-\\uddff]){2}",Ga="[\\ud800-\\udbff][\\udc00-\\udfff]",Ka="(?:"+Ha+"|"+Va+")"+"?",Za="[\\ufe0e\\ufe0f]?",Qa=Za+Ka+("(?:\\u200d(?:"+[Ya,Xa,Ga].join("|")+")"+Za+Ka+")*"),Ja="(?:"+[Ya+Ha+"?",Ha,Xa,Ga,qa].join("|")+")",es=RegExp(Va+"(?="+Va+")|"+Ja+Qa,"g");var ts=function(e){return e.match(es)||[]},ns=Ua,rs=$a,is=ts;var os=function(e){return rs(e)?is(e):ns(e)},as=za,ss=$a,cs=os,ls=no;var us=function(e){return function(t){t=ls(t);var n=ss(t)?cs(t):void 0,r=n?n[0]:t.charAt(0),i=n?as(n,1).join(""):t.slice(1);return r[e]()+i}};const fs=y(us("toUpperCase"));function ds(e){return function(){return e}}const ps=Math.cos,hs=Math.sin,ys=Math.sqrt,ms=Math.PI,vs=2*ms,gs=Math.PI,bs=2*gs,xs=1e-6,ws=bs-xs;function js(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=arguments[t]+e[t]}class Os{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?js:function(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return js;const n=10**t;return function(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=Math.round(arguments[t]*n)/n+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,n,r){this._append`Q${+e},${+t},${this._x1=+n},${this._y1=+r}`}bezierCurveTo(e,t,n,r,i,o){this._append`C${+e},${+t},${+n},${+r},${this._x1=+i},${this._y1=+o}`}arcTo(e,t,n,r,i){if(e=+e,t=+t,n=+n,r=+r,(i=+i)<0)throw new Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,s=n-e,c=r-t,l=o-e,u=a-t,f=l*l+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>xs)if(Math.abs(u*s-c*l)>xs&&i){let d=n-o,p=r-a,h=s*s+c*c,y=d*d+p*p,m=Math.sqrt(h),v=Math.sqrt(f),g=i*Math.tan((gs-Math.acos((h+f-y)/(2*m*v)))/2),b=g/v,x=g/m;Math.abs(b-1)>xs&&this._append`L${e+b*l},${t+b*u}`,this._append`A${i},${i},0,0,${+(u*d>l*p)},${this._x1=e+x*s},${this._y1=t+x*c}`}else this._append`L${this._x1=e},${this._y1=t}`;else;}arc(e,t,n,r,i,o){if(e=+e,t=+t,o=!!o,(n=+n)<0)throw new Error(`negative radius: ${n}`);let a=n*Math.cos(r),s=n*Math.sin(r),c=e+a,l=t+s,u=1^o,f=o?r-i:i-r;null===this._x1?this._append`M${c},${l}`:(Math.abs(this._x1-c)>xs||Math.abs(this._y1-l)>xs)&&this._append`L${c},${l}`,n&&(f<0&&(f=f%bs+bs),f>ws?this._append`A${n},${n},0,1,${u},${e-a},${t-s}A${n},${n},0,1,${u},${this._x1=c},${this._y1=l}`:f>xs&&this._append`A${n},${n},0,${+(f>=gs)},${u},${this._x1=e+n*Math.cos(i)},${this._y1=t+n*Math.sin(i)}`)}rect(e,t,n,r){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${n=+n}v${+r}h${-n}Z`}toString(){return this._}}function Ss(e){let t=3;return e.digits=function(n){if(!arguments.length)return t;if(null==n)t=null;else{const e=Math.floor(n);if(!(e>=0))throw new RangeError(`invalid digits: ${n}`);t=e}return e},()=>new Os(t)}function Ps(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function As(e){this._context=e}function ks(e){return new As(e)}function _s(e){return e[0]}function Es(e){return e[1]}function Ns(e,t){var n=ds(!0),r=null,i=ks,o=null,a=Ss(s);function s(s){var c,l,u,f=(s=Ps(s)).length,d=!1;for(null==r&&(o=i(u=a())),c=0;c<=f;++c)!(c<f&&n(l=s[c],c,s))===d&&((d=!d)?o.lineStart():o.lineEnd()),d&&o.point(+e(l,c,s),+t(l,c,s));if(u)return o=null,u+""||null}return e="function"==typeof e?e:void 0===e?_s:ds(e),t="function"==typeof t?t:void 0===t?Es:ds(t),s.x=function(t){return arguments.length?(e="function"==typeof t?t:ds(+t),s):e},s.y=function(e){return arguments.length?(t="function"==typeof e?e:ds(+e),s):t},s.defined=function(e){return arguments.length?(n="function"==typeof e?e:ds(!!e),s):n},s.curve=function(e){return arguments.length?(i=e,null!=r&&(o=i(r)),s):i},s.context=function(e){return arguments.length?(null==e?r=o=null:o=i(r=e),s):r},s}function Ms(e,t,n){var r=null,i=ds(!0),o=null,a=ks,s=null,c=Ss(l);function l(l){var u,f,d,p,h,y=(l=Ps(l)).length,m=!1,v=new Array(y),g=new Array(y);for(null==o&&(s=a(h=c())),u=0;u<=y;++u){if(!(u<y&&i(p=l[u],u,l))===m)if(m=!m)f=u,s.areaStart(),s.lineStart();else{for(s.lineEnd(),s.lineStart(),d=u-1;d>=f;--d)s.point(v[d],g[d]);s.lineEnd(),s.areaEnd()}m&&(v[u]=+e(p,u,l),g[u]=+t(p,u,l),s.point(r?+r(p,u,l):v[u],n?+n(p,u,l):g[u]))}if(h)return s=null,h+""||null}function u(){return Ns().defined(i).curve(a).context(o)}return e="function"==typeof e?e:void 0===e?_s:ds(+e),t="function"==typeof t?t:ds(void 0===t?0:+t),n="function"==typeof n?n:void 0===n?Es:ds(+n),l.x=function(t){return arguments.length?(e="function"==typeof t?t:ds(+t),r=null,l):e},l.x0=function(t){return arguments.length?(e="function"==typeof t?t:ds(+t),l):e},l.x1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:ds(+e),l):r},l.y=function(e){return arguments.length?(t="function"==typeof e?e:ds(+e),n=null,l):t},l.y0=function(e){return arguments.length?(t="function"==typeof e?e:ds(+e),l):t},l.y1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:ds(+e),l):n},l.lineX0=l.lineY0=function(){return u().x(e).y(t)},l.lineY1=function(){return u().x(e).y(n)},l.lineX1=function(){return u().x(r).y(t)},l.defined=function(e){return arguments.length?(i="function"==typeof e?e:ds(!!e),l):i},l.curve=function(e){return arguments.length?(a=e,null!=o&&(s=a(o)),l):a},l.context=function(e){return arguments.length?(null==e?o=s=null:s=a(o=e),l):o},l}As.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}};class Ts{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}const Cs={draw(e,t){const n=ys(t/ms);e.moveTo(n,0),e.arc(0,0,n,0,vs)}},Ds={draw(e,t){const n=ys(t/5)/2;e.moveTo(-3*n,-n),e.lineTo(-n,-n),e.lineTo(-n,-3*n),e.lineTo(n,-3*n),e.lineTo(n,-n),e.lineTo(3*n,-n),e.lineTo(3*n,n),e.lineTo(n,n),e.lineTo(n,3*n),e.lineTo(-n,3*n),e.lineTo(-n,n),e.lineTo(-3*n,n),e.closePath()}},Is=ys(1/3),Rs=2*Is,Bs={draw(e,t){const n=ys(t/Rs),r=n*Is;e.moveTo(0,-n),e.lineTo(r,0),e.lineTo(0,n),e.lineTo(-r,0),e.closePath()}},Ls={draw(e,t){const n=ys(t),r=-n/2;e.rect(r,r,n,n)}},zs=hs(ms/10)/hs(7*ms/10),Fs=hs(vs/10)*zs,$s=-ps(vs/10)*zs,Us={draw(e,t){const n=ys(.8908130915292852*t),r=Fs*n,i=$s*n;e.moveTo(0,-n),e.lineTo(r,i);for(let o=1;o<5;++o){const t=vs*o/5,a=ps(t),s=hs(t);e.lineTo(s*n,-a*n),e.lineTo(a*r-s*i,s*r+a*i)}e.closePath()}},Ws=ys(3),qs={draw(e,t){const n=-ys(t/(3*Ws));e.moveTo(0,2*n),e.lineTo(-Ws*n,-n),e.lineTo(Ws*n,-n),e.closePath()}},Hs=-.5,Vs=ys(3)/2,Ys=1/ys(12),Xs=3*(Ys/2+1),Gs={draw(e,t){const n=ys(t/Xs),r=n/2,i=n*Ys,o=r,a=n*Ys+n,s=-o,c=a;e.moveTo(r,i),e.lineTo(o,a),e.lineTo(s,c),e.lineTo(Hs*r-Vs*i,Vs*r+Hs*i),e.lineTo(Hs*o-Vs*a,Vs*o+Hs*a),e.lineTo(Hs*s-Vs*c,Vs*s+Hs*c),e.lineTo(Hs*r+Vs*i,Hs*i-Vs*r),e.lineTo(Hs*o+Vs*a,Hs*a-Vs*o),e.lineTo(Hs*s+Vs*c,Hs*c-Vs*s),e.closePath()}};function Ks(){}function Zs(e,t,n){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+n)/6)}function Qs(e){this._context=e}function Js(e){this._context=e}function ec(e){this._context=e}function tc(e){this._context=e}function nc(e){return e<0?-1:1}function rc(e,t,n){var r=e._x1-e._x0,i=t-e._x1,o=(e._y1-e._y0)/(r||i<0&&-0),a=(n-e._y1)/(i||r<0&&-0),s=(o*i+a*r)/(r+i);return(nc(o)+nc(a))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs(s))||0}function ic(e,t){var n=e._x1-e._x0;return n?(3*(e._y1-e._y0)/n-t)/2:t}function oc(e,t,n){var r=e._x0,i=e._y0,o=e._x1,a=e._y1,s=(o-r)/3;e._context.bezierCurveTo(r+s,i+s*t,o-s,a-s*n,o,a)}function ac(e){this._context=e}function sc(e){this._context=new cc(e)}function cc(e){this._context=e}function lc(e){this._context=e}function uc(e){var t,n,r=e.length-1,i=new Array(r),o=new Array(r),a=new Array(r);for(i[0]=0,o[0]=2,a[0]=e[0]+2*e[1],t=1;t<r-1;++t)i[t]=1,o[t]=4,a[t]=4*e[t]+2*e[t+1];for(i[r-1]=2,o[r-1]=7,a[r-1]=8*e[r-1]+e[r],t=1;t<r;++t)n=i[t]/o[t-1],o[t]-=n,a[t]-=n*a[t-1];for(i[r-1]=a[r-1]/o[r-1],t=r-2;t>=0;--t)i[t]=(a[t]-i[t+1])/o[t];for(o[r-1]=(e[r]+i[r-1])/2,t=0;t<r-1;++t)o[t]=2*e[t+1]-i[t+1];return[i,o]}function fc(e,t){this._context=e,this._t=t}function dc(e,t){if((i=e.length)>1)for(var n,r,i,o=1,a=e[t[0]],s=a.length;o<i;++o)for(r=a,a=e[t[o]],n=0;n<s;++n)a[n][1]+=a[n][0]=isNaN(r[n][1])?r[n][0]:r[n][1]}function pc(e){for(var t=e.length,n=new Array(t);--t>=0;)n[t]=t;return n}function hc(e,t){return e[t]}function yc(e){const t=[];return t.key=e,t}function mc(e){return mc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mc(e)}Qs.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:Zs(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:Zs(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},Js.prototype={areaStart:Ks,areaEnd:Ks,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:Zs(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},ec.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+e)/6,r=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(n,r):this._context.moveTo(n,r);break;case 3:this._point=4;default:Zs(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},tc.prototype={areaStart:Ks,areaEnd:Ks,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}},ac.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:oc(this,this._t0,ic(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var n=NaN;if(t=+t,(e=+e)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,oc(this,ic(this,n=rc(this,e,t)),n);break;default:oc(this,this._t0,n=rc(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=n}}},(sc.prototype=Object.create(ac.prototype)).point=function(e,t){ac.prototype.point.call(this,t,e)},cc.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,n,r,i,o){this._context.bezierCurveTo(t,e,r,n,o,i)}},lc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,n=e.length;if(n)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===n)this._context.lineTo(e[1],t[1]);else for(var r=uc(e),i=uc(t),o=0,a=1;a<n;++o,++a)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],e[a],t[a]);(this._line||0!==this._line&&1===n)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},fc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var n=this._x*(1-this._t)+e*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,t)}}this._x=e,this._y=t}};var vc=["type","size","sizeType"];function gc(){return gc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gc.apply(this,arguments)}function bc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function xc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bc(Object(n),!0).forEach((function(t){wc(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function wc(e,t,n){return t=function(e){var t=function(e,t){if("object"!=mc(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=mc(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==mc(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jc(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var Oc={symbolCircle:Cs,symbolCross:Ds,symbolDiamond:Bs,symbolSquare:Ls,symbolStar:Us,symbolTriangle:qs,symbolWye:Gs},Sc=Math.PI/180,Pc=function(e){var t,n,r=e.type,i=void 0===r?"circle":r,o=e.size,a=void 0===o?64:o,s=e.sizeType,c=void 0===s?"area":s,l=xc(xc({},jc(e,vc)),{},{type:i,size:a,sizeType:c}),u=l.className,f=l.cx,d=l.cy,p=Sa(l,!0);return f===+f&&d===+d&&a===+a?v.createElement("path",gc({},p,{className:m("recharts-symbols",u),transform:"translate(".concat(f,", ").concat(d,")"),d:(t=function(e){var t="symbol".concat(fs(e));return Oc[t]||Cs}(i),n=function(e,t){let n=null,r=Ss(i);function i(){let i;if(n||(n=i=r()),e.apply(this,arguments).draw(n,+t.apply(this,arguments)),i)return n=null,i+""||null}return e="function"==typeof e?e:ds(e||Cs),t="function"==typeof t?t:ds(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:ds(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:ds(+e),i):t},i.context=function(e){return arguments.length?(n=null==e?null:e,i):n},i}().type(t).size(function(e,t,n){if("area"===t)return e;switch(n){case"cross":return 5*e*e/9;case"diamond":return.5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var r=18*Sc;return 1.25*e*e*(Math.tan(r)-Math.tan(2*r)*Math.pow(Math.tan(r),2));case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}}(a,c,i)),n())})):null};function Ac(e){return Ac="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ac(e)}function kc(){return kc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},kc.apply(this,arguments)}function _c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ec(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ic(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function Nc(e,t,n){return t=Tc(t),function(e,t){if(t&&("object"===Ac(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Mc()?Reflect.construct(t,n||[],Tc(e).constructor):t.apply(e,n))}function Mc(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Mc=function(){return!!e})()}function Tc(e){return Tc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Tc(e)}function Cc(e,t){return Cc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Cc(e,t)}function Dc(e,t,n){return(t=Ic(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ic(e){var t=function(e,t){if("object"!=Ac(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Ac(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Ac(t)?t:t+""}Pc.registerSymbol=function(e,t){Oc["symbol".concat(fs(e))]=t};var Rc=32,Bc=function(){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Nc(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Cc(e,t)}(t,e.PureComponent),Ec(t,[{key:"renderIcon",value:function(e){var t=this.props.inactiveColor,n=16,r=Rc/6,i=Rc/3,o=e.inactive?t:e.color;if("plainline"===e.type)return v.createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:n,x2:Rc,y2:n,className:"recharts-legend-icon"});if("line"===e.type)return v.createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(n,"h").concat(i,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*i,",").concat(n,"\n            H").concat(Rc,"M").concat(2*i,",").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(i,",").concat(n),className:"recharts-legend-icon"});if("rect"===e.type)return v.createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(Rc,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(v.isValidElement(e.legendIcon)){var a=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_c(Object(n),!0).forEach((function(t){Dc(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e);return delete a.legendIcon,v.cloneElement(e.legendIcon,a)}return v.createElement(Pc,{fill:o,cx:n,cy:n,size:Rc,sizeType:"diameter",type:e.type})}},{key:"renderItems",value:function(){var e=this,t=this.props,n=t.payload,r=t.iconSize,i=t.layout,o=t.formatter,a=t.inactiveColor,s={x:0,y:0,width:Rc,height:Rc},c={display:"horizontal"===i?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return n.map((function(t,n){var i=t.formatter||o,u=m(Dc(Dc({"recharts-legend-item":!0},"legend-item-".concat(n),!0),"inactive",t.inactive));if("none"===t.type)return null;var f=jr(t.value)?null:t.value;Ra(!jr(t.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=t.inactive?a:t.color;return v.createElement("li",kc({className:u,style:c,key:"legend-item-".concat(n)},ua(e.props,t,n)),v.createElement(Ma,{width:r,height:r,viewBox:s,style:l},e.renderIcon(t)),v.createElement("span",{className:"recharts-legend-item-text",style:{color:d}},i?i(f,t,n):f))}))}},{key:"render",value:function(){var e=this.props,t=e.payload,n=e.layout,r=e.align;if(!t||!t.length)return null;var i={padding:0,margin:0,textAlign:"horizontal"===n?r:"left"};return v.createElement("ul",{className:"recharts-default-legend",style:i},this.renderItems())}}])}();Dc(Bc,"displayName","Legend"),Dc(Bc,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var Lc=wi;var zc=wi,Fc=ji,$c=Li;var Uc=wi,Wc=function(){this.__data__=new Lc,this.size=0},qc=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Hc=function(e){return this.__data__.get(e)},Vc=function(e){return this.__data__.has(e)},Yc=function(e,t){var n=this.__data__;if(n instanceof zc){var r=n.__data__;if(!Fc||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new $c(r)}return n.set(e,t),this.size=n.size,this};function Xc(e){var t=this.__data__=new Uc(e);this.size=t.size}Xc.prototype.clear=Wc,Xc.prototype.delete=qc,Xc.prototype.get=Hc,Xc.prototype.has=Vc,Xc.prototype.set=Yc;var Gc=Xc;var Kc=Li,Zc=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},Qc=function(e){return this.__data__.has(e)};function Jc(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new Kc;++t<n;)this.add(e[t])}Jc.prototype.add=Jc.prototype.push=Zc,Jc.prototype.has=Qc;var el=Jc;var tl=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1};var nl=function(e,t){return e.has(t)},rl=el,il=tl,ol=nl;var al=function(e,t,n,r,i,o){var a=1&n,s=e.length,c=t.length;if(s!=c&&!(a&&c>s))return!1;var l=o.get(e),u=o.get(t);if(l&&u)return l==t&&u==e;var f=-1,d=!0,p=2&n?new rl:void 0;for(o.set(e,t),o.set(t,e);++f<s;){var h=e[f],y=t[f];if(r)var m=a?r(y,h,f,t,e,o):r(h,y,f,e,t,o);if(void 0!==m){if(m)continue;d=!1;break}if(p){if(!il(t,(function(e,t){if(!ol(p,t)&&(h===e||i(h,e,n,r,o)))return p.push(t)}))){d=!1;break}}else if(h!==y&&!i(h,y,n,r,o)){d=!1;break}}return o.delete(e),o.delete(t),d};var sl=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n};var cl=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n},ll=Gn.Uint8Array,ul=si,fl=al,dl=sl,pl=cl,hl=Kn?Kn.prototype:void 0,yl=hl?hl.valueOf:void 0;var ml=function(e,t,n,r,i,o,a){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!o(new ll(e),new ll(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return ul(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=dl;case"[object Set]":var c=1&r;if(s||(s=pl),e.size!=t.size&&!c)return!1;var l=a.get(e);if(l)return l==t;r|=2,a.set(e,t);var u=fl(s(e),s(t),r,i,o,a);return a.delete(e),u;case"[object Symbol]":if(yl)return yl.call(e)==yl.call(t)}return!1};var vl=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e},gl=vl,bl=Hn;var xl=function(e,t,n){var r=t(e);return bl(e)?r:gl(r,n(e))};var wl=function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,o=[];++n<r;){var a=e[n];t(a,n,e)&&(o[i++]=a)}return o};var jl=wl,Ol=function(){return[]},Sl=Object.prototype.propertyIsEnumerable,Pl=Object.getOwnPropertySymbols,Al=Pl?function(e){return null==e?[]:(e=Object(e),jl(Pl(e),(function(t){return Sl.call(e,t)})))}:Ol,kl=Al;var _l=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},El=sr,Nl=cr;var Ml=function(e){return Nl(e)&&"[object Arguments]"==El(e)},Tl=cr,Cl=Object.prototype,Dl=Cl.hasOwnProperty,Il=Cl.propertyIsEnumerable,Rl=Ml(function(){return arguments}())?Ml:function(e){return Tl(e)&&Dl.call(e,"callee")&&!Il.call(e,"callee")},Bl={exports:{}};var Ll=function(){return!1};!function(e,t){var n=Gn,r=Ll,i=t&&!t.nodeType&&t,o=i&&e&&!e.nodeType&&e,a=o&&o.exports===i?n.Buffer:void 0,s=(a?a.isBuffer:void 0)||r;e.exports=s}(Bl,Bl.exports);var zl=Bl.exports,Fl=/^(?:0|[1-9]\d*)$/;var $l=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&Fl.test(e))&&e>-1&&e%1==0&&e<t};var Ul=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},Wl=sr,ql=Ul,Hl=cr,Vl={};Vl["[object Float32Array]"]=Vl["[object Float64Array]"]=Vl["[object Int8Array]"]=Vl["[object Int16Array]"]=Vl["[object Int32Array]"]=Vl["[object Uint8Array]"]=Vl["[object Uint8ClampedArray]"]=Vl["[object Uint16Array]"]=Vl["[object Uint32Array]"]=!0,Vl["[object Arguments]"]=Vl["[object Array]"]=Vl["[object ArrayBuffer]"]=Vl["[object Boolean]"]=Vl["[object DataView]"]=Vl["[object Date]"]=Vl["[object Error]"]=Vl["[object Function]"]=Vl["[object Map]"]=Vl["[object Number]"]=Vl["[object Object]"]=Vl["[object RegExp]"]=Vl["[object Set]"]=Vl["[object String]"]=Vl["[object WeakMap]"]=!1;var Yl=function(e){return Hl(e)&&ql(e.length)&&!!Vl[Wl(e)]};var Xl=function(e){return function(t){return e(t)}},Gl={exports:{}};!function(e,t){var n=Vn,r=t&&!t.nodeType&&t,i=r&&e&&!e.nodeType&&e,o=i&&i.exports===r&&n.process,a=function(){try{var e=i&&i.require&&i.require("util").types;return e||o&&o.binding&&o.binding("util")}catch(_o){}}();e.exports=a}(Gl,Gl.exports);var Kl=Gl.exports,Zl=Yl,Ql=Xl,Jl=Kl&&Kl.isTypedArray,eu=Jl?Ql(Jl):Zl,tu=_l,nu=Rl,ru=Hn,iu=zl,ou=$l,au=eu,su=Object.prototype.hasOwnProperty;var cu=function(e,t){var n=ru(e),r=!n&&nu(e),i=!n&&!r&&iu(e),o=!n&&!r&&!i&&au(e),a=n||r||i||o,s=a?tu(e.length,String):[],c=s.length;for(var l in e)!t&&!su.call(e,l)||a&&("length"==l||i&&("offset"==l||"parent"==l)||o&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||ou(l,c))||s.push(l);return s},lu=Object.prototype;var uu=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||lu)};var fu=function(e,t){return function(n){return e(t(n))}},du=fu(Object.keys,Object),pu=uu,hu=du,yu=Object.prototype.hasOwnProperty;var mu=function(e){if(!pu(e))return hu(e);var t=[];for(var n in Object(e))yu.call(e,n)&&"constructor"!=n&&t.push(n);return t},vu=wr,gu=Ul;var bu=function(e){return null!=e&&gu(e.length)&&!vu(e)},xu=cu,wu=mu,ju=bu;var Ou=function(e){return ju(e)?xu(e):wu(e)},Su=xl,Pu=kl,Au=Ou;var ku=function(e){return Su(e,Au,Pu)},_u=ku,Eu=Object.prototype.hasOwnProperty;var Nu=function(e,t,n,r,i,o){var a=1&n,s=_u(e),c=s.length;if(c!=_u(t).length&&!a)return!1;for(var l=c;l--;){var u=s[l];if(!(a?u in t:Eu.call(t,u)))return!1}var f=o.get(e),d=o.get(t);if(f&&d)return f==t&&d==e;var p=!0;o.set(e,t),o.set(t,e);for(var h=a;++l<c;){var y=e[u=s[l]],m=t[u];if(r)var v=a?r(m,y,u,t,e,o):r(y,m,u,e,t,o);if(!(void 0===v?y===m||i(y,m,n,r,o):v)){p=!1;break}h||(h="constructor"==u)}if(p&&!h){var g=e.constructor,b=t.constructor;g==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof g&&g instanceof g&&"function"==typeof b&&b instanceof b||(p=!1)}return o.delete(e),o.delete(t),p},Mu=Ur(Gn,"DataView"),Tu=Ur(Gn,"Promise"),Cu=Ur(Gn,"Set"),Du=Mu,Iu=ji,Ru=Tu,Bu=Cu,Lu=Ur(Gn,"WeakMap"),zu=sr,Fu=_r,$u="[object Map]",Uu="[object Promise]",Wu="[object Set]",qu="[object WeakMap]",Hu="[object DataView]",Vu=Fu(Du),Yu=Fu(Iu),Xu=Fu(Ru),Gu=Fu(Bu),Ku=Fu(Lu),Zu=zu;(Du&&Zu(new Du(new ArrayBuffer(1)))!=Hu||Iu&&Zu(new Iu)!=$u||Ru&&Zu(Ru.resolve())!=Uu||Bu&&Zu(new Bu)!=Wu||Lu&&Zu(new Lu)!=qu)&&(Zu=function(e){var t=zu(e),n="[object Object]"==t?e.constructor:void 0,r=n?Fu(n):"";if(r)switch(r){case Vu:return Hu;case Yu:return $u;case Xu:return Uu;case Gu:return Wu;case Ku:return qu}return t});var Qu=Gc,Ju=al,ef=ml,tf=Nu,nf=Zu,rf=Hn,of=zl,af=eu,sf="[object Arguments]",cf="[object Array]",lf="[object Object]",uf=Object.prototype.hasOwnProperty;var ff=function(e,t,n,r,i,o){var a=rf(e),s=rf(t),c=a?cf:nf(e),l=s?cf:nf(t),u=(c=c==sf?lf:c)==lf,f=(l=l==sf?lf:l)==lf,d=c==l;if(d&&of(e)){if(!of(t))return!1;a=!0,u=!1}if(d&&!u)return o||(o=new Qu),a||af(e)?Ju(e,t,n,r,i,o):ef(e,t,c,n,r,i,o);if(!(1&n)){var p=u&&uf.call(e,"__wrapped__"),h=f&&uf.call(t,"__wrapped__");if(p||h){var y=p?e.value():e,m=h?t.value():t;return o||(o=new Qu),i(y,m,n,r,o)}}return!!d&&(o||(o=new Qu),tf(e,t,n,r,i,o))},df=ff,pf=cr;var hf=function e(t,n,r,i,o){return t===n||(null==t||null==n||!pf(t)&&!pf(n)?t!=t&&n!=n:df(t,n,r,i,e,o))},yf=Gc,mf=hf;var vf=function(e,t,n,r){var i=n.length,o=i,a=!r;if(null==e)return!o;for(e=Object(e);i--;){var s=n[i];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<o;){var c=(s=n[i])[0],l=e[c],u=s[1];if(a&&s[2]){if(void 0===l&&!(c in e))return!1}else{var f=new yf;if(r)var d=r(l,u,c,e,t,f);if(!(void 0===d?mf(u,l,3,r,f):d))return!1}}return!0},gf=vr;var bf=function(e){return e==e&&!gf(e)},xf=bf,wf=Ou;var jf=function(e){for(var t=wf(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,xf(i)]}return t};var Of=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}},Sf=vf,Pf=jf,Af=Of;var kf=function(e){var t=Pf(e);return 1==t.length&&t[0][2]?Af(t[0][0],t[0][1]):function(n){return n===e||Sf(n,e,t)}};var _f=function(e,t){return null!=e&&t in Object(e)},Ef=so,Nf=Rl,Mf=Hn,Tf=$l,Cf=Ul,Df=lo;var If=function(e,t,n){for(var r=-1,i=(t=Ef(t,e)).length,o=!1;++r<i;){var a=Df(t[r]);if(!(o=null!=e&&n(e,a)))break;e=e[a]}return o||++r!=i?o:!!(i=null==e?0:e.length)&&Cf(i)&&Tf(a,i)&&(Mf(e)||Nf(e))},Rf=_f,Bf=If;var Lf=function(e,t){return null!=e&&Bf(e,t,Rf)},zf=hf,Ff=yo,$f=Lf,Uf=mr,Wf=bf,qf=Of,Hf=lo;var Vf=function(e,t){return Uf(e)&&Wf(t)?qf(Hf(e),t):function(n){var r=Ff(n,e);return void 0===r&&r===t?$f(n,e):zf(t,r,3)}};var Yf=function(e){return e};var Xf=function(e){return function(t){return null==t?void 0:t[e]}},Gf=po;var Kf=function(e){return function(t){return Gf(t,e)}},Zf=Xf,Qf=Kf,Jf=mr,ed=lo;var td=kf,nd=Vf,rd=Yf,id=Hn,od=function(e){return Jf(e)?Zf(ed(e)):Qf(e)};var ad=function(e){return"function"==typeof e?e:null==e?rd:"object"==typeof e?id(e)?nd(e[0],e[1]):td(e):od(e)};var sd=function(e,t,n,r){for(var i=e.length,o=n+(r?1:-1);r?o--:++o<i;)if(t(e[o],o,e))return o;return-1};var cd=function(e,t,n){for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1},ld=sd,ud=function(e){return e!=e},fd=cd;var dd=function(e,t,n){return t==t?fd(e,t,n):ld(e,ud,n)},pd=dd;var hd=function(e,t){return!!(null==e?0:e.length)&&pd(e,t,0)>-1};var yd=function(e,t,n){for(var r=-1,i=null==e?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1};var md=Cu,vd=function(){},gd=md&&1/cl(new md([,-0]))[1]==1/0?function(e){return new md(e)}:vd,bd=el,xd=hd,wd=yd,jd=nl,Od=gd,Sd=cl;var Pd=function(e,t,n){var r=-1,i=xd,o=e.length,a=!0,s=[],c=s;if(n)a=!1,i=wd;else if(o>=200){var l=t?null:Od(e);if(l)return Sd(l);a=!1,i=jd,c=new bd}else c=t?[]:s;e:for(;++r<o;){var u=e[r],f=t?t(u):u;if(u=n||0!==u?u:0,a&&f==f){for(var d=c.length;d--;)if(c[d]===f)continue e;t&&c.push(f),s.push(u)}else i(c,f,n)||(c!==s&&c.push(f),s.push(u))}return s},Ad=ad,kd=Pd;var _d=function(e,t){return e&&e.length?kd(e,Ad(t)):[]};const Ed=y(_d);function Nd(e,t,n){return!0===t?Ed(e,n):jr(t)?Ed(e,t):e}function Md(e){return Md="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Md(e)}var Td=["ref"];function Cd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Dd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Cd(Object(n),!0).forEach((function(t){Fd(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Cd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Id(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,$d(r.key),r)}}function Rd(e,t,n){return t=Ld(t),function(e,t){if(t&&("object"===Md(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Bd()?Reflect.construct(t,n||[],Ld(e).constructor):t.apply(e,n))}function Bd(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Bd=function(){return!!e})()}function Ld(e){return Ld=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ld(e)}function zd(e,t){return zd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zd(e,t)}function Fd(e,t,n){return(t=$d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $d(e){var t=function(e,t){if("object"!=Md(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Md(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Md(t)?t:t+""}function Ud(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Wd(e){return e.value}var qd=function(){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return Fd(e=Rd(this,t,[].concat(r)),"lastBoundingBox",{width:-1,height:-1}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zd(e,t)}(t,e.PureComponent),n=t,i=[{key:"getWithHeight",value:function(e,t){var n=Dd(Dd({},this.defaultProps),e.props).layout;return"vertical"===n&&Go(e.props.height)?{height:e.props.height}:"horizontal"===n?{width:e.props.width||t}:null}}],(r=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();return e.height=this.wrapperNode.offsetHeight,e.width=this.wrapperNode.offsetWidth,e}return null}},{key:"updateBBox",value:function(){var e=this.props.onBBoxUpdate,t=this.getBBox();t?(Math.abs(t.width-this.lastBoundingBox.width)>1||Math.abs(t.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=t.width,this.lastBoundingBox.height=t.height,e&&e(t)):-1===this.lastBoundingBox.width&&-1===this.lastBoundingBox.height||(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,e&&e(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Dd({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(e){var t,n,r=this.props,i=r.layout,o=r.align,a=r.verticalAlign,s=r.margin,c=r.chartWidth,l=r.chartHeight;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(t="center"===o&&"vertical"===i?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:s&&s.right||0}:{left:s&&s.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(n="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:s&&s.bottom||0}:{top:s&&s.top||0}),Dd(Dd({},t),n)}},{key:"render",value:function(){var e=this,t=this.props,n=t.content,r=t.width,i=t.height,o=t.wrapperStyle,a=t.payloadUniqBy,s=t.payload,c=Dd(Dd({position:"absolute",width:r||"auto",height:i||"auto"},this.getDefaultPosition(o)),o);return v.createElement("div",{className:"recharts-legend-wrapper",style:c,ref:function(t){e.wrapperNode=t}},function(e,t){if(v.isValidElement(e))return v.cloneElement(e,t);if("function"==typeof e)return v.createElement(e,t);t.ref;var n=Ud(t,Td);return v.createElement(Bc,n)}(n,Dd(Dd({},this.props),{},{payload:Nd(s,a,Wd)})))}}])&&Id(n.prototype,r),i&&Id(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();Fd(qd,"displayName","Legend"),Fd(qd,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var Hd=Rl,Vd=Hn,Yd=Kn?Kn.isConcatSpreadable:void 0;var Xd=vl,Gd=function(e){return Vd(e)||Hd(e)||!!(Yd&&e&&e[Yd])};var Kd=function e(t,n,r,i,o){var a=-1,s=t.length;for(r||(r=Gd),o||(o=[]);++a<s;){var c=t[a];n>0&&r(c)?n>1?e(c,n-1,r,i,o):Xd(o,c):i||(o[o.length]=c)}return o};var Zd=function(e){return function(t,n,r){for(var i=-1,o=Object(t),a=r(t),s=a.length;s--;){var c=a[e?s:++i];if(!1===n(o[c],c,o))break}return t}},Qd=Zd(),Jd=Ou;var ep=function(e,t){return e&&Qd(e,t,Jd)},tp=bu;var np=function(e,t){return function(n,r){if(null==n)return n;if(!tp(n))return e(n,r);for(var i=n.length,o=t?i:-1,a=Object(n);(t?o--:++o<i)&&!1!==r(a[o],o,a););return n}}(ep),rp=np,ip=bu;var op=function(e,t){var n=-1,r=ip(e)?Array(e.length):[];return rp(e,(function(e,i,o){r[++n]=t(e,i,o)})),r};var ap=function(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e},sp=fr;var cp=function(e,t){if(e!==t){var n=void 0!==e,r=null===e,i=e==e,o=sp(e),a=void 0!==t,s=null===t,c=t==t,l=sp(t);if(!s&&!l&&!o&&e>t||o&&a&&c&&!s&&!l||r&&a&&c||!n&&c||!i)return 1;if(!r&&!o&&!l&&e<t||l&&n&&i&&!r&&!o||s&&n&&i||!a&&i||!c)return-1}return 0};var lp=function(e,t,n){for(var r=-1,i=e.criteria,o=t.criteria,a=i.length,s=n.length;++r<a;){var c=cp(i[r],o[r]);if(c)return r>=s?c:c*("desc"==n[r]?-1:1)}return e.index-t.index},up=Xi,fp=po,dp=ad,pp=op,hp=ap,yp=Xl,mp=lp,vp=Yf,gp=Hn;var bp=function(e,t,n){t=t.length?up(t,(function(e){return gp(e)?function(t){return fp(t,1===e.length?e[0]:e)}:e})):[vp];var r=-1;t=up(t,yp(dp));var i=pp(e,(function(e,n,i){return{criteria:up(t,(function(t){return t(e)})),index:++r,value:e}}));return hp(i,(function(e,t){return mp(e,t,n)}))};var xp=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)},wp=xp,jp=Math.max;var Op=function(e,t,n){return t=jp(void 0===t?e.length-1:t,0),function(){for(var r=arguments,i=-1,o=jp(r.length-t,0),a=Array(o);++i<o;)a[i]=r[t+i];i=-1;for(var s=Array(t+1);++i<t;)s[i]=r[i];return s[t]=n(a),wp(e,this,s)}};var Sp=function(e){return function(){return e}},Pp=Ur,Ap=function(){try{var e=Pp(Object,"defineProperty");return e({},"",{}),e}catch(_o){}}(),kp=Sp,_p=Ap,Ep=_p?function(e,t){return _p(e,"toString",{configurable:!0,enumerable:!1,value:kp(t),writable:!0})}:Yf,Np=Ep,Mp=Date.now;var Tp=function(e){var t=0,n=0;return function(){var r=Mp(),i=16-(r-n);if(n=r,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(Np),Cp=Yf,Dp=Op,Ip=Tp;var Rp=si,Bp=bu,Lp=$l,zp=vr;var Fp=function(e,t,n){if(!zp(n))return!1;var r=typeof t;return!!("number"==r?Bp(n)&&Lp(t,n.length):"string"==r&&t in n)&&Rp(n[t],e)},$p=Kd,Up=bp,Wp=Fp;const qp=y(function(e,t){return Ip(Dp(e,t,Cp),e+"")}((function(e,t){if(null==e)return[];var n=t.length;return n>1&&Wp(e,t[0],t[1])?t=[]:n>2&&Wp(t[0],t[1],t[2])&&(t=[t[0]]),Up(e,$p(t,1),[])})));function Hp(e){return Hp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Hp(e)}function Vp(){return Vp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vp.apply(this,arguments)}function Yp(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Xp(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xp(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Xp(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Gp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Kp(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gp(Object(n),!0).forEach((function(t){Zp(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Zp(e,t,n){return t=function(e){var t=function(e,t){if("object"!=Hp(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Hp(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Hp(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qp(e){return Array.isArray(e)&&Ko(e[0])&&Ko(e[1])?e.join(" ~ "):e}var Jp=function(e){var t=e.separator,n=void 0===t?" : ":t,r=e.contentStyle,i=void 0===r?{}:r,o=e.itemStyle,a=void 0===o?{}:o,s=e.labelStyle,c=void 0===s?{}:s,l=e.payload,u=e.formatter,f=e.itemSorter,d=e.wrapperClassName,p=e.labelClassName,h=e.label,y=e.labelFormatter,g=e.accessibilityLayer,b=void 0!==g&&g,x=Kp({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},i),w=Kp({margin:0},c),j=!vo(h),O=j?h:"",S=m("recharts-default-tooltip",d),P=m("recharts-tooltip-label",p);j&&y&&null!=l&&(O=y(h,l));var A=b?{role:"status","aria-live":"assertive"}:{};return v.createElement("div",Vp({className:S,style:x},A),v.createElement("p",{className:P,style:w},v.isValidElement(O)?O:"".concat(O)),function(){if(l&&l.length){var e=(f?qp(l,f):l).map((function(e,t){if("none"===e.type)return null;var r=Kp({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},a),i=e.formatter||u||Qp,o=e.value,s=e.name,c=o,f=s;if(i&&null!=c&&null!=f){var d=i(o,s,e,t,l);if(Array.isArray(d)){var p=Yp(d,2);c=p[0],f=p[1]}else c=d}return v.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(t),style:r},Ko(f)?v.createElement("span",{className:"recharts-tooltip-item-name"},f):null,Ko(f)?v.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,v.createElement("span",{className:"recharts-tooltip-item-value"},c),v.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))}));return v.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null}())};function eh(e){return eh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},eh(e)}function th(e,t,n){return t=function(e){var t=function(e,t){if("object"!=eh(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=eh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eh(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var nh="recharts-tooltip-wrapper",rh={visibility:"hidden"};function ih(e){var t=e.coordinate,n=e.translateX,r=e.translateY;return m(nh,th(th(th(th({},"".concat(nh,"-right"),Go(n)&&t&&Go(t.x)&&n>=t.x),"".concat(nh,"-left"),Go(n)&&t&&Go(t.x)&&n<t.x),"".concat(nh,"-bottom"),Go(r)&&t&&Go(t.y)&&r>=t.y),"".concat(nh,"-top"),Go(r)&&t&&Go(t.y)&&r<t.y))}function oh(e){var t=e.allowEscapeViewBox,n=e.coordinate,r=e.key,i=e.offsetTopLeft,o=e.position,a=e.reverseDirection,s=e.tooltipDimension,c=e.viewBox,l=e.viewBoxDimension;if(o&&Go(o[r]))return o[r];var u=n[r]-s-i,f=n[r]+i;return t[r]?a[r]?u:f:a[r]?u<c[r]?Math.max(f,c[r]):Math.max(u,c[r]):f+s>c[r]+l?Math.max(u,c[r]):Math.max(f,c[r])}function ah(e){return ah="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ah(e)}function sh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ch(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?sh(Object(n),!0).forEach((function(t){hh(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sh(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function lh(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,yh(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function uh(e,t,n){return t=dh(t),function(e,t){if(t&&("object"===ah(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,fh()?Reflect.construct(t,n||[],dh(e).constructor):t.apply(e,n))}function fh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(fh=function(){return!!e})()}function dh(e){return dh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},dh(e)}function ph(e,t){return ph=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ph(e,t)}function hh(e,t,n){return(t=yh(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yh(e){var t=function(e,t){if("object"!=ah(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=ah(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ah(t)?t:t+""}var mh=function(){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return hh(e=uh(this,t,[].concat(r)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),hh(e,"handleKeyDown",(function(t){var n,r,i,o;"Escape"===t.key&&e.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(n=null===(r=e.props.coordinate)||void 0===r?void 0:r.x)&&void 0!==n?n:0,y:null!==(i=null===(o=e.props.coordinate)||void 0===o?void 0:o.y)&&void 0!==i?i:0}})})),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ph(e,t)}(t,e.PureComponent),lh(t,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var e=this.wrapperNode.getBoundingClientRect();(Math.abs(e.width-this.state.lastBoundingBox.width)>1||Math.abs(e.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:e.width,height:e.height}})}else-1===this.state.lastBoundingBox.width&&-1===this.state.lastBoundingBox.height||this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var e,t;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(e=this.props.coordinate)||void 0===e?void 0:e.x)===this.state.dismissedAtCoordinate.x&&(null===(t=this.props.coordinate)||void 0===t?void 0:t.y)===this.state.dismissedAtCoordinate.y||(this.state.dismissed=!1))}},{key:"render",value:function(){var e=this,t=this.props,n=t.active,r=t.allowEscapeViewBox,i=t.animationDuration,o=t.animationEasing,a=t.children,s=t.coordinate,c=t.hasPayload,l=t.isAnimationActive,u=t.offset,f=t.position,d=t.reverseDirection,p=t.useTranslate3d,h=t.viewBox,y=t.wrapperStyle,m=function(e){var t,n,r=e.allowEscapeViewBox,i=e.coordinate,o=e.offsetTopLeft,a=e.position,s=e.reverseDirection,c=e.tooltipBox,l=e.useTranslate3d,u=e.viewBox;return{cssProperties:c.height>0&&c.width>0&&i?function(e){var t=e.translateX,n=e.translateY;return{transform:e.useTranslate3d?"translate3d(".concat(t,"px, ").concat(n,"px, 0)"):"translate(".concat(t,"px, ").concat(n,"px)")}}({translateX:t=oh({allowEscapeViewBox:r,coordinate:i,key:"x",offsetTopLeft:o,position:a,reverseDirection:s,tooltipDimension:c.width,viewBox:u,viewBoxDimension:u.width}),translateY:n=oh({allowEscapeViewBox:r,coordinate:i,key:"y",offsetTopLeft:o,position:a,reverseDirection:s,tooltipDimension:c.height,viewBox:u,viewBoxDimension:u.height}),useTranslate3d:l}):rh,cssClasses:ih({translateX:t,translateY:n,coordinate:i})}}({allowEscapeViewBox:r,coordinate:s,offsetTopLeft:u,position:f,reverseDirection:d,tooltipBox:this.state.lastBoundingBox,useTranslate3d:p,viewBox:h}),g=m.cssClasses,b=m.cssProperties,x=ch(ch({transition:l&&n?"transform ".concat(i,"ms ").concat(o):void 0},b),{},{pointerEvents:"none",visibility:!this.state.dismissed&&n&&c?"visible":"hidden",position:"absolute",top:0,left:0},y);return v.createElement("div",{tabIndex:-1,className:g,style:x,ref:function(t){e.wrapperNode=t}},a)}}])}(),vh={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(e){return vh[e]},set:function(e,t){if("string"==typeof e)vh[e]=t;else{var n=Object.keys(e);n&&n.length&&n.forEach((function(t){vh[t]=e[t]}))}}};function gh(e){return gh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},gh(e)}function bh(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function xh(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bh(Object(n),!0).forEach((function(t){Ah(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bh(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function wh(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,kh(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function jh(e,t,n){return t=Sh(t),function(e,t){if(t&&("object"===gh(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,Oh()?Reflect.construct(t,n||[],Sh(e).constructor):t.apply(e,n))}function Oh(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(Oh=function(){return!!e})()}function Sh(e){return Sh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Sh(e)}function Ph(e,t){return Ph=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Ph(e,t)}function Ah(e,t,n){return(t=kh(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kh(e){var t=function(e,t){if("object"!=gh(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=gh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==gh(t)?t:t+""}function _h(e){return e.dataKey}var Eh=function(){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),jh(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Ph(e,t)}(t,e.PureComponent),wh(t,[{key:"render",value:function(){var e=this,t=this.props,n=t.active,r=t.allowEscapeViewBox,i=t.animationDuration,o=t.animationEasing,a=t.content,s=t.coordinate,c=t.filterNull,l=t.isAnimationActive,u=t.offset,f=t.payload,d=t.payloadUniqBy,p=t.position,h=t.reverseDirection,y=t.useTranslate3d,m=t.viewBox,g=t.wrapperStyle,b=null!=f?f:[];c&&b.length&&(b=Nd(f.filter((function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)})),d,_h));var x=b.length>0;return v.createElement(mh,{allowEscapeViewBox:r,animationDuration:i,animationEasing:o,isAnimationActive:l,active:n,coordinate:s,hasPayload:x,offset:u,position:p,reverseDirection:h,useTranslate3d:y,viewBox:m,wrapperStyle:g},function(e,t){return v.isValidElement(e)?v.cloneElement(e,t):"function"==typeof e?v.createElement(e,t):v.createElement(Jp,t)}(a,xh(xh({},this.props),{},{payload:b})))}}])}();Ah(Eh,"displayName","Tooltip"),Ah(Eh,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!vh.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var Nh=Gn,Mh=/\s/;var Th=function(e){for(var t=e.length;t--&&Mh.test(e.charAt(t)););return t},Ch=Th,Dh=/^\s+/;var Ih=function(e){return e?e.slice(0,Ch(e)+1).replace(Dh,""):e},Rh=Ih,Bh=vr,Lh=fr,zh=/^[-+]0x[0-9a-f]+$/i,Fh=/^0b[01]+$/i,$h=/^0o[0-7]+$/i,Uh=parseInt;var Wh=function(e){if("number"==typeof e)return e;if(Lh(e))return NaN;if(Bh(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=Bh(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Rh(e);var n=Fh.test(e);return n||$h.test(e)?Uh(e.slice(2),n?2:8):zh.test(e)?NaN:+e},qh=vr,Hh=function(){return Nh.Date.now()},Vh=Wh,Yh=Math.max,Xh=Math.min;var Gh=function(e,t,n){var r,i,o,a,s,c,l=0,u=!1,f=!1,d=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function p(t){var n=r,o=i;return r=i=void 0,l=t,a=e.apply(o,n)}function h(e){var n=e-c;return void 0===c||n>=t||n<0||f&&e-l>=o}function y(){var e=Hh();if(h(e))return m(e);s=setTimeout(y,function(e){var n=t-(e-c);return f?Xh(n,o-(e-l)):n}(e))}function m(e){return s=void 0,d&&r?p(e):(r=i=void 0,a)}function v(){var e=Hh(),n=h(e);if(r=arguments,i=this,c=e,n){if(void 0===s)return function(e){return l=e,s=setTimeout(y,t),u?p(e):a}(c);if(f)return clearTimeout(s),s=setTimeout(y,t),p(c)}return void 0===s&&(s=setTimeout(y,t)),a}return t=Vh(t)||0,qh(n)&&(u=!!n.leading,o=(f="maxWait"in n)?Yh(Vh(n.maxWait)||0,t):o,d="trailing"in n?!!n.trailing:d),v.cancel=function(){void 0!==s&&clearTimeout(s),l=0,r=c=i=s=void 0},v.flush=function(){return void 0===s?a:m(Hh())},v},Kh=Gh,Zh=vr;const Qh=y((function(e,t,n){var r=!0,i=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return Zh(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),Kh(e,t,{leading:r,maxWait:t,trailing:i})}));function Jh(e){return Jh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jh(e)}function ey(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ty(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ey(Object(n),!0).forEach((function(t){ny(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ey(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ny(e,t,n){return t=function(e){var t=function(e,t){if("object"!=Jh(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Jh(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Jh(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ry(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return iy(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return iy(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function iy(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var oy=e.forwardRef((function(t,n){var r=t.aspect,i=t.initialDimension,o=void 0===i?{width:-1,height:-1}:i,a=t.width,s=void 0===a?"100%":a,c=t.height,l=void 0===c?"100%":c,u=t.minWidth,f=void 0===u?0:u,d=t.minHeight,p=t.maxHeight,h=t.children,y=t.debounce,g=void 0===y?0:y,b=t.id,x=t.className,w=t.onResize,j=t.style,O=void 0===j?{}:j,S=e.useRef(null),P=e.useRef();P.current=w,e.useImperativeHandle(n,(function(){return Object.defineProperty(S.current,"current",{get:function(){return S.current},configurable:!0})}));var A=ry(e.useState({containerWidth:o.width,containerHeight:o.height}),2),k=A[0],_=A[1],E=e.useCallback((function(e,t){_((function(n){var r=Math.round(e),i=Math.round(t);return n.containerWidth===r&&n.containerHeight===i?n:{containerWidth:r,containerHeight:i}}))}),[]);e.useEffect((function(){var e=function(e){var t,n=e[0].contentRect,r=n.width,i=n.height;E(r,i),null===(t=P.current)||void 0===t||t.call(P,r,i)};g>0&&(e=Qh(e,g,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),n=S.current.getBoundingClientRect(),r=n.width,i=n.height;return E(r,i),t.observe(S.current),function(){t.disconnect()}}),[E,g]);var N=e.useMemo((function(){var t=k.containerWidth,n=k.containerHeight;if(t<0||n<0)return null;Ra(Xo(s)||Xo(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",s,l),Ra(!r||r>0,"The aspect(%s) must be greater than zero.",r);var i=Xo(s)?t:s,o=Xo(l)?n:l;r&&r>0&&(i?o=i/r:o&&(i=o*r),p&&o>p&&(o=p)),Ra(i>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",i,o,s,l,f,d,r);var a=!Array.isArray(h)&&ma(h.type).endsWith("Chart");return v.Children.map(h,(function(t){return Fo.isElement(t)?e.cloneElement(t,ty({width:i,height:o},a?{style:ty({height:"100%",width:"100%",maxHeight:o,maxWidth:i},t.props.style)}:{})):t}))}),[r,h,l,p,d,f,k,s]);return v.createElement("div",{id:b?"".concat(b):void 0,className:m("recharts-responsive-container",x),style:ty(ty({},O),{},{width:s,height:l,minWidth:f,minHeight:d,maxHeight:p}),ref:S},N)})),ay=function(e){return null};function sy(e){return sy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sy(e)}function cy(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ly(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cy(Object(n),!0).forEach((function(t){uy(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cy(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function uy(e,t,n){return t=function(e){var t=function(e,t){if("object"!=sy(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=sy(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sy(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}ay.displayName="Cell";var fy={widthCache:{},cacheCount:0},dy={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},py="recharts_measurement_span";var hy=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||vh.isSsr)return{width:0,height:0};var n,r=(n=ly({},t),Object.keys(n).forEach((function(e){n[e]||delete n[e]})),n),i=JSON.stringify({text:e,copyStyle:r});if(fy.widthCache[i])return fy.widthCache[i];try{var o=document.getElementById(py);o||((o=document.createElement("span")).setAttribute("id",py),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=ly(ly({},dy),r);Object.assign(o.style,a),o.textContent="".concat(e);var s=o.getBoundingClientRect(),c={width:s.width,height:s.height};return fy.widthCache[i]=c,++fy.cacheCount>2e3&&(fy.cacheCount=0,fy.widthCache={}),c}catch(_o){return{width:0,height:0}}};function yy(e){return yy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yy(e)}function my(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return vy(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return vy(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function vy(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function gy(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,by(r.key),r)}}function by(e){var t=function(e,t){if("object"!=yy(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=yy(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==yy(t)?t:t+""}var xy=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,wy=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,jy=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,Oy=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,Sy={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},Py=Object.keys(Sy),Ay="NaN";var ky=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.num=t,this.unit=n,this.num=t,this.unit=n,Number.isNaN(t)&&(this.unit=""),""===n||jy.test(n)||(this.num=NaN,this.unit=""),Py.includes(n)&&(this.num=function(e,t){return e*Sy[t]}(t,n),this.unit="px")}return t=e,r=[{key:"parse",value:function(t){var n,r=my(null!==(n=Oy.exec(t))&&void 0!==n?n:[],3),i=r[1],o=r[2];return new e(parseFloat(i),null!=o?o:"")}}],(n=[{key:"add",value:function(t){return this.unit!==t.unit?new e(NaN,""):new e(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new e(NaN,""):new e(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new e(NaN,""):new e(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new e(NaN,""):new e(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}])&&gy(t.prototype,n),r&&gy(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();function _y(e){if(e.includes(Ay))return Ay;for(var t=e;t.includes("*")||t.includes("/");){var n,r=my(null!==(n=xy.exec(t))&&void 0!==n?n:[],4),i=r[1],o=r[2],a=r[3],s=ky.parse(null!=i?i:""),c=ky.parse(null!=a?a:""),l="*"===o?s.multiply(c):s.divide(c);if(l.isNaN())return Ay;t=t.replace(xy,l.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var u,f=my(null!==(u=wy.exec(t))&&void 0!==u?u:[],4),d=f[1],p=f[2],h=f[3],y=ky.parse(null!=d?d:""),m=ky.parse(null!=h?h:""),v="+"===p?y.add(m):y.subtract(m);if(v.isNaN())return Ay;t=t.replace(wy,v.toString())}return t}var Ey=/\(([^()]*)\)/;function Ny(e){var t=e.replace(/\s+/g,"");return t=function(e){for(var t=e;t.includes("(");){var n=my(Ey.exec(t),2)[1];t=t.replace(Ey,_y(n))}return t}(t),t=_y(t)}function My(e){var t=function(e){try{return Ny(e)}catch(_o){return Ay}}(e.slice(5,-1));return t===Ay?"":t}var Ty=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],Cy=["dx","dy","angle","className","breakAll"];function Dy(){return Dy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dy.apply(this,arguments)}function Iy(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function Ry(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return By(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return By(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function By(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ly=/[ \f\n\r\t\v\u2028\u2029]+/,zy=function(e){var t=e.children,n=e.breakAll,r=e.style;try{var i=[];return vo(t)||(i=n?t.toString().split(""):t.toString().split(Ly)),{wordsWithComputedWidth:i.map((function(e){return{word:e,width:hy(e,r).width}})),spaceWidth:n?0:hy(" ",r).width}}catch(_o){return null}},Fy=function(e){return[{words:vo(e)?[]:e.toString().split(Ly)}]},$y=function(e){var t=e.width,n=e.scaleToFit,r=e.children,i=e.style,o=e.breakAll,a=e.maxLines;if((t||n)&&!vh.isSsr){var s=zy({breakAll:o,children:r,style:i});return s?function(e,t,n,r,i){var o=e.maxLines,a=e.children,s=e.style,c=e.breakAll,l=Go(o),u=a,f=function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).reduce((function(e,t){var o=t.word,a=t.width,s=e[e.length-1];if(s&&(null==r||i||s.width+a+n<Number(r)))s.words.push(o),s.width+=a+n;else{var c={words:[o],width:a};e.push(c)}return e}),[])},d=f(t);if(!l)return d;for(var p,h=function(e){var t=u.slice(0,e),n=zy({breakAll:c,style:s,children:t+"…"}).wordsWithComputedWidth,i=f(n),a=i.length>o||function(e){return e.reduce((function(e,t){return e.width>t.width?e:t}))}(i).width>Number(r);return[a,i]},y=0,m=u.length-1,v=0;y<=m&&v<=u.length-1;){var g=Math.floor((y+m)/2),b=Ry(h(g-1),2),x=b[0],w=b[1],j=Ry(h(g),1)[0];if(x||j||(y=g+1),x&&j&&(m=g-1),!x&&j){p=w;break}v++}return p||d}({breakAll:o,children:r,maxLines:a,style:i},s.wordsWithComputedWidth,s.spaceWidth,t,n):Fy(r)}return Fy(r)},Uy="#808080",Wy=function(t){var n=t.x,r=void 0===n?0:n,i=t.y,o=void 0===i?0:i,a=t.lineHeight,s=void 0===a?"1em":a,c=t.capHeight,l=void 0===c?"0.71em":c,u=t.scaleToFit,f=void 0!==u&&u,d=t.textAnchor,p=void 0===d?"start":d,h=t.verticalAnchor,y=void 0===h?"end":h,g=t.fill,b=void 0===g?Uy:g,x=Iy(t,Ty),w=e.useMemo((function(){return $y({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:f,style:x.style,width:x.width})}),[x.breakAll,x.children,x.maxLines,f,x.style,x.width]),j=x.dx,O=x.dy,S=x.angle,P=x.className,A=x.breakAll,k=Iy(x,Cy);if(!Ko(r)||!Ko(o))return null;var _,E=r+(Go(j)?j:0),N=o+(Go(O)?O:0);switch(y){case"start":_=My("calc(".concat(l,")"));break;case"middle":_=My("calc(".concat((w.length-1)/2," * -").concat(s," + (").concat(l," / 2))"));break;default:_=My("calc(".concat(w.length-1," * -").concat(s,")"))}var M=[];if(f){var T=w[0].width,C=x.width;M.push("scale(".concat((Go(C)?C/T:1)/T,")"))}return S&&M.push("rotate(".concat(S,", ").concat(E,", ").concat(N,")")),M.length&&(k.transform=M.join(" ")),v.createElement("text",Dy({},Sa(k,!0),{x:E,y:N,className:m("recharts-text",P),textAnchor:p,fill:b.includes("url")?Uy:b}),w.map((function(e,t){var n=e.words.join(A?"":" ");return v.createElement("tspan",{x:E,dy:0===t?_:s,key:"".concat(n,"-").concat(t)},n)})))};function qy(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function Hy(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Vy(e){let t,n,r;function i(e,r,i=0,o=e.length){if(i<o){if(0!==t(r,r))return o;do{const t=i+o>>>1;n(e[t],r)<0?i=t+1:o=t}while(i<o)}return i}return 2!==e.length?(t=qy,n=(t,n)=>qy(e(t),n),r=(t,n)=>e(t)-n):(t=e===qy||e===Hy?e:Yy,n=e,r=e),{left:i,center:function(e,t,n=0,o=e.length){const a=i(e,t,n,o-1);return a>n&&r(e[a-1],t)>-r(e[a],t)?a-1:a},right:function(e,r,i=0,o=e.length){if(i<o){if(0!==t(r,r))return o;do{const t=i+o>>>1;n(e[t],r)<=0?i=t+1:o=t}while(i<o)}return i}}}function Yy(){return 0}function Xy(e){return null===e?NaN:+e}const Gy=Vy(qy).right;Vy(Xy).center;class Ky extends Map{constructor(e,t=Qy){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(const[n,r]of e)this.set(n,r)}get(e){return super.get(Zy(this,e))}has(e){return super.has(Zy(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},n){const r=t(n);return e.has(r)?e.get(r):(e.set(r,n),n)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},n){const r=t(n);e.has(r)&&(n=e.get(r),e.delete(r));return n}(this,e))}}function Zy({_intern:e,_key:t},n){const r=t(n);return e.has(r)?e.get(r):n}function Qy(e){return null!==e&&"object"==typeof e?e.valueOf():e}function Jy(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:e>t?1:0)}const em=Math.sqrt(50),tm=Math.sqrt(10),nm=Math.sqrt(2);function rm(e,t,n){const r=(t-e)/Math.max(0,n),i=Math.floor(Math.log10(r)),o=r/Math.pow(10,i),a=o>=em?10:o>=tm?5:o>=nm?2:1;let s,c,l;return i<0?(l=Math.pow(10,-i)/a,s=Math.round(e*l),c=Math.round(t*l),s/l<e&&++s,c/l>t&&--c,l=-l):(l=Math.pow(10,i)*a,s=Math.round(e/l),c=Math.round(t/l),s*l<e&&++s,c*l>t&&--c),c<s&&.5<=n&&n<2?rm(e,t,2*n):[s,c,l]}function im(e,t,n){if(!((n=+n)>0))return[];if((e=+e)===(t=+t))return[e];const r=t<e,[i,o,a]=r?rm(t,e,n):rm(e,t,n);if(!(o>=i))return[];const s=o-i+1,c=new Array(s);if(r)if(a<0)for(let l=0;l<s;++l)c[l]=(o-l)/-a;else for(let l=0;l<s;++l)c[l]=(o-l)*a;else if(a<0)for(let l=0;l<s;++l)c[l]=(i+l)/-a;else for(let l=0;l<s;++l)c[l]=(i+l)*a;return c}function om(e,t,n){return rm(e=+e,t=+t,n=+n)[2]}function am(e,t,n){n=+n;const r=(t=+t)<(e=+e),i=r?om(t,e,n):om(e,t,n);return(r?-1:1)*(i<0?1/-i:i)}function sm(e,t){let n;for(const r of e)null!=r&&(n<r||void 0===n&&r>=r)&&(n=r);return n}function cm(e,t){let n;for(const r of e)null!=r&&(n>r||void 0===n&&r>=r)&&(n=r);return n}function lm(e,t,n=0,r=1/0,i){if(t=Math.floor(t),n=Math.floor(Math.max(0,n)),r=Math.floor(Math.min(e.length-1,r)),!(n<=t&&t<=r))return e;for(i=void 0===i?Jy:function(e=qy){if(e===qy)return Jy;if("function"!=typeof e)throw new TypeError("compare is not a function");return(t,n)=>{const r=e(t,n);return r||0===r?r:(0===e(n,n))-(0===e(t,t))}}(i);r>n;){if(r-n>600){const o=r-n+1,a=t-n+1,s=Math.log(o),c=.5*Math.exp(2*s/3),l=.5*Math.sqrt(s*c*(o-c)/o)*(a-o/2<0?-1:1);lm(e,t,Math.max(n,Math.floor(t-a*c/o+l)),Math.min(r,Math.floor(t+(o-a)*c/o+l)),i)}const o=e[t];let a=n,s=r;for(um(e,n,t),i(e[r],o)>0&&um(e,n,r);a<s;){for(um(e,a,s),++a,--s;i(e[a],o)<0;)++a;for(;i(e[s],o)>0;)--s}0===i(e[n],o)?um(e,n,s):(++s,um(e,s,r)),s<=t&&(n=s+1),t<=s&&(r=s-1)}return e}function um(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}function fm(e,t,n=Xy){if((r=e.length)&&!isNaN(t=+t)){if(t<=0||r<2)return+n(e[0],0,e);if(t>=1)return+n(e[r-1],r-1,e);var r,i=(r-1)*t,o=Math.floor(i),a=+n(e[o],o,e);return a+(+n(e[o+1],o+1,e)-a)*(i-o)}}function dm(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function pm(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}const hm=Symbol("implicit");function ym(){var e=new Ky,t=[],n=[],r=hm;function i(i){let o=e.get(i);if(void 0===o){if(r!==hm)return r;e.set(i,o=t.push(i)-1)}return n[o%n.length]}return i.domain=function(n){if(!arguments.length)return t.slice();t=[],e=new Ky;for(const r of n)e.has(r)||e.set(r,t.push(r)-1);return i},i.range=function(e){return arguments.length?(n=Array.from(e),i):n.slice()},i.unknown=function(e){return arguments.length?(r=e,i):r},i.copy=function(){return ym(t,n).unknown(r)},dm.apply(i,arguments),i}function mm(){var e,t,n=ym().unknown(void 0),r=n.domain,i=n.range,o=0,a=1,s=!1,c=0,l=0,u=.5;function f(){var n=r().length,f=a<o,d=f?a:o,p=f?o:a;e=(p-d)/Math.max(1,n-c+2*l),s&&(e=Math.floor(e)),d+=(p-d-e*(n-c))*u,t=e*(1-c),s&&(d=Math.round(d),t=Math.round(t));var h=function(e,t,n){e=+e,t=+t,n=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+n;for(var r=-1,i=0|Math.max(0,Math.ceil((t-e)/n)),o=new Array(i);++r<i;)o[r]=e+r*n;return o}(n).map((function(t){return d+e*t}));return i(f?h.reverse():h)}return delete n.unknown,n.domain=function(e){return arguments.length?(r(e),f()):r()},n.range=function(e){return arguments.length?([o,a]=e,o=+o,a=+a,f()):[o,a]},n.rangeRound=function(e){return[o,a]=e,o=+o,a=+a,s=!0,f()},n.bandwidth=function(){return t},n.step=function(){return e},n.round=function(e){return arguments.length?(s=!!e,f()):s},n.padding=function(e){return arguments.length?(c=Math.min(1,l=+e),f()):c},n.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},n.paddingOuter=function(e){return arguments.length?(l=+e,f()):l},n.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},n.copy=function(){return mm(r(),[o,a]).round(s).paddingInner(c).paddingOuter(l).align(u)},dm.apply(f(),arguments)}function vm(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return vm(t())},e}function gm(){return vm(mm.apply(null,arguments).paddingInner(1))}function bm(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function xm(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function wm(){}var jm=.7,Om=1/jm,Sm="\\s*([+-]?\\d+)\\s*",Pm="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Am="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",km=/^#([0-9a-f]{3,8})$/,_m=new RegExp(`^rgb\\(${Sm},${Sm},${Sm}\\)$`),Em=new RegExp(`^rgb\\(${Am},${Am},${Am}\\)$`),Nm=new RegExp(`^rgba\\(${Sm},${Sm},${Sm},${Pm}\\)$`),Mm=new RegExp(`^rgba\\(${Am},${Am},${Am},${Pm}\\)$`),Tm=new RegExp(`^hsl\\(${Pm},${Am},${Am}\\)$`),Cm=new RegExp(`^hsla\\(${Pm},${Am},${Am},${Pm}\\)$`),Dm={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function Im(){return this.rgb().formatHex()}function Rm(){return this.rgb().formatRgb()}function Bm(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=km.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?Lm(t):3===n?new $m(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?zm(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?zm(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=_m.exec(e))?new $m(t[1],t[2],t[3],1):(t=Em.exec(e))?new $m(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Nm.exec(e))?zm(t[1],t[2],t[3],t[4]):(t=Mm.exec(e))?zm(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Tm.exec(e))?Ym(t[1],t[2]/100,t[3]/100,1):(t=Cm.exec(e))?Ym(t[1],t[2]/100,t[3]/100,t[4]):Dm.hasOwnProperty(e)?Lm(Dm[e]):"transparent"===e?new $m(NaN,NaN,NaN,0):null}function Lm(e){return new $m(e>>16&255,e>>8&255,255&e,1)}function zm(e,t,n,r){return r<=0&&(e=t=n=NaN),new $m(e,t,n,r)}function Fm(e,t,n,r){return 1===arguments.length?((i=e)instanceof wm||(i=Bm(i)),i?new $m((i=i.rgb()).r,i.g,i.b,i.opacity):new $m):new $m(e,t,n,null==r?1:r);var i}function $m(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}function Um(){return`#${Vm(this.r)}${Vm(this.g)}${Vm(this.b)}`}function Wm(){const e=qm(this.opacity);return`${1===e?"rgb(":"rgba("}${Hm(this.r)}, ${Hm(this.g)}, ${Hm(this.b)}${1===e?")":`, ${e})`}`}function qm(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Hm(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Vm(e){return((e=Hm(e))<16?"0":"")+e.toString(16)}function Ym(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Gm(e,t,n,r)}function Xm(e){if(e instanceof Gm)return new Gm(e.h,e.s,e.l,e.opacity);if(e instanceof wm||(e=Bm(e)),!e)return new Gm;if(e instanceof Gm)return e;var t=(e=e.rgb()).r/255,n=e.g/255,r=e.b/255,i=Math.min(t,n,r),o=Math.max(t,n,r),a=NaN,s=o-i,c=(o+i)/2;return s?(a=t===o?(n-r)/s+6*(n<r):n===o?(r-t)/s+2:(t-n)/s+4,s/=c<.5?o+i:2-o-i,a*=60):s=c>0&&c<1?0:a,new Gm(a,s,c,e.opacity)}function Gm(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}function Km(e){return(e=(e||0)%360)<0?e+360:e}function Zm(e){return Math.max(0,Math.min(1,e||0))}function Qm(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}bm(wm,Bm,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Im,formatHex:Im,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Xm(this).formatHsl()},formatRgb:Rm,toString:Rm}),bm($m,Fm,xm(wm,{brighter(e){return e=null==e?Om:Math.pow(Om,e),new $m(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?jm:Math.pow(jm,e),new $m(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new $m(Hm(this.r),Hm(this.g),Hm(this.b),qm(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Um,formatHex:Um,formatHex8:function(){return`#${Vm(this.r)}${Vm(this.g)}${Vm(this.b)}${Vm(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Wm,toString:Wm})),bm(Gm,(function(e,t,n,r){return 1===arguments.length?Xm(e):new Gm(e,t,n,null==r?1:r)}),xm(wm,{brighter(e){return e=null==e?Om:Math.pow(Om,e),new Gm(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?jm:Math.pow(jm,e),new Gm(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,i=2*n-r;return new $m(Qm(e>=240?e-240:e+120,i,r),Qm(e,i,r),Qm(e<120?e+240:e-120,i,r),this.opacity)},clamp(){return new Gm(Km(this.h),Zm(this.s),Zm(this.l),qm(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=qm(this.opacity);return`${1===e?"hsl(":"hsla("}${Km(this.h)}, ${100*Zm(this.s)}%, ${100*Zm(this.l)}%${1===e?")":`, ${e})`}`}}));const Jm=e=>()=>e;function ev(e){return 1==(e=+e)?tv:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}(t,n,e):Jm(isNaN(t)?n:t)}}function tv(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):Jm(isNaN(e)?t:e)}const nv=function e(t){var n=ev(t);function r(e,t){var r=n((e=Fm(e)).r,(t=Fm(t)).r),i=n(e.g,t.g),o=n(e.b,t.b),a=tv(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=o(t),e.opacity=a(t),e+""}}return r.gamma=e,r}(1);function rv(e,t){t||(t=[]);var n,r=e?Math.min(t.length,e.length):0,i=t.slice();return function(o){for(n=0;n<r;++n)i[n]=e[n]*(1-o)+t[n]*o;return i}}function iv(e,t){var n,r=t?t.length:0,i=e?Math.min(r,e.length):0,o=new Array(i),a=new Array(r);for(n=0;n<i;++n)o[n]=fv(e[n],t[n]);for(;n<r;++n)a[n]=t[n];return function(e){for(n=0;n<i;++n)a[n]=o[n](e);return a}}function ov(e,t){var n=new Date;return e=+e,t=+t,function(r){return n.setTime(e*(1-r)+t*r),n}}function av(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function sv(e,t){var n,r={},i={};for(n in null!==e&&"object"==typeof e||(e={}),null!==t&&"object"==typeof t||(t={}),t)n in e?r[n]=fv(e[n],t[n]):i[n]=t[n];return function(e){for(n in r)i[n]=r[n](e);return i}}var cv=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,lv=new RegExp(cv.source,"g");function uv(e,t){var n,r,i,o=cv.lastIndex=lv.lastIndex=0,a=-1,s=[],c=[];for(e+="",t+="";(n=cv.exec(e))&&(r=lv.exec(t));)(i=r.index)>o&&(i=t.slice(o,i),s[a]?s[a]+=i:s[++a]=i),(n=n[0])===(r=r[0])?s[a]?s[a]+=r:s[++a]=r:(s[++a]=null,c.push({i:a,x:av(n,r)})),o=lv.lastIndex;return o<t.length&&(i=t.slice(o),s[a]?s[a]+=i:s[++a]=i),s.length<2?c[0]?function(e){return function(t){return e(t)+""}}(c[0].x):function(e){return function(){return e}}(t):(t=c.length,function(e){for(var n,r=0;r<t;++r)s[(n=c[r]).i]=n.x(e);return s.join("")})}function fv(e,t){var n,r=typeof t;return null==t||"boolean"===r?Jm(t):("number"===r?av:"string"===r?(n=Bm(t))?(t=n,nv):uv:t instanceof Bm?nv:t instanceof Date?ov:function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}(t)?rv:Array.isArray(t)?iv:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?sv:av)(e,t)}function dv(e,t){return e=+e,t=+t,function(n){return Math.round(e*(1-n)+t*n)}}function pv(e,t){void 0===t&&(t=e,e=fv);for(var n=0,r=t.length-1,i=t[0],o=new Array(r<0?0:r);n<r;)o[n]=e(i,i=t[++n]);return function(e){var t=Math.max(0,Math.min(r-1,Math.floor(e*=r)));return o[t](e-t)}}function hv(e){return+e}var yv=[0,1];function mv(e){return e}function vv(e,t){return(t-=e=+e)?function(n){return(n-e)/t}:function(e){return function(){return e}}(isNaN(t)?NaN:.5)}function gv(e,t,n){var r=e[0],i=e[1],o=t[0],a=t[1];return i<r?(r=vv(i,r),o=n(a,o)):(r=vv(r,i),o=n(o,a)),function(e){return o(r(e))}}function bv(e,t,n){var r=Math.min(e.length,t.length)-1,i=new Array(r),o=new Array(r),a=-1;for(e[r]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++a<r;)i[a]=vv(e[a],e[a+1]),o[a]=n(t[a],t[a+1]);return function(t){var n=Gy(e,t,1,r)-1;return o[n](i[n](t))}}function xv(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function wv(){var e,t,n,r,i,o,a=yv,s=yv,c=fv,l=mv;function u(){var e=Math.min(a.length,s.length);return l!==mv&&(l=function(e,t){var n;return e>t&&(n=e,e=t,t=n),function(n){return Math.max(e,Math.min(t,n))}}(a[0],a[e-1])),r=e>2?bv:gv,i=o=null,f}function f(t){return null==t||isNaN(t=+t)?n:(i||(i=r(a.map(e),s,c)))(e(l(t)))}return f.invert=function(n){return l(t((o||(o=r(s,a.map(e),av)))(n)))},f.domain=function(e){return arguments.length?(a=Array.from(e,hv),u()):a.slice()},f.range=function(e){return arguments.length?(s=Array.from(e),u()):s.slice()},f.rangeRound=function(e){return s=Array.from(e),c=dv,u()},f.clamp=function(e){return arguments.length?(l=!!e||mv,u()):l!==mv},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(n=e,f):n},function(n,r){return e=n,t=r,u()}}function jv(){return wv()(mv,mv)}function Ov(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}function Sv(e){return(e=Ov(Math.abs(e)))?e[1]:NaN}var Pv,Av=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function kv(e){if(!(t=Av.exec(e)))throw new Error("invalid format: "+e);var t;return new _v({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function _v(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function Ev(e,t){var n=Ov(e,t);if(!n)return e+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}kv.prototype=_v.prototype,_v.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};const Nv={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>Ev(100*e,t),r:Ev,s:function(e,t){var n=Ov(e,t);if(!n)return e+"";var r=n[0],i=n[1],o=i-(Pv=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=r.length;return o===a?r:o>a?r+new Array(o-a+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Ov(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function Mv(e){return e}var Tv,Cv,Dv,Iv=Array.prototype.map,Rv=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Bv(e){var t,n,r=void 0===e.grouping||void 0===e.thousands?Mv:(t=Iv.call(e.grouping,Number),n=e.thousands+"",function(e,r){for(var i=e.length,o=[],a=0,s=t[0],c=0;i>0&&s>0&&(c+s+1>r&&(s=Math.max(1,r-c)),o.push(e.substring(i-=s,i+s)),!((c+=s+1)>r));)s=t[a=(a+1)%t.length];return o.reverse().join(n)}),i=void 0===e.currency?"":e.currency[0]+"",o=void 0===e.currency?"":e.currency[1]+"",a=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?Mv:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(Iv.call(e.numerals,String)),c=void 0===e.percent?"%":e.percent+"",l=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function f(e){var t=(e=kv(e)).fill,n=e.align,f=e.sign,d=e.symbol,p=e.zero,h=e.width,y=e.comma,m=e.precision,v=e.trim,g=e.type;"n"===g?(y=!0,g="g"):Nv[g]||(void 0===m&&(m=12),v=!0,g="g"),(p||"0"===t&&"="===n)&&(p=!0,t="0",n="=");var b="$"===d?i:"#"===d&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",x="$"===d?o:/[%p]/.test(g)?c:"",w=Nv[g],j=/[defgprs%]/.test(g);function O(e){var i,o,c,d=b,O=x;if("c"===g)O=w(e)+O,e="";else{var S=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),m),v&&(e=function(e){e:for(var t,n=e.length,r=1,i=-1;r<n;++r)switch(e[r]){case".":i=t=r;break;case"0":0===i&&(i=r),t=r;break;default:if(!+e[r])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),S&&0==+e&&"+"!==f&&(S=!1),d=(S?"("===f?f:l:"-"===f||"("===f?"":f)+d,O=("s"===g?Rv[8+Pv/3]:"")+O+(S&&"("===f?")":""),j)for(i=-1,o=e.length;++i<o;)if(48>(c=e.charCodeAt(i))||c>57){O=(46===c?a+e.slice(i+1):e.slice(i))+O,e=e.slice(0,i);break}}y&&!p&&(e=r(e,1/0));var P=d.length+e.length+O.length,A=P<h?new Array(h-P+1).join(t):"";switch(y&&p&&(e=r(A+e,A.length?h-O.length:1/0),A=""),n){case"<":e=d+e+O+A;break;case"=":e=d+A+e+O;break;case"^":e=A.slice(0,P=A.length>>1)+d+e+O+A.slice(P);break;default:e=A+d+e+O}return s(e)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),O.toString=function(){return e+""},O}return{format:f,formatPrefix:function(e,t){var n=f(((e=kv(e)).type="f",e)),r=3*Math.max(-8,Math.min(8,Math.floor(Sv(t)/3))),i=Math.pow(10,-r),o=Rv[8+r/3];return function(e){return n(i*e)+o}}}}function Lv(e,t,n,r){var i,o=am(e,t,n);switch((r=kv(null==r?",f":r)).type){case"s":var a=Math.max(Math.abs(e),Math.abs(t));return null!=r.precision||isNaN(i=function(e,t){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(Sv(t)/3)))-Sv(Math.abs(e)))}(o,a))||(r.precision=i),Dv(r,a);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=function(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,Sv(t)-Sv(e))+1}(o,Math.max(Math.abs(e),Math.abs(t))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=function(e){return Math.max(0,-Sv(Math.abs(e)))}(o))||(r.precision=i-2*("%"===r.type))}return Cv(r)}function zv(e){var t=e.domain;return e.ticks=function(e){var n=t();return im(n[0],n[n.length-1],null==e?10:e)},e.tickFormat=function(e,n){var r=t();return Lv(r[0],r[r.length-1],null==e?10:e,n)},e.nice=function(n){null==n&&(n=10);var r,i,o=t(),a=0,s=o.length-1,c=o[a],l=o[s],u=10;for(l<c&&(i=c,c=l,l=i,i=a,a=s,s=i);u-- >0;){if((i=om(c,l,n))===r)return o[a]=c,o[s]=l,t(o);if(i>0)c=Math.floor(c/i)*i,l=Math.ceil(l/i)*i;else{if(!(i<0))break;c=Math.ceil(c*i)/i,l=Math.floor(l*i)/i}r=i}return e},e}function Fv(){var e=jv();return e.copy=function(){return xv(e,Fv())},dm.apply(e,arguments),zv(e)}function $v(e,t){var n,r=0,i=(e=e.slice()).length-1,o=e[r],a=e[i];return a<o&&(n=r,r=i,i=n,n=o,o=a,a=n),e[r]=t.floor(o),e[i]=t.ceil(a),e}function Uv(e){return Math.log(e)}function Wv(e){return Math.exp(e)}function qv(e){return-Math.log(-e)}function Hv(e){return-Math.exp(-e)}function Vv(e){return isFinite(e)?+("1e"+e):e<0?0:e}function Yv(e){return(t,n)=>-e(-t,n)}function Xv(e){const t=e(Uv,Wv),n=t.domain;let r,i,o=10;function a(){return r=function(e){return e===Math.E?Math.log:10===e&&Math.log10||2===e&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}(o),i=function(e){return 10===e?Vv:e===Math.E?Math.exp:t=>Math.pow(e,t)}(o),n()[0]<0?(r=Yv(r),i=Yv(i),e(qv,Hv)):e(Uv,Wv),t}return t.base=function(e){return arguments.length?(o=+e,a()):o},t.domain=function(e){return arguments.length?(n(e),a()):n()},t.ticks=e=>{const t=n();let a=t[0],s=t[t.length-1];const c=s<a;c&&([a,s]=[s,a]);let l,u,f=r(a),d=r(s);const p=null==e?10:+e;let h=[];if(!(o%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),a>0){for(;f<=d;++f)for(l=1;l<o;++l)if(u=f<0?l/i(-f):l*i(f),!(u<a)){if(u>s)break;h.push(u)}}else for(;f<=d;++f)for(l=o-1;l>=1;--l)if(u=f>0?l/i(-f):l*i(f),!(u<a)){if(u>s)break;h.push(u)}2*h.length<p&&(h=im(a,s,p))}else h=im(f,d,Math.min(d-f,p)).map(i);return c?h.reverse():h},t.tickFormat=(e,n)=>{if(null==e&&(e=10),null==n&&(n=10===o?"s":","),"function"!=typeof n&&(o%1||null!=(n=kv(n)).precision||(n.trim=!0),n=Cv(n)),e===1/0)return n;const a=Math.max(1,o*e/t.ticks().length);return e=>{let t=e/i(Math.round(r(e)));return t*o<o-.5&&(t*=o),t<=a?n(e):""}},t.nice=()=>n($v(n(),{floor:e=>i(Math.floor(r(e))),ceil:e=>i(Math.ceil(r(e)))})),t}function Gv(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Kv(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Zv(e){var t=1,n=e(Gv(t),Kv(t));return n.constant=function(n){return arguments.length?e(Gv(t=+n),Kv(t)):t},zv(n)}function Qv(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function Jv(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function eg(e){return e<0?-e*e:e*e}function tg(e){var t=e(mv,mv),n=1;return t.exponent=function(t){return arguments.length?1===(n=+t)?e(mv,mv):.5===n?e(Jv,eg):e(Qv(n),Qv(1/n)):n},zv(t)}function ng(){var e=tg(wv());return e.copy=function(){return xv(e,ng()).exponent(e.exponent())},dm.apply(e,arguments),e}function rg(e){return Math.sign(e)*e*e}Tv=Bv({thousands:",",grouping:[3],currency:["$",""]}),Cv=Tv.format,Dv=Tv.formatPrefix;const ig=new Date,og=new Date;function ag(e,t,n,r){function i(t){return e(t=0===arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=n=>(e(n=new Date(n-1)),t(n,1),e(n),n),i.round=e=>{const t=i(e),n=i.ceil(e);return e-t<n-e?t:n},i.offset=(e,n)=>(t(e=new Date(+e),null==n?1:Math.floor(n)),e),i.range=(n,r,o)=>{const a=[];if(n=i.ceil(n),o=null==o?1:Math.floor(o),!(n<r&&o>0))return a;let s;do{a.push(s=new Date(+n)),t(n,o),e(n)}while(s<n&&n<r);return a},i.filter=n=>ag((t=>{if(t>=t)for(;e(t),!n(t);)t.setTime(t-1)}),((e,r)=>{if(e>=e)if(r<0)for(;++r<=0;)for(;t(e,-1),!n(e););else for(;--r>=0;)for(;t(e,1),!n(e););})),n&&(i.count=(t,r)=>(ig.setTime(+t),og.setTime(+r),e(ig),e(og),Math.floor(n(ig,og))),i.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?i.filter(r?t=>r(t)%e==0:t=>i.count(0,t)%e==0):i:null)),i}const sg=ag((()=>{}),((e,t)=>{e.setTime(+e+t)}),((e,t)=>t-e));sg.every=e=>(e=Math.floor(e),isFinite(e)&&e>0?e>1?ag((t=>{t.setTime(Math.floor(t/e)*e)}),((t,n)=>{t.setTime(+t+n*e)}),((t,n)=>(n-t)/e)):sg:null),sg.range;const cg=1e3,lg=6e4,ug=36e5,fg=864e5,dg=6048e5,pg=2592e6,hg=31536e6,yg=ag((e=>{e.setTime(e-e.getMilliseconds())}),((e,t)=>{e.setTime(+e+t*cg)}),((e,t)=>(t-e)/cg),(e=>e.getUTCSeconds()));yg.range;const mg=ag((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*cg)}),((e,t)=>{e.setTime(+e+t*lg)}),((e,t)=>(t-e)/lg),(e=>e.getMinutes()));mg.range;const vg=ag((e=>{e.setUTCSeconds(0,0)}),((e,t)=>{e.setTime(+e+t*lg)}),((e,t)=>(t-e)/lg),(e=>e.getUTCMinutes()));vg.range;const gg=ag((e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*cg-e.getMinutes()*lg)}),((e,t)=>{e.setTime(+e+t*ug)}),((e,t)=>(t-e)/ug),(e=>e.getHours()));gg.range;const bg=ag((e=>{e.setUTCMinutes(0,0,0)}),((e,t)=>{e.setTime(+e+t*ug)}),((e,t)=>(t-e)/ug),(e=>e.getUTCHours()));bg.range;const xg=ag((e=>e.setHours(0,0,0,0)),((e,t)=>e.setDate(e.getDate()+t)),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*lg)/fg),(e=>e.getDate()-1));xg.range;const wg=ag((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/fg),(e=>e.getUTCDate()-1));wg.range;const jg=ag((e=>{e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+t)}),((e,t)=>(t-e)/fg),(e=>Math.floor(e/fg)));function Og(e){return ag((t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)}),((e,t)=>{e.setDate(e.getDate()+7*t)}),((e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*lg)/dg))}jg.range;const Sg=Og(0),Pg=Og(1),Ag=Og(2),kg=Og(3),_g=Og(4),Eg=Og(5),Ng=Og(6);function Mg(e){return ag((t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)}),((e,t)=>(t-e)/dg))}Sg.range,Pg.range,Ag.range,kg.range,_g.range,Eg.range,Ng.range;const Tg=Mg(0),Cg=Mg(1),Dg=Mg(2),Ig=Mg(3),Rg=Mg(4),Bg=Mg(5),Lg=Mg(6);Tg.range,Cg.range,Dg.range,Ig.range,Rg.range,Bg.range,Lg.range;const zg=ag((e=>{e.setDate(1),e.setHours(0,0,0,0)}),((e,t)=>{e.setMonth(e.getMonth()+t)}),((e,t)=>t.getMonth()-e.getMonth()+12*(t.getFullYear()-e.getFullYear())),(e=>e.getMonth()));zg.range;const Fg=ag((e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)}),((e,t)=>t.getUTCMonth()-e.getUTCMonth()+12*(t.getUTCFullYear()-e.getUTCFullYear())),(e=>e.getUTCMonth()));Fg.range;const $g=ag((e=>{e.setMonth(0,1),e.setHours(0,0,0,0)}),((e,t)=>{e.setFullYear(e.getFullYear()+t)}),((e,t)=>t.getFullYear()-e.getFullYear()),(e=>e.getFullYear()));$g.every=e=>isFinite(e=Math.floor(e))&&e>0?ag((t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)}),((t,n)=>{t.setFullYear(t.getFullYear()+n*e)})):null,$g.range;const Ug=ag((e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)}),((e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)}),((e,t)=>t.getUTCFullYear()-e.getUTCFullYear()),(e=>e.getUTCFullYear()));function Wg(e,t,n,r,i,o){const a=[[yg,1,cg],[yg,5,5e3],[yg,15,15e3],[yg,30,3e4],[o,1,lg],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,ug],[i,3,108e5],[i,6,216e5],[i,12,432e5],[r,1,fg],[r,2,1728e5],[n,1,dg],[t,1,pg],[t,3,7776e6],[e,1,hg]];function s(t,n,r){const i=Math.abs(n-t)/r,o=Vy((([,,e])=>e)).right(a,i);if(o===a.length)return e.every(am(t/hg,n/hg,r));if(0===o)return sg.every(Math.max(am(t,n,r),1));const[s,c]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return s.every(c)}return[function(e,t,n){const r=t<e;r&&([e,t]=[t,e]);const i=n&&"function"==typeof n.range?n:s(e,t,n),o=i?i.range(e,+t+1):[];return r?o.reverse():o},s]}Ug.every=e=>isFinite(e=Math.floor(e))&&e>0?ag((t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)}),((t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)})):null,Ug.range;const[qg,Hg]=Wg(Ug,Fg,Tg,jg,bg,vg),[Vg,Yg]=Wg($g,zg,Sg,xg,gg,mg);function Xg(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function Gg(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function Kg(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}var Zg,Qg,Jg,eb={"-":"",_:" ",0:"0"},tb=/^\s*\d+/,nb=/^%/,rb=/[\\^$*+?|[\]().{}]/g;function ib(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",o=i.length;return r+(o<n?new Array(n-o+1).join(t)+i:i)}function ob(e){return e.replace(rb,"\\$&")}function ab(e){return new RegExp("^(?:"+e.map(ob).join("|")+")","i")}function sb(e){return new Map(e.map(((e,t)=>[e.toLowerCase(),t])))}function cb(e,t,n){var r=tb.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function lb(e,t,n){var r=tb.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function ub(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function fb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function db(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function pb(e,t,n){var r=tb.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function hb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function yb(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function mb(e,t,n){var r=tb.exec(t.slice(n,n+1));return r?(e.q=3*r[0]-3,n+r[0].length):-1}function vb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function gb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function bb(e,t,n){var r=tb.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function xb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function wb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function jb(e,t,n){var r=tb.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function Ob(e,t,n){var r=tb.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function Sb(e,t,n){var r=tb.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function Pb(e,t,n){var r=nb.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function Ab(e,t,n){var r=tb.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function kb(e,t,n){var r=tb.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function _b(e,t){return ib(e.getDate(),t,2)}function Eb(e,t){return ib(e.getHours(),t,2)}function Nb(e,t){return ib(e.getHours()%12||12,t,2)}function Mb(e,t){return ib(1+xg.count($g(e),e),t,3)}function Tb(e,t){return ib(e.getMilliseconds(),t,3)}function Cb(e,t){return Tb(e,t)+"000"}function Db(e,t){return ib(e.getMonth()+1,t,2)}function Ib(e,t){return ib(e.getMinutes(),t,2)}function Rb(e,t){return ib(e.getSeconds(),t,2)}function Bb(e){var t=e.getDay();return 0===t?7:t}function Lb(e,t){return ib(Sg.count($g(e)-1,e),t,2)}function zb(e){var t=e.getDay();return t>=4||0===t?_g(e):_g.ceil(e)}function Fb(e,t){return e=zb(e),ib(_g.count($g(e),e)+(4===$g(e).getDay()),t,2)}function $b(e){return e.getDay()}function Ub(e,t){return ib(Pg.count($g(e)-1,e),t,2)}function Wb(e,t){return ib(e.getFullYear()%100,t,2)}function qb(e,t){return ib((e=zb(e)).getFullYear()%100,t,2)}function Hb(e,t){return ib(e.getFullYear()%1e4,t,4)}function Vb(e,t){var n=e.getDay();return ib((e=n>=4||0===n?_g(e):_g.ceil(e)).getFullYear()%1e4,t,4)}function Yb(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ib(t/60|0,"0",2)+ib(t%60,"0",2)}function Xb(e,t){return ib(e.getUTCDate(),t,2)}function Gb(e,t){return ib(e.getUTCHours(),t,2)}function Kb(e,t){return ib(e.getUTCHours()%12||12,t,2)}function Zb(e,t){return ib(1+wg.count(Ug(e),e),t,3)}function Qb(e,t){return ib(e.getUTCMilliseconds(),t,3)}function Jb(e,t){return Qb(e,t)+"000"}function ex(e,t){return ib(e.getUTCMonth()+1,t,2)}function tx(e,t){return ib(e.getUTCMinutes(),t,2)}function nx(e,t){return ib(e.getUTCSeconds(),t,2)}function rx(e){var t=e.getUTCDay();return 0===t?7:t}function ix(e,t){return ib(Tg.count(Ug(e)-1,e),t,2)}function ox(e){var t=e.getUTCDay();return t>=4||0===t?Rg(e):Rg.ceil(e)}function ax(e,t){return e=ox(e),ib(Rg.count(Ug(e),e)+(4===Ug(e).getUTCDay()),t,2)}function sx(e){return e.getUTCDay()}function cx(e,t){return ib(Cg.count(Ug(e)-1,e),t,2)}function lx(e,t){return ib(e.getUTCFullYear()%100,t,2)}function ux(e,t){return ib((e=ox(e)).getUTCFullYear()%100,t,2)}function fx(e,t){return ib(e.getUTCFullYear()%1e4,t,4)}function dx(e,t){var n=e.getUTCDay();return ib((e=n>=4||0===n?Rg(e):Rg.ceil(e)).getUTCFullYear()%1e4,t,4)}function px(){return"+0000"}function hx(){return"%"}function yx(e){return+e}function mx(e){return Math.floor(+e/1e3)}function vx(e){return new Date(e)}function gx(e){return e instanceof Date?+e:+new Date(+e)}function bx(e,t,n,r,i,o,a,s,c,l){var u=jv(),f=u.invert,d=u.domain,p=l(".%L"),h=l(":%S"),y=l("%I:%M"),m=l("%I %p"),v=l("%a %d"),g=l("%b %d"),b=l("%B"),x=l("%Y");function w(e){return(c(e)<e?p:s(e)<e?h:a(e)<e?y:o(e)<e?m:r(e)<e?i(e)<e?v:g:n(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,gx)):d().map(vx)},u.ticks=function(t){var n=d();return e(n[0],n[n.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:l(t)},u.nice=function(e){var n=d();return e&&"function"==typeof e.range||(e=t(n[0],n[n.length-1],null==e?10:e)),e?d($v(n,e)):u},u.copy=function(){return xv(u,bx(e,t,n,r,i,o,a,s,c,l))},u}function xx(){var e,t,n,r,i,o=0,a=1,s=mv,c=!1;function l(t){return null==t||isNaN(t=+t)?i:s(0===n?.5:(t=(r(t)-e)*n,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var n,r;return arguments.length?([n,r]=t,s=e(n,r),l):[s(0),s(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,e=r(o=+o),t=r(a=+a),n=e===t?0:1/(t-e),l):[o,a]},l.clamp=function(e){return arguments.length?(c=!!e,l):c},l.interpolator=function(e){return arguments.length?(s=e,l):s},l.range=u(fv),l.rangeRound=u(dv),l.unknown=function(e){return arguments.length?(i=e,l):i},function(i){return r=i,e=i(o),t=i(a),n=e===t?0:1/(t-e),l}}function wx(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function jx(){var e=tg(xx());return e.copy=function(){return wx(e,jx()).exponent(e.exponent())},pm.apply(e,arguments)}function Ox(){var e,t,n,r,i,o,a,s=0,c=.5,l=1,u=1,f=mv,d=!1;function p(e){return isNaN(e=+e)?a:(e=.5+((e=+o(e))-t)*(u*e<u*t?r:i),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var n,r,i;return arguments.length?([n,r,i]=t,f=pv(e,[n,r,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(a){return arguments.length?([s,c,l]=a,e=o(s=+s),t=o(c=+c),n=o(l=+l),r=e===t?0:.5/(t-e),i=t===n?0:.5/(n-t),u=t<e?-1:1,p):[s,c,l]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(fv),p.rangeRound=h(dv),p.unknown=function(e){return arguments.length?(a=e,p):a},function(a){return o=a,e=a(s),t=a(c),n=a(l),r=e===t?0:.5/(t-e),i=t===n?0:.5/(n-t),u=t<e?-1:1,p}}function Sx(){var e=tg(Ox());return e.copy=function(){return wx(e,Sx()).exponent(e.exponent())},pm.apply(e,arguments)}!function(e){Zg=function(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,o=e.days,a=e.shortDays,s=e.months,c=e.shortMonths,l=ab(i),u=sb(i),f=ab(o),d=sb(o),p=ab(a),h=sb(a),y=ab(s),m=sb(s),v=ab(c),g=sb(c),b={a:function(e){return a[e.getDay()]},A:function(e){return o[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return s[e.getMonth()]},c:null,d:_b,e:_b,f:Cb,g:qb,G:Vb,H:Eb,I:Nb,j:Mb,L:Tb,m:Db,M:Ib,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:yx,s:mx,S:Rb,u:Bb,U:Lb,V:Fb,w:$b,W:Ub,x:null,X:null,y:Wb,Y:Hb,Z:Yb,"%":hx},x={a:function(e){return a[e.getUTCDay()]},A:function(e){return o[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return s[e.getUTCMonth()]},c:null,d:Xb,e:Xb,f:Jb,g:ux,G:dx,H:Gb,I:Kb,j:Zb,L:Qb,m:ex,M:tx,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:yx,s:mx,S:nx,u:rx,U:ix,V:ax,w:sx,W:cx,x:null,X:null,y:lx,Y:fx,Z:px,"%":hx},w={a:function(e,t,n){var r=p.exec(t.slice(n));return r?(e.w=h.get(r[0].toLowerCase()),n+r[0].length):-1},A:function(e,t,n){var r=f.exec(t.slice(n));return r?(e.w=d.get(r[0].toLowerCase()),n+r[0].length):-1},b:function(e,t,n){var r=v.exec(t.slice(n));return r?(e.m=g.get(r[0].toLowerCase()),n+r[0].length):-1},B:function(e,t,n){var r=y.exec(t.slice(n));return r?(e.m=m.get(r[0].toLowerCase()),n+r[0].length):-1},c:function(e,n,r){return S(e,t,n,r)},d:gb,e:gb,f:Sb,g:hb,G:pb,H:xb,I:xb,j:bb,L:Ob,m:vb,M:wb,p:function(e,t,n){var r=l.exec(t.slice(n));return r?(e.p=u.get(r[0].toLowerCase()),n+r[0].length):-1},q:mb,Q:Ab,s:kb,S:jb,u:lb,U:ub,V:fb,w:cb,W:db,x:function(e,t,r){return S(e,n,t,r)},X:function(e,t,n){return S(e,r,t,n)},y:hb,Y:pb,Z:yb,"%":Pb};function j(e,t){return function(n){var r,i,o,a=[],s=-1,c=0,l=e.length;for(n instanceof Date||(n=new Date(+n));++s<l;)37===e.charCodeAt(s)&&(a.push(e.slice(c,s)),null!=(i=eb[r=e.charAt(++s)])?r=e.charAt(++s):i="e"===r?" ":"0",(o=t[r])&&(r=o(n,i)),a.push(r),c=s+1);return a.push(e.slice(c,s)),a.join("")}}function O(e,t){return function(n){var r,i,o=Kg(1900,void 0,1);if(S(o,e,n+="",0)!=n.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(t&&!("Z"in o)&&(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(i=(r=Gg(Kg(o.y,0,1))).getUTCDay(),r=i>4||0===i?Cg.ceil(r):Cg(r),r=wg.offset(r,7*(o.V-1)),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(i=(r=Xg(Kg(o.y,0,1))).getDay(),r=i>4||0===i?Pg.ceil(r):Pg(r),r=xg.offset(r,7*(o.V-1)),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?Gg(Kg(o.y,0,1)).getUTCDay():Xg(Kg(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,Gg(o)):Xg(o)}}function S(e,t,n,r){for(var i,o,a=0,s=t.length,c=n.length;a<s;){if(r>=c)return-1;if(37===(i=t.charCodeAt(a++))){if(i=t.charAt(a++),!(o=w[i in eb?t.charAt(a++):i])||(r=o(e,n,r))<0)return-1}else if(i!=n.charCodeAt(r++))return-1}return r}return b.x=j(n,b),b.X=j(r,b),b.c=j(t,b),x.x=j(n,x),x.X=j(r,x),x.c=j(t,x),{format:function(e){var t=j(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=O(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=j(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=O(e+="",!0);return t.toString=function(){return e},t}}}(e),Qg=Zg.format,Zg.parse,Jg=Zg.utcFormat,Zg.utcParse}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});const Px=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:mm,scaleDiverging:function e(){var t=zv(Ox()(mv));return t.copy=function(){return wx(t,e())},pm.apply(t,arguments)},scaleDivergingLog:function e(){var t=Xv(Ox()).domain([.1,1,10]);return t.copy=function(){return wx(t,e()).base(t.base())},pm.apply(t,arguments)},scaleDivergingPow:Sx,scaleDivergingSqrt:function(){return Sx.apply(null,arguments).exponent(.5)},scaleDivergingSymlog:function e(){var t=Zv(Ox());return t.copy=function(){return wx(t,e()).constant(t.constant())},pm.apply(t,arguments)},scaleIdentity:function e(t){var n;function r(e){return null==e||isNaN(e=+e)?n:e}return r.invert=r,r.domain=r.range=function(e){return arguments.length?(t=Array.from(e,hv),r):t.slice()},r.unknown=function(e){return arguments.length?(n=e,r):n},r.copy=function(){return e(t).unknown(n)},t=arguments.length?Array.from(t,hv):[0,1],zv(r)},scaleImplicit:hm,scaleLinear:Fv,scaleLog:function e(){const t=Xv(wv()).domain([1,10]);return t.copy=()=>xv(t,e()).base(t.base()),dm.apply(t,arguments),t},scaleOrdinal:ym,scalePoint:gm,scalePow:ng,scaleQuantile:function e(){var t,n=[],r=[],i=[];function o(){var e=0,t=Math.max(1,r.length);for(i=new Array(t-1);++e<t;)i[e-1]=fm(n,e/t);return a}function a(e){return null==e||isNaN(e=+e)?t:r[Gy(i,e)]}return a.invertExtent=function(e){var t=r.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:n[0],t<i.length?i[t]:n[n.length-1]]},a.domain=function(e){if(!arguments.length)return n.slice();n=[];for(let t of e)null==t||isNaN(t=+t)||n.push(t);return n.sort(qy),o()},a.range=function(e){return arguments.length?(r=Array.from(e),o()):r.slice()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.quantiles=function(){return i.slice()},a.copy=function(){return e().domain(n).range(r).unknown(t)},dm.apply(a,arguments)},scaleQuantize:function e(){var t,n=0,r=1,i=1,o=[.5],a=[0,1];function s(e){return null!=e&&e<=e?a[Gy(o,e,0,i)]:t}function c(){var e=-1;for(o=new Array(i);++e<i;)o[e]=((e+1)*r-(e-i)*n)/(i+1);return s}return s.domain=function(e){return arguments.length?([n,r]=e,n=+n,r=+r,c()):[n,r]},s.range=function(e){return arguments.length?(i=(a=Array.from(e)).length-1,c()):a.slice()},s.invertExtent=function(e){var t=a.indexOf(e);return t<0?[NaN,NaN]:t<1?[n,o[0]]:t>=i?[o[i-1],r]:[o[t-1],o[t]]},s.unknown=function(e){return arguments.length?(t=e,s):s},s.thresholds=function(){return o.slice()},s.copy=function(){return e().domain([n,r]).range(a).unknown(t)},dm.apply(zv(s),arguments)},scaleRadial:function e(){var t,n=jv(),r=[0,1],i=!1;function o(e){var r=function(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}(n(e));return isNaN(r)?t:i?Math.round(r):r}return o.invert=function(e){return n.invert(rg(e))},o.domain=function(e){return arguments.length?(n.domain(e),o):n.domain()},o.range=function(e){return arguments.length?(n.range((r=Array.from(e,hv)).map(rg)),o):r.slice()},o.rangeRound=function(e){return o.range(e).round(!0)},o.round=function(e){return arguments.length?(i=!!e,o):i},o.clamp=function(e){return arguments.length?(n.clamp(e),o):n.clamp()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return e(n.domain(),r).round(i).clamp(n.clamp()).unknown(t)},dm.apply(o,arguments),zv(o)},scaleSequential:function e(){var t=zv(xx()(mv));return t.copy=function(){return wx(t,e())},pm.apply(t,arguments)},scaleSequentialLog:function e(){var t=Xv(xx()).domain([1,10]);return t.copy=function(){return wx(t,e()).base(t.base())},pm.apply(t,arguments)},scaleSequentialPow:jx,scaleSequentialQuantile:function e(){var t=[],n=mv;function r(e){if(null!=e&&!isNaN(e=+e))return n((Gy(t,e,1)-1)/(t.length-1))}return r.domain=function(e){if(!arguments.length)return t.slice();t=[];for(let n of e)null==n||isNaN(n=+n)||t.push(n);return t.sort(qy),r},r.interpolator=function(e){return arguments.length?(n=e,r):n},r.range=function(){return t.map(((e,r)=>n(r/(t.length-1))))},r.quantiles=function(e){return Array.from({length:e+1},((n,r)=>function(e,t){if((n=(e=Float64Array.from(function*(e){for(let t of e)null!=t&&(t=+t)>=t&&(yield t)}(e))).length)&&!isNaN(t=+t)){if(t<=0||n<2)return cm(e);if(t>=1)return sm(e);var n,r=(n-1)*t,i=Math.floor(r),o=sm(lm(e,i).subarray(0,i+1));return o+(cm(e.subarray(i+1))-o)*(r-i)}}(t,r/e)))},r.copy=function(){return e(n).domain(t)},pm.apply(r,arguments)},scaleSequentialSqrt:function(){return jx.apply(null,arguments).exponent(.5)},scaleSequentialSymlog:function e(){var t=Zv(xx());return t.copy=function(){return wx(t,e()).constant(t.constant())},pm.apply(t,arguments)},scaleSqrt:function(){return ng.apply(null,arguments).exponent(.5)},scaleSymlog:function e(){var t=Zv(wv());return t.copy=function(){return xv(t,e()).constant(t.constant())},dm.apply(t,arguments)},scaleThreshold:function e(){var t,n=[.5],r=[0,1],i=1;function o(e){return null!=e&&e<=e?r[Gy(n,e,0,i)]:t}return o.domain=function(e){return arguments.length?(n=Array.from(e),i=Math.min(n.length,r.length-1),o):n.slice()},o.range=function(e){return arguments.length?(r=Array.from(e),i=Math.min(n.length,r.length-1),o):r.slice()},o.invertExtent=function(e){var t=r.indexOf(e);return[n[t-1],n[t]]},o.unknown=function(e){return arguments.length?(t=e,o):t},o.copy=function(){return e().domain(n).range(r).unknown(t)},dm.apply(o,arguments)},scaleTime:function(){return dm.apply(bx(Vg,Yg,$g,zg,Sg,xg,gg,mg,yg,Qg).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},scaleUtc:function(){return dm.apply(bx(qg,Hg,Ug,Fg,Tg,wg,bg,vg,yg,Jg).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)},tickFormat:Lv},Symbol.toStringTag,{value:"Module"}));var Ax=fr;var kx=function(e,t,n){for(var r=-1,i=e.length;++r<i;){var o=e[r],a=t(o);if(null!=a&&(void 0===s?a==a&&!Ax(a):n(a,s)))var s=a,c=o}return c};var _x=function(e,t){return e>t},Ex=kx,Nx=_x,Mx=Yf;var Tx=function(e){return e&&e.length?Ex(e,Mx,Nx):void 0};const Cx=y(Tx);var Dx=function(e,t){return e<t},Ix=kx,Rx=Dx,Bx=Yf;var Lx=function(e){return e&&e.length?Ix(e,Bx,Rx):void 0};const zx=y(Lx);var Fx=Xi,$x=ad,Ux=op,Wx=Hn;var qx=Kd,Hx=function(e,t){return(Wx(e)?Fx:Ux)(e,$x(t))};const Vx=y((function(e,t){return qx(Hx(e,t),1)}));var Yx=hf;const Xx=y((function(e,t){return Yx(e,t)}));var Gx,Kx=1e9,Zx=!0,Qx="[DecimalError] ",Jx=Qx+"Invalid argument: ",ew=Qx+"Exponent out of range: ",tw=Math.floor,nw=Math.pow,rw=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,iw=1e7,ow=9007199254740991,aw=tw(1286742750677284.5),sw={};function cw(e,t){var n,r,i,o,a,s,c,l,u=e.constructor,f=u.precision;if(!e.s||!t.s)return t.s||(t=new u(e)),Zx?gw(t,f):t;if(c=e.d,l=t.d,a=e.e,i=t.e,c=c.slice(),o=a-i){for(o<0?(r=c,o=-o,s=l.length):(r=l,i=a,s=c.length),o>(s=(a=Math.ceil(f/7))>s?a+1:s+1)&&(o=s,r.length=1),r.reverse();o--;)r.push(0);r.reverse()}for((s=c.length)-(o=l.length)<0&&(o=s,r=l,l=c,c=r),n=0;o;)n=(c[--o]=c[o]+l[o]+n)/iw|0,c[o]%=iw;for(n&&(c.unshift(n),++i),s=c.length;0==c[--s];)c.pop();return t.d=c,t.e=i,Zx?gw(t,f):t}function lw(e,t,n){if(e!==~~e||e<t||e>n)throw Error(Jx+e)}function uw(e){var t,n,r,i=e.length-1,o="",a=e[0];if(i>0){for(o+=a,t=1;t<i;t++)(n=7-(r=e[t]+"").length)&&(o+=yw(n)),o+=r;(n=7-(r=(a=e[t])+"").length)&&(o+=yw(n))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}sw.absoluteValue=sw.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},sw.comparedTo=sw.cmp=function(e){var t,n,r,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(t=0,n=(r=o.d.length)<(i=e.d.length)?r:i;t<n;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return r===i?0:r>i^o.s<0?1:-1},sw.decimalPlaces=sw.dp=function(){var e=this,t=e.d.length-1,n=7*(t-e.e);if(t=e.d[t])for(;t%10==0;t/=10)n--;return n<0?0:n},sw.dividedBy=sw.div=function(e){return fw(this,new this.constructor(e))},sw.dividedToIntegerBy=sw.idiv=function(e){var t=this.constructor;return gw(fw(this,new t(e),0,1),t.precision)},sw.equals=sw.eq=function(e){return!this.cmp(e)},sw.exponent=function(){return pw(this)},sw.greaterThan=sw.gt=function(e){return this.cmp(e)>0},sw.greaterThanOrEqualTo=sw.gte=function(e){return this.cmp(e)>=0},sw.isInteger=sw.isint=function(){return this.e>this.d.length-2},sw.isNegative=sw.isneg=function(){return this.s<0},sw.isPositive=sw.ispos=function(){return this.s>0},sw.isZero=function(){return 0===this.s},sw.lessThan=sw.lt=function(e){return this.cmp(e)<0},sw.lessThanOrEqualTo=sw.lte=function(e){return this.cmp(e)<1},sw.logarithm=sw.log=function(e){var t,n=this,r=n.constructor,i=r.precision,o=i+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(Gx))throw Error(Qx+"NaN");if(n.s<1)throw Error(Qx+(n.s?"NaN":"-Infinity"));return n.eq(Gx)?new r(0):(Zx=!1,t=fw(mw(n,o),mw(e,o),o),Zx=!0,gw(t,i))},sw.minus=sw.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?bw(t,e):cw(t,(e.s=-e.s,e))},sw.modulo=sw.mod=function(e){var t,n=this,r=n.constructor,i=r.precision;if(!(e=new r(e)).s)throw Error(Qx+"NaN");return n.s?(Zx=!1,t=fw(n,e,0,1).times(e),Zx=!0,n.minus(t)):gw(new r(n),i)},sw.naturalExponential=sw.exp=function(){return dw(this)},sw.naturalLogarithm=sw.ln=function(){return mw(this)},sw.negated=sw.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},sw.plus=sw.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?cw(t,e):bw(t,(e.s=-e.s,e))},sw.precision=sw.sd=function(e){var t,n,r,i=this;if(void 0!==e&&e!==!!e&&1!==e&&0!==e)throw Error(Jx+e);if(t=pw(i)+1,n=7*(r=i.d.length-1)+1,r=i.d[r]){for(;r%10==0;r/=10)n--;for(r=i.d[0];r>=10;r/=10)n++}return e&&t>n?t:n},sw.squareRoot=sw.sqrt=function(){var e,t,n,r,i,o,a,s=this,c=s.constructor;if(s.s<1){if(!s.s)return new c(0);throw Error(Qx+"NaN")}for(e=pw(s),Zx=!1,0==(i=Math.sqrt(+s))||i==1/0?(((t=uw(s.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=tw((e+1)/2)-(e<0||e%2),r=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):r=new c(i.toString()),i=a=(n=c.precision)+3;;)if(r=(o=r).plus(fw(s,o,a+2)).times(.5),uw(o.d).slice(0,a)===(t=uw(r.d)).slice(0,a)){if(t=t.slice(a-3,a+1),i==a&&"4999"==t){if(gw(o,n+1,0),o.times(o).eq(s)){r=o;break}}else if("9999"!=t)break;a+=4}return Zx=!0,gw(r,n)},sw.times=sw.mul=function(e){var t,n,r,i,o,a,s,c,l,u=this,f=u.constructor,d=u.d,p=(e=new f(e)).d;if(!u.s||!e.s)return new f(0);for(e.s*=u.s,n=u.e+e.e,(c=d.length)<(l=p.length)&&(o=d,d=p,p=o,a=c,c=l,l=a),o=[],r=a=c+l;r--;)o.push(0);for(r=l;--r>=0;){for(t=0,i=c+r;i>r;)s=o[i]+p[r]*d[i-r-1]+t,o[i--]=s%iw|0,t=s/iw|0;o[i]=(o[i]+t)%iw|0}for(;!o[--a];)o.pop();return t?++n:o.shift(),e.d=o,e.e=n,Zx?gw(e,f.precision):e},sw.toDecimalPlaces=sw.todp=function(e,t){var n=this,r=n.constructor;return n=new r(n),void 0===e?n:(lw(e,0,Kx),void 0===t?t=r.rounding:lw(t,0,8),gw(n,e+pw(n)+1,t))},sw.toExponential=function(e,t){var n,r=this,i=r.constructor;return void 0===e?n=xw(r,!0):(lw(e,0,Kx),void 0===t?t=i.rounding:lw(t,0,8),n=xw(r=gw(new i(r),e+1,t),!0,e+1)),n},sw.toFixed=function(e,t){var n,r,i=this,o=i.constructor;return void 0===e?xw(i):(lw(e,0,Kx),void 0===t?t=o.rounding:lw(t,0,8),n=xw((r=gw(new o(i),e+pw(i)+1,t)).abs(),!1,e+pw(r)+1),i.isneg()&&!i.isZero()?"-"+n:n)},sw.toInteger=sw.toint=function(){var e=this,t=e.constructor;return gw(new t(e),pw(e)+1,t.rounding)},sw.toNumber=function(){return+this},sw.toPower=sw.pow=function(e){var t,n,r,i,o,a,s=this,c=s.constructor,l=+(e=new c(e));if(!e.s)return new c(Gx);if(!(s=new c(s)).s){if(e.s<1)throw Error(Qx+"Infinity");return s}if(s.eq(Gx))return s;if(r=c.precision,e.eq(Gx))return gw(s,r);if(a=(t=e.e)>=(n=e.d.length-1),o=s.s,a){if((n=l<0?-l:l)<=ow){for(i=new c(Gx),t=Math.ceil(r/7+4),Zx=!1;n%2&&ww((i=i.times(s)).d,t),0!==(n=tw(n/2));)ww((s=s.times(s)).d,t);return Zx=!0,e.s<0?new c(Gx).div(i):gw(i,r)}}else if(o<0)throw Error(Qx+"NaN");return o=o<0&&1&e.d[Math.max(t,n)]?-1:1,s.s=1,Zx=!1,i=e.times(mw(s,r+12)),Zx=!0,(i=dw(i)).s=o,i},sw.toPrecision=function(e,t){var n,r,i=this,o=i.constructor;return void 0===e?r=xw(i,(n=pw(i))<=o.toExpNeg||n>=o.toExpPos):(lw(e,1,Kx),void 0===t?t=o.rounding:lw(t,0,8),r=xw(i=gw(new o(i),e,t),e<=(n=pw(i))||n<=o.toExpNeg,e)),r},sw.toSignificantDigits=sw.tosd=function(e,t){var n=this.constructor;return void 0===e?(e=n.precision,t=n.rounding):(lw(e,1,Kx),void 0===t?t=n.rounding:lw(t,0,8)),gw(new n(this),e,t)},sw.toString=sw.valueOf=sw.val=sw.toJSON=sw[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=pw(e),n=e.constructor;return xw(e,t<=n.toExpNeg||t>=n.toExpPos)};var fw=function(){function e(e,t){var n,r=0,i=e.length;for(e=e.slice();i--;)n=e[i]*t+r,e[i]=n%iw|0,r=n/iw|0;return r&&e.unshift(r),e}function t(e,t,n,r){var i,o;if(n!=r)o=n>r?1:-1;else for(i=o=0;i<n;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function n(e,t,n){for(var r=0;n--;)e[n]-=r,r=e[n]<t[n]?1:0,e[n]=r*iw+e[n]-t[n];for(;!e[0]&&e.length>1;)e.shift()}return function(r,i,o,a){var s,c,l,u,f,d,p,h,y,m,v,g,b,x,w,j,O,S,P=r.constructor,A=r.s==i.s?1:-1,k=r.d,_=i.d;if(!r.s)return new P(r);if(!i.s)throw Error(Qx+"Division by zero");for(c=r.e-i.e,O=_.length,w=k.length,h=(p=new P(A)).d=[],l=0;_[l]==(k[l]||0);)++l;if(_[l]>(k[l]||0)&&--c,(g=null==o?o=P.precision:a?o+(pw(r)-pw(i))+1:o)<0)return new P(0);if(g=g/7+2|0,l=0,1==O)for(u=0,_=_[0],g++;(l<w||u)&&g--;l++)b=u*iw+(k[l]||0),h[l]=b/_|0,u=b%_|0;else{for((u=iw/(_[0]+1)|0)>1&&(_=e(_,u),k=e(k,u),O=_.length,w=k.length),x=O,m=(y=k.slice(0,O)).length;m<O;)y[m++]=0;(S=_.slice()).unshift(0),j=_[0],_[1]>=iw/2&&++j;do{u=0,(s=t(_,y,O,m))<0?(v=y[0],O!=m&&(v=v*iw+(y[1]||0)),(u=v/j|0)>1?(u>=iw&&(u=iw-1),1==(s=t(f=e(_,u),y,d=f.length,m=y.length))&&(u--,n(f,O<d?S:_,d))):(0==u&&(s=u=1),f=_.slice()),(d=f.length)<m&&f.unshift(0),n(y,f,m),-1==s&&(s=t(_,y,O,m=y.length))<1&&(u++,n(y,O<m?S:_,m)),m=y.length):0===s&&(u++,y=[0]),h[l++]=u,s&&y[0]?y[m++]=k[x]||0:(y=[k[x]],m=1)}while((x++<w||void 0!==y[0])&&g--)}return h[0]||h.shift(),p.e=c,gw(p,a?o+pw(p)+1:o)}}();function dw(e,t){var n,r,i,o,a,s=0,c=0,l=e.constructor,u=l.precision;if(pw(e)>16)throw Error(ew+pw(e));if(!e.s)return new l(Gx);for(null==t?(Zx=!1,a=u):a=t,o=new l(.03125);e.abs().gte(.1);)e=e.times(o),c+=5;for(a+=Math.log(nw(2,c))/Math.LN10*2+5|0,n=r=i=new l(Gx),l.precision=a;;){if(r=gw(r.times(e),a),n=n.times(++s),uw((o=i.plus(fw(r,n,a))).d).slice(0,a)===uw(i.d).slice(0,a)){for(;c--;)i=gw(i.times(i),a);return l.precision=u,null==t?(Zx=!0,gw(i,u)):i}i=o}}function pw(e){for(var t=7*e.e,n=e.d[0];n>=10;n/=10)t++;return t}function hw(e,t,n){if(t>e.LN10.sd())throw Zx=!0,n&&(e.precision=n),Error(Qx+"LN10 precision limit exceeded");return gw(new e(e.LN10),t)}function yw(e){for(var t="";e--;)t+="0";return t}function mw(e,t){var n,r,i,o,a,s,c,l,u,f=1,d=e,p=d.d,h=d.constructor,y=h.precision;if(d.s<1)throw Error(Qx+(d.s?"NaN":"-Infinity"));if(d.eq(Gx))return new h(0);if(null==t?(Zx=!1,l=y):l=t,d.eq(10))return null==t&&(Zx=!0),hw(h,l);if(l+=10,h.precision=l,r=(n=uw(p)).charAt(0),o=pw(d),!(Math.abs(o)<15e14))return c=hw(h,l+2,y).times(o+""),d=mw(new h(r+"."+n.slice(1)),l-10).plus(c),h.precision=y,null==t?(Zx=!0,gw(d,y)):d;for(;r<7&&1!=r||1==r&&n.charAt(1)>3;)r=(n=uw((d=d.times(e)).d)).charAt(0),f++;for(o=pw(d),r>1?(d=new h("0."+n),o++):d=new h(r+"."+n.slice(1)),s=a=d=fw(d.minus(Gx),d.plus(Gx),l),u=gw(d.times(d),l),i=3;;){if(a=gw(a.times(u),l),uw((c=s.plus(fw(a,new h(i),l))).d).slice(0,l)===uw(s.d).slice(0,l))return s=s.times(2),0!==o&&(s=s.plus(hw(h,l+2,y).times(o+""))),s=fw(s,new h(f),l),h.precision=y,null==t?(Zx=!0,gw(s,y)):s;s=c,i+=2}}function vw(e,t){var n,r,i;for((n=t.indexOf("."))>-1&&(t=t.replace(".","")),(r=t.search(/e/i))>0?(n<0&&(n=r),n+=+t.slice(r+1),t=t.substring(0,r)):n<0&&(n=t.length),r=0;48===t.charCodeAt(r);)++r;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(r,i)){if(i-=r,n=n-r-1,e.e=tw(n/7),e.d=[],r=(n+1)%7,n<0&&(r+=7),r<i){for(r&&e.d.push(+t.slice(0,r)),i-=7;r<i;)e.d.push(+t.slice(r,r+=7));r=7-(t=t.slice(r)).length}else r-=i;for(;r--;)t+="0";if(e.d.push(+t),Zx&&(e.e>aw||e.e<-aw))throw Error(ew+n)}else e.s=0,e.e=0,e.d=[0];return e}function gw(e,t,n){var r,i,o,a,s,c,l,u,f=e.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((r=t-a)<0)r+=7,i=t,l=f[u=0];else{if((u=Math.ceil((r+1)/7))>=(o=f.length))return e;for(l=o=f[u],a=1;o>=10;o/=10)a++;i=(r%=7)-7+a}if(void 0!==n&&(s=l/(o=nw(10,a-i-1))%10|0,c=t<0||void 0!==f[u+1]||l%o,c=n<4?(s||c)&&(0==n||n==(e.s<0?3:2)):s>5||5==s&&(4==n||c||6==n&&(r>0?i>0?l/nw(10,a-i):0:f[u-1])%10&1||n==(e.s<0?8:7))),t<1||!f[0])return c?(o=pw(e),f.length=1,t=t-o-1,f[0]=nw(10,(7-t%7)%7),e.e=tw(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==r?(f.length=u,o=1,u--):(f.length=u+1,o=nw(10,7-r),f[u]=i>0?(l/nw(10,a-i)%nw(10,i)|0)*o:0),c)for(;;){if(0==u){(f[0]+=o)==iw&&(f[0]=1,++e.e);break}if(f[u]+=o,f[u]!=iw)break;f[u--]=0,o=1}for(r=f.length;0===f[--r];)f.pop();if(Zx&&(e.e>aw||e.e<-aw))throw Error(ew+pw(e));return e}function bw(e,t){var n,r,i,o,a,s,c,l,u,f,d=e.constructor,p=d.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new d(e),Zx?gw(t,p):t;if(c=e.d,f=t.d,r=t.e,l=e.e,c=c.slice(),a=l-r){for((u=a<0)?(n=c,a=-a,s=f.length):(n=f,r=l,s=c.length),a>(i=Math.max(Math.ceil(p/7),s)+2)&&(a=i,n.length=1),n.reverse(),i=a;i--;)n.push(0);n.reverse()}else{for((u=(i=c.length)<(s=f.length))&&(s=i),i=0;i<s;i++)if(c[i]!=f[i]){u=c[i]<f[i];break}a=0}for(u&&(n=c,c=f,f=n,t.s=-t.s),s=c.length,i=f.length-s;i>0;--i)c[s++]=0;for(i=f.length;i>a;){if(c[--i]<f[i]){for(o=i;o&&0===c[--o];)c[o]=iw-1;--c[o],c[i]+=iw}c[i]-=f[i]}for(;0===c[--s];)c.pop();for(;0===c[0];c.shift())--r;return c[0]?(t.d=c,t.e=r,Zx?gw(t,p):t):new d(0)}function xw(e,t,n){var r,i=pw(e),o=uw(e.d),a=o.length;return t?(n&&(r=n-a)>0?o=o.charAt(0)+"."+o.slice(1)+yw(r):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+yw(-i-1)+o,n&&(r=n-a)>0&&(o+=yw(r))):i>=a?(o+=yw(i+1-a),n&&(r=n-i-1)>0&&(o=o+"."+yw(r))):((r=i+1)<a&&(o=o.slice(0,r)+"."+o.slice(r)),n&&(r=n-a)>0&&(i+1===a&&(o+="."),o+=yw(r))),e.s<0?"-"+o:o}function ww(e,t){if(e.length>t)return e.length=t,!0}function jw(e){if(!e||"object"!=typeof e)throw Error(Qx+"Object expected");var t,n,r,i=["precision",1,Kx,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(r=e[n=i[t]])){if(!(tw(r)===r&&r>=i[t+1]&&r<=i[t+2]))throw Error(Jx+n+": "+r);this[n]=r}if(void 0!==(r=e[n="LN10"])){if(r!=Math.LN10)throw Error(Jx+n+": "+r);this[n]=new this(r)}return this}var Ow=function e(t){var n,r,i;function o(e){var t=this;if(!(t instanceof o))return new o(e);if(t.constructor=o,e instanceof o)return t.s=e.s,t.e=e.e,void(t.d=(e=e.d)?e.slice():e);if("number"==typeof e){if(0*e!=0)throw Error(Jx+e);if(e>0)t.s=1;else{if(!(e<0))return t.s=0,t.e=0,void(t.d=[0]);e=-e,t.s=-1}return e===~~e&&e<1e7?(t.e=0,void(t.d=[e])):vw(t,e.toString())}if("string"!=typeof e)throw Error(Jx+e);if(45===e.charCodeAt(0)?(e=e.slice(1),t.s=-1):t.s=1,!rw.test(e))throw Error(Jx+e);vw(t,e)}if(o.prototype=sw,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=e,o.config=o.set=jw,void 0===t&&(t={}),t)for(i=["precision","rounding","toExpNeg","toExpPos","LN10"],n=0;n<i.length;)t.hasOwnProperty(r=i[n++])||(t[r]=this[r]);return o.config(t),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});Gx=new Ow(1);const Sw=Ow;function Pw(e){return function(e){if(Array.isArray(e))return Aw(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return Aw(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Aw(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Aw(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var kw=function(e){return e},_w={"@@functional/placeholder":!0},Ew=function(e){return e===_w},Nw=function(e){return function t(){return 0===arguments.length||1===arguments.length&&Ew(arguments.length<=0?void 0:arguments[0])?t:e.apply(void 0,arguments)}},Mw=function e(t,n){return 1===t?n:Nw((function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var a=i.filter((function(e){return e!==_w})).length;return a>=t?n.apply(void 0,i):e(t-a,Nw((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var o=i.map((function(e){return Ew(e)?t.shift():e}));return n.apply(void 0,Pw(o).concat(t))})))}))},Tw=function(e){return Mw(e.length,e)},Cw=function(e,t){for(var n=[],r=e;r<t;++r)n[r-e]=r;return n},Dw=Tw((function(e,t){return Array.isArray(t)?t.map(e):Object.keys(t).map((function(e){return t[e]})).map(e)})),Iw=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},Rw=function(e){var t=null,n=null;return function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return t&&i.every((function(e,n){return e===t[n]}))?n:(t=i,n=e.apply(void 0,i))}};var Bw=Tw((function(e,t,n){var r=+e;return r+n*(+t-r)})),Lw=Tw((function(e,t,n){var r=t-+e;return(n-e)/(r=r||1/0)})),zw=Tw((function(e,t,n){var r=t-+e;return r=r||1/0,Math.max(0,Math.min(1,(n-e)/r))}));const Fw={rangeStep:function(e,t,n){for(var r=new Sw(e),i=0,o=[];r.lt(t)&&i<1e5;)o.push(r.toNumber()),r=r.add(n),i++;return o},getDigitCount:function(e){return 0===e?1:Math.floor(new Sw(e).abs().log(10).toNumber())+1},interpolateNumber:Bw,uninterpolateNumber:Lw,uninterpolateTruncation:zw};function $w(e){return function(e){if(Array.isArray(e))return qw(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||Ww(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Uw(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(e)))return;var n=[],r=!0,i=!1,o=void 0;try{for(var a,s=e[Symbol.iterator]();!(r=(a=s.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(c){i=!0,o=c}finally{try{r||null==s.return||s.return()}finally{if(i)throw o}}return n}(e,t)||Ww(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ww(e,t){if(e){if("string"==typeof e)return qw(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?qw(e,t):void 0}}function qw(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Hw(e){var t=Uw(e,2),n=t[0],r=t[1],i=n,o=r;return n>r&&(i=r,o=n),[i,o]}function Vw(e,t,n){if(e.lte(0))return new Sw(0);var r=Fw.getDigitCount(e.toNumber()),i=new Sw(10).pow(r),o=e.div(i),a=1!==r?.05:.1,s=new Sw(Math.ceil(o.div(a).toNumber())).add(n).mul(a).mul(i);return t?s:new Sw(Math.ceil(s))}function Yw(e,t,n){var r=1,i=new Sw(e);if(!i.isint()&&n){var o=Math.abs(e);o<1?(r=new Sw(10).pow(Fw.getDigitCount(e)-1),i=new Sw(Math.floor(i.div(r).toNumber())).mul(r)):o>1&&(i=new Sw(Math.floor(e)))}else 0===e?i=new Sw(Math.floor((t-1)/2)):n||(i=new Sw(Math.floor(e)));var a=Math.floor((t-1)/2),s=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(!t.length)return kw;var r=t.reverse(),i=r[0],o=r.slice(1);return function(){return o.reduce((function(e,t){return t(e)}),i.apply(void 0,arguments))}}(Dw((function(e){return i.add(new Sw(e-a).mul(r)).toNumber()})),Cw);return s(0,t)}function Xw(e,t,n,r){var i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(n-1)))return{step:new Sw(0),tickMin:new Sw(0),tickMax:new Sw(0)};var o,a=Vw(new Sw(t).sub(e).div(n-1),r,i);o=e<=0&&t>=0?new Sw(0):(o=new Sw(e).add(t).div(2)).sub(new Sw(o).mod(a));var s=Math.ceil(o.sub(e).div(a).toNumber()),c=Math.ceil(new Sw(t).sub(o).div(a).toNumber()),l=s+c+1;return l>n?Xw(e,t,n,r,i+1):(l<n&&(c=t>0?c+(n-l):c,s=t>0?s:s+(n-l)),{step:a,tickMin:o.sub(new Sw(s).mul(a)),tickMax:o.add(new Sw(c).mul(a))})}var Gw=Rw((function(e){var t=Uw(e,2),n=t[0],r=t[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Math.max(i,2),s=Uw(Hw([n,r]),2),c=s[0],l=s[1];if(c===-1/0||l===1/0){var u=l===1/0?[c].concat($w(Cw(0,i-1).map((function(){return 1/0})))):[].concat($w(Cw(0,i-1).map((function(){return-1/0}))),[l]);return n>r?Iw(u):u}if(c===l)return Yw(c,i,o);var f=Xw(c,l,a,o),d=f.step,p=f.tickMin,h=f.tickMax,y=Fw.rangeStep(p,h.add(new Sw(.1).mul(d)),d);return n>r?Iw(y):y})),Kw=Rw((function(e,t){var n=Uw(e,2),r=n[0],i=n[1],o=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=Uw(Hw([r,i]),2),s=a[0],c=a[1];if(s===-1/0||c===1/0)return[r,i];if(s===c)return[s];var l=Math.max(t,2),u=Vw(new Sw(c).sub(s).div(l-1),o,0),f=[].concat($w(Fw.rangeStep(new Sw(s),new Sw(c).sub(new Sw(.99).mul(u)),u)),[c]);return r>i?Iw(f):f}));function Zw(e,t){throw new Error("Invariant failed")}var Qw=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Jw(e){return(Jw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ej(){return ej=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ej.apply(this,arguments)}function tj(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return nj(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nj(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nj(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function rj(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function ij(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,uj(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function oj(e,t,n){return t=sj(t),function(e,t){if(t&&("object"===Jw(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,aj()?Reflect.construct(t,n||[],sj(e).constructor):t.apply(e,n))}function aj(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(aj=function(){return!!e})()}function sj(e){return(sj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function cj(e,t){return(cj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function lj(e,t,n){return(t=uj(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function uj(e){var t=function(e,t){if("object"!=Jw(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Jw(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Jw(t)?t:t+""}var fj=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),oj(this,e,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cj(e,t)}(e,v.Component),ij(e,[{key:"render",value:function(){var e=this.props,t=e.offset,n=e.layout,r=e.width,i=e.dataKey,o=e.data,a=e.dataPointFormatter,s=e.xAxis,c=e.yAxis,l=rj(e,Qw),u=Sa(l,!1);"x"===this.props.direction&&"number"!==s.type&&Zw();var f=o.map((function(e){var o=a(e,i),l=o.x,f=o.y,d=o.value,p=o.errorVal;if(!p)return null;var h,y,m=[];if(Array.isArray(p)){var g=tj(p,2);h=g[0],y=g[1]}else h=y=p;if("vertical"===n){var b=s.scale,x=f+t,w=x+r,j=x-r,O=b(d-h),S=b(d+y);m.push({x1:S,y1:w,x2:S,y2:j}),m.push({x1:O,y1:x,x2:S,y2:x}),m.push({x1:O,y1:w,x2:O,y2:j})}else if("horizontal"===n){var P=c.scale,A=l+t,k=A-r,_=A+r,E=P(d-h),N=P(d+y);m.push({x1:k,y1:N,x2:_,y2:N}),m.push({x1:A,y1:E,x2:A,y2:N}),m.push({x1:k,y1:E,x2:_,y2:E})}return v.createElement(Ia,ej({className:"recharts-errorBar",key:"bar-".concat(m.map((function(e){return"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)})))},u),m.map((function(e){return v.createElement("line",ej({},e,{key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)}))})))}));return v.createElement(Ia,{className:"recharts-errorBars"},f)}}])}();function dj(e){return dj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dj(e)}function pj(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function hj(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pj(Object(n),!0).forEach((function(t){yj(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pj(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function yj(e,t,n){return t=function(e){var t=function(e,t){if("object"!=dj(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=dj(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dj(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}lj(fj,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),lj(fj,"displayName","ErrorBar");var mj=function(e){var t=e.children,n=e.formattedGraphicalItems,r=e.legendWidth,i=e.legendContent,o=wa(t,qd);if(!o)return null;var a,s=qd.defaultProps,c=void 0!==s?hj(hj({},s),o.props):{};return a=o.props&&o.props.payload?o.props&&o.props.payload:"children"===i?(n||[]).reduce((function(e,t){var n=t.item,r=t.props,i=r.sectors||r.data||[];return e.concat(i.map((function(e){return{type:o.props.iconType||n.props.legendType,value:e.name,color:e.fill,payload:e}})))}),[]):(n||[]).map((function(e){var t=e.item,n=t.type.defaultProps,r=void 0!==n?hj(hj({},n),t.props):{},i=r.dataKey,o=r.name,a=r.legendType;return{inactive:r.hide,dataKey:i,type:c.iconType||a||"square",color:Pj(t),value:o||i,payload:r}})),hj(hj(hj({},c),qd.getWithHeight(o,r)),{},{payload:a,item:o})};function vj(e){return vj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vj(e)}function gj(e){return function(e){if(Array.isArray(e))return bj(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return bj(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return bj(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function bj(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function xj(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wj(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xj(Object(n),!0).forEach((function(t){jj(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xj(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function jj(e,t,n){return t=function(e){var t=function(e,t){if("object"!=vj(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=vj(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==vj(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oj(e,t,n){return vo(e)||vo(t)?n:Ko(t)?mo(e,t,n):jr(t)?t(e):n}function Sj(e,t,n,r){var i=Vx(e,(function(e){return Oj(e,t)}));if("number"===n){var o=i.filter((function(e){return Go(e)||parseFloat(e)}));return o.length?[zx(o),Cx(o)]:[1/0,-1/0]}return(r?i.filter((function(e){return!vo(e)})):i).map((function(e){return Ko(e)||e instanceof Date?e:""}))}var Pj=function(e){var t,n,r=e.type.displayName,i=null!==(t=e.type)&&void 0!==t&&t.defaultProps?wj(wj({},e.type.defaultProps),e.props):e.props,o=i.stroke,a=i.fill;switch(r){case"Line":n=o;break;case"Area":case"Radar":n=o&&"none"!==o?o:a;break;default:n=a}return n},Aj=function(e,t,n,r,i){var o=xa(t.props.children,fj).filter((function(e){return function(e,t,n){return!!vo(t)||("horizontal"===e?"yAxis"===t:"vertical"===e||"x"===n?"xAxis"===t:"y"!==n||"yAxis"===t)}(r,i,e.props.direction)}));if(o&&o.length){var a=o.map((function(e){return e.props.dataKey}));return e.reduce((function(e,t){var r=Oj(t,n);if(vo(r))return e;var i=Array.isArray(r)?[zx(r),Cx(r)]:[r,r],o=a.reduce((function(e,n){var r=Oj(t,n,0),o=i[0]-Math.abs(Array.isArray(r)?r[0]:r),a=i[1]+Math.abs(Array.isArray(r)?r[1]:r);return[Math.min(o,e[0]),Math.max(a,e[1])]}),[1/0,-1/0]);return[Math.min(o[0],e[0]),Math.max(o[1],e[1])]}),[1/0,-1/0])}return null},kj=function(e,t,n,r,i){var o=t.map((function(t){var o=t.props.dataKey;return"number"===n&&o&&Aj(e,t,o,r)||Sj(e,o,n,i)}));if("number"===n)return o.reduce((function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}),[1/0,-1/0]);var a={};return o.reduce((function(e,t){for(var n=0,r=t.length;n<r;n++)a[t[n]]||(a[t[n]]=!0,e.push(t[n]));return e}),[])},_j=function(e,t){return"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t},Ej=function(e,t,n,r){if(r)return e.map((function(e){return e.coordinate}));var i,o,a=e.map((function(e){return e.coordinate===t&&(i=!0),e.coordinate===n&&(o=!0),e.coordinate}));return i||a.push(t),o||a.push(n),a},Nj=function(e,t,n){if(!e)return null;var r=e.scale,i=e.duplicateDomain,o=e.type,a=e.range,s="scaleBand"===e.realScaleType?r.bandwidth()/2:2,c=(t||n)&&"category"===o&&r.bandwidth?r.bandwidth()/s:0;return c="angleAxis"===e.axisType&&(null==a?void 0:a.length)>=2?2*Yo(a[0]-a[1])*c:c,t&&(e.ticks||e.niceTicks)?(e.ticks||e.niceTicks).map((function(e){var t=i?i.indexOf(e):e;return{coordinate:r(t)+c,value:e,offset:c}})).filter((function(e){return!Vo(e.coordinate)})):e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map((function(e,t){return{coordinate:r(e)+c,value:e,index:t,offset:c}})):r.ticks&&!n?r.ticks(e.tickCount).map((function(e){return{coordinate:r(e)+c,value:e,offset:c}})):r.domain().map((function(e,t){return{coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}}))},Mj=new WeakMap,Tj=function(e,t){if("function"!=typeof t)return e;Mj.has(e)||Mj.set(e,new WeakMap);var n=Mj.get(e);if(n.has(t))return n.get(t);var r=function(){e.apply(void 0,arguments),t.apply(void 0,arguments)};return n.set(t,r),r},Cj=function(e,t,n){var r=e.scale,i=e.type,o=e.layout,a=e.axisType;if("auto"===r)return"radial"===o&&"radiusAxis"===a?{scale:mm(),realScaleType:"band"}:"radial"===o&&"angleAxis"===a?{scale:Fv(),realScaleType:"linear"}:"category"===i&&t&&(t.indexOf("LineChart")>=0||t.indexOf("AreaChart")>=0||t.indexOf("ComposedChart")>=0&&!n)?{scale:gm(),realScaleType:"point"}:"category"===i?{scale:mm(),realScaleType:"band"}:{scale:Fv(),realScaleType:"linear"};if(wo(r)){var s="scale".concat(fs(r));return{scale:(Px[s]||gm)(),realScaleType:Px[s]?s:"point"}}return jr(r)?{scale:r}:{scale:gm(),realScaleType:"point"}},Dj=1e-4,Ij=function(e){var t=e.domain();if(t&&!(t.length<=2)){var n=t.length,r=e.range(),i=Math.min(r[0],r[1])-Dj,o=Math.max(r[0],r[1])+Dj,a=e(t[0]),s=e(t[n-1]);(a<i||a>o||s<i||s>o)&&e.domain([t[0],t[n-1]])}},Rj={sign:function(e){var t=e.length;if(!(t<=0))for(var n=0,r=e[0].length;n<r;++n)for(var i=0,o=0,a=0;a<t;++a){var s=Vo(e[a][n][1])?e[a][n][0]:e[a][n][1];s>=0?(e[a][n][0]=i,e[a][n][1]=i+s,i=e[a][n][1]):(e[a][n][0]=o,e[a][n][1]=o+s,o=e[a][n][1])}},expand:function(e,t){if((r=e.length)>0){for(var n,r,i,o=0,a=e[0].length;o<a;++o){for(i=n=0;n<r;++n)i+=e[n][o][1]||0;if(i)for(n=0;n<r;++n)e[n][o][1]/=i}dc(e,t)}},none:dc,silhouette:function(e,t){if((n=e.length)>0){for(var n,r=0,i=e[t[0]],o=i.length;r<o;++r){for(var a=0,s=0;a<n;++a)s+=e[a][r][1]||0;i[r][1]+=i[r][0]=-s/2}dc(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(r=(n=e[t[0]]).length)>0){for(var n,r,i,o=0,a=1;a<r;++a){for(var s=0,c=0,l=0;s<i;++s){for(var u=e[t[s]],f=u[a][1]||0,d=(f-(u[a-1][1]||0))/2,p=0;p<s;++p){var h=e[t[p]];d+=(h[a][1]||0)-(h[a-1][1]||0)}c+=f,l+=d*f}n[a-1][1]+=n[a-1][0]=o,c&&(o-=l/c)}n[a-1][1]+=n[a-1][0]=o,dc(e,t)}},positive:function(e){var t=e.length;if(!(t<=0))for(var n=0,r=e[0].length;n<r;++n)for(var i=0,o=0;o<t;++o){var a=Vo(e[o][n][1])?e[o][n][0]:e[o][n][1];a>=0?(e[o][n][0]=i,e[o][n][1]=i+a,i=e[o][n][1]):(e[o][n][0]=0,e[o][n][1]=0)}}},Bj=function(e,t,n){var r=t.map((function(e){return e.props.dataKey})),i=Rj[n],o=function(){var e=ds([]),t=pc,n=dc,r=hc;function i(i){var o,a,s=Array.from(e.apply(this,arguments),yc),c=s.length,l=-1;for(const e of i)for(o=0,++l;o<c;++o)(s[o][l]=[0,+r(e,s[o].key,l,i)]).data=e;for(o=0,a=Ps(t(s));o<c;++o)s[a[o]].index=o;return n(s,a),s}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:ds(Array.from(t)),i):e},i.value=function(e){return arguments.length?(r="function"==typeof e?e:ds(+e),i):r},i.order=function(e){return arguments.length?(t=null==e?pc:"function"==typeof e?e:ds(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(n=null==e?dc:e,i):n},i}().keys(r).value((function(e,t){return+Oj(e,t,0)})).order(pc).offset(i);return o(e)},Lj=function(e,t){var n=t.realScaleType,r=t.type,i=t.tickCount,o=t.originalDomain,a=t.allowDecimals,s=n||t.scale;if("auto"!==s&&"linear"!==s)return null;if(i&&"number"===r&&o&&("auto"===o[0]||"auto"===o[1])){var c=e.domain();if(!c.length)return null;var l=Gw(c,i,a);return e.domain([zx(l),Cx(l)]),{niceTicks:l}}if(i&&"number"===r){var u=e.domain();return{niceTicks:Kw(u,i,a)}}return null};function zj(e){var t=e.axis,n=e.ticks,r=e.bandSize,i=e.entry,o=e.index,a=e.dataKey;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!vo(i[t.dataKey])){var s=na(n,"value",i[t.dataKey]);if(s)return s.coordinate+r/2}return n[o]?n[o].coordinate+r/2:null}var c=Oj(i,vo(a)?t.dataKey:a);return vo(c)?null:t.scale(c)}var Fj=function(e){var t=e.axis,n=e.ticks,r=e.offset,i=e.bandSize,o=e.entry,a=e.index;if("category"===t.type)return n[a]?n[a].coordinate+r:null;var s=Oj(o,t.dataKey,t.domain[a]);return vo(s)?null:t.scale(s)-i/2+r},$j=function(e,t,n){return Object.keys(e).reduce((function(r,i){var o=e[i].stackedData.reduce((function(e,r){var i=r.slice(t,n+1).reduce((function(e,t){return[zx(t.concat([e[0]]).filter(Go)),Cx(t.concat([e[1]]).filter(Go))]}),[1/0,-1/0]);return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]}),[1/0,-1/0]);return[Math.min(o[0],r[0]),Math.max(o[1],r[1])]}),[1/0,-1/0]).map((function(e){return e===1/0||e===-1/0?0:e}))},Uj=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Wj=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,qj=function(e,t,n){if(jr(e))return e(t,n);if(!Array.isArray(e))return t;var r=[];if(Go(e[0]))r[0]=n?e[0]:Math.min(e[0],t[0]);else if(Uj.test(e[0])){var i=+Uj.exec(e[0])[1];r[0]=t[0]-i}else jr(e[0])?r[0]=e[0](t[0]):r[0]=t[0];if(Go(e[1]))r[1]=n?e[1]:Math.max(e[1],t[1]);else if(Wj.test(e[1])){var o=+Wj.exec(e[1])[1];r[1]=t[1]+o}else jr(e[1])?r[1]=e[1](t[1]):r[1]=t[1];return r},Hj=function(e,t,n){if(e&&e.scale&&e.scale.bandwidth){var r=e.scale.bandwidth();if(!n||r>0)return r}if(e&&t&&t.length>=2){for(var i=qp(t,(function(e){return e.coordinate})),o=1/0,a=1,s=i.length;a<s;a++){var c=i[a],l=i[a-1];o=Math.min((c.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Vj=function(e,t,n){return e&&e.length?Xx(e,mo(n,"type.defaultProps.domain"))?t:e:t},Yj=function(e,t){var n=e.type.defaultProps?wj(wj({},e.type.defaultProps),e.props):e.props,r=n.dataKey,i=n.name,o=n.unit,a=n.formatter,s=n.tooltipType,c=n.chartType,l=n.hide;return wj(wj({},Sa(e,!1)),{},{dataKey:r,unit:o,formatter:a,name:i||r,color:Pj(e),value:Oj(t,r),type:s,payload:t,chartType:c,hide:l})};function Xj(e){return Xj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xj(e)}function Gj(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Kj(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gj(Object(n),!0).forEach((function(t){Zj(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gj(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Zj(e,t,n){return t=function(e){var t=function(e,t){if("object"!=Xj(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Xj(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Xj(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qj(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Jj(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Jj(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jj(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var eO=Math.PI/180,tO=function(e){return 180*e/Math.PI},nO=function(e,t,n,r){return{x:e+Math.cos(-eO*r)*n,y:t+Math.sin(-eO*r)*n}},rO=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(n.left||0)-(n.right||0)),Math.abs(t-(n.top||0)-(n.bottom||0)))/2},iO=function(e,t){var n=e.x,r=e.y,i=t.cx,o=t.cy,a=function(e,t){var n=e.x,r=e.y,i=t.x,o=t.y;return Math.sqrt(Math.pow(n-i,2)+Math.pow(r-o,2))}({x:n,y:r},{x:i,y:o});if(a<=0)return{radius:a};var s=(n-i)/a,c=Math.acos(s);return r>o&&(c=2*Math.PI-c),{radius:a,angle:tO(c),angleInRadian:c}},oO=function(e,t){var n=t.startAngle,r=t.endAngle,i=Math.floor(n/360),o=Math.floor(r/360);return e+360*Math.min(i,o)},aO=function(e,t){var n=e.x,r=e.y,i=iO({x:n,y:r},t),o=i.radius,a=i.angle,s=t.innerRadius,c=t.outerRadius;if(o<s||o>c)return!1;if(0===o)return!0;var l,u=function(e){var t=e.startAngle,n=e.endAngle,r=Math.floor(t/360),i=Math.floor(n/360),o=Math.min(r,i);return{startAngle:t-360*o,endAngle:n-360*o}}(t),f=u.startAngle,d=u.endAngle,p=a;if(f<=d){for(;p>d;)p-=360;for(;p<f;)p+=360;l=p>=f&&p<=d}else{for(;p>f;)p-=360;for(;p<d;)p+=360;l=p>=d&&p<=f}return l?Kj(Kj({},t),{},{radius:o,angle:oO(p,t)}):null},sO=function(t){return e.isValidElement(t)||jr(t)||"boolean"==typeof t?"":t.className};function cO(e){return cO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cO(e)}var lO=["offset"];function uO(e){return function(e){if(Array.isArray(e))return fO(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return fO(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return fO(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function fO(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function dO(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function pO(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function hO(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pO(Object(n),!0).forEach((function(t){yO(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pO(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function yO(e,t,n){return t=function(e){var t=function(e,t){if("object"!=cO(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=cO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==cO(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function mO(){return mO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mO.apply(this,arguments)}var vO=function(e,t,n){var r,i,o=e.position,a=e.viewBox,s=e.offset,c=e.className,l=a,u=l.cx,f=l.cy,d=l.innerRadius,p=l.outerRadius,h=l.startAngle,y=l.endAngle,g=l.clockWise,b=(d+p)/2,x=function(e,t){return Yo(t-e)*Math.min(Math.abs(t-e),360)}(h,y),w=x>=0?1:-1;"insideStart"===o?(r=h+w*s,i=g):"insideEnd"===o?(r=y-w*s,i=!g):"end"===o&&(r=y+w*s,i=g),i=x<=0?i:!i;var j=nO(u,f,b,r),O=nO(u,f,b,r+359*(i?1:-1)),S="M".concat(j.x,",").concat(j.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(i?0:1,",\n    ").concat(O.x,",").concat(O.y),P=vo(e.id)?Qo("recharts-radial-line-"):e.id;return v.createElement("text",mO({},n,{dominantBaseline:"central",className:m("recharts-radial-bar-label",c)}),v.createElement("defs",null,v.createElement("path",{id:P,d:S})),v.createElement("textPath",{xlinkHref:"#".concat(P)},t))};function gO(t){var n,r=t.offset,i=hO({offset:void 0===r?5:r},dO(t,lO)),o=i.viewBox,a=i.position,s=i.value,c=i.children,l=i.content,u=i.className,f=void 0===u?"":u,d=i.textBreakAll;if(!o||vo(s)&&vo(c)&&!e.isValidElement(l)&&!jr(l))return null;if(e.isValidElement(l))return e.cloneElement(l,i);if(jr(l)){if(n=e.createElement(l,i),e.isValidElement(n))return n}else n=function(e){var t=e.value,n=e.formatter,r=vo(e.children)?t:e.children;return jr(n)?n(r):r}(i);var p=function(e){return"cx"in e&&Go(e.cx)}(o),h=Sa(i,!0);if(p&&("insideStart"===a||"insideEnd"===a||"end"===a))return vO(i,n,h);var y=p?function(e){var t=e.viewBox,n=e.offset,r=e.position,i=t,o=i.cx,a=i.cy,s=i.innerRadius,c=i.outerRadius,l=(i.startAngle+i.endAngle)/2;if("outside"===r){var u=nO(o,a,c+n,l),f=u.x;return{x:f,y:u.y,textAnchor:f>=o?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:o,y:a,textAnchor:"middle",verticalAnchor:"end"};var d=nO(o,a,(s+c)/2,l);return{x:d.x,y:d.y,textAnchor:"middle",verticalAnchor:"middle"}}(i):function(e){var t=e.viewBox,n=e.parentViewBox,r=e.offset,i=e.position,o=t,a=o.x,s=o.y,c=o.width,l=o.height,u=l>=0?1:-1,f=u*r,d=u>0?"end":"start",p=u>0?"start":"end",h=c>=0?1:-1,y=h*r,m=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return hO(hO({},{x:a+c/2,y:s-u*r,textAnchor:"middle",verticalAnchor:d}),n?{height:Math.max(s-n.y,0),width:c}:{});if("bottom"===i)return hO(hO({},{x:a+c/2,y:s+l+f,textAnchor:"middle",verticalAnchor:p}),n?{height:Math.max(n.y+n.height-(s+l),0),width:c}:{});if("left"===i){var g={x:a-y,y:s+l/2,textAnchor:m,verticalAnchor:"middle"};return hO(hO({},g),n?{width:Math.max(g.x-n.x,0),height:l}:{})}if("right"===i){var b={x:a+c+y,y:s+l/2,textAnchor:v,verticalAnchor:"middle"};return hO(hO({},b),n?{width:Math.max(n.x+n.width-b.x,0),height:l}:{})}var x=n?{width:c,height:l}:{};return"insideLeft"===i?hO({x:a+y,y:s+l/2,textAnchor:v,verticalAnchor:"middle"},x):"insideRight"===i?hO({x:a+c-y,y:s+l/2,textAnchor:m,verticalAnchor:"middle"},x):"insideTop"===i?hO({x:a+c/2,y:s+f,textAnchor:"middle",verticalAnchor:p},x):"insideBottom"===i?hO({x:a+c/2,y:s+l-f,textAnchor:"middle",verticalAnchor:d},x):"insideTopLeft"===i?hO({x:a+y,y:s+f,textAnchor:v,verticalAnchor:p},x):"insideTopRight"===i?hO({x:a+c-y,y:s+f,textAnchor:m,verticalAnchor:p},x):"insideBottomLeft"===i?hO({x:a+y,y:s+l-f,textAnchor:v,verticalAnchor:d},x):"insideBottomRight"===i?hO({x:a+c-y,y:s+l-f,textAnchor:m,verticalAnchor:d},x):gr(i)&&(Go(i.x)||Xo(i.x))&&(Go(i.y)||Xo(i.y))?hO({x:a+Jo(i.x,c),y:s+Jo(i.y,l),textAnchor:"end",verticalAnchor:"end"},x):hO({x:a+c/2,y:s+l/2,textAnchor:"middle",verticalAnchor:"middle"},x)}(i);return v.createElement(Wy,mO({className:m("recharts-label",f)},h,y,{breakAll:d}),n)}gO.displayName="Label";var bO=function(e){var t=e.cx,n=e.cy,r=e.angle,i=e.startAngle,o=e.endAngle,a=e.r,s=e.radius,c=e.innerRadius,l=e.outerRadius,u=e.x,f=e.y,d=e.top,p=e.left,h=e.width,y=e.height,m=e.clockWise,v=e.labelViewBox;if(v)return v;if(Go(h)&&Go(y)){if(Go(u)&&Go(f))return{x:u,y:f,width:h,height:y};if(Go(d)&&Go(p))return{x:d,y:p,width:h,height:y}}return Go(u)&&Go(f)?{x:u,y:f,width:0,height:0}:Go(t)&&Go(n)?{cx:t,cy:n,startAngle:i||r||0,endAngle:o||r||0,innerRadius:c||0,outerRadius:l||s||a||0,clockWise:m}:e.viewBox?e.viewBox:{}};gO.parseViewBox=bO,gO.renderCallByParent=function(t,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var i=t.children,o=bO(t),a=xa(i,gO).map((function(t,r){return e.cloneElement(t,{viewBox:n||o,key:"label-".concat(r)})}));if(!r)return a;var s=function(t,n){return t?!0===t?v.createElement(gO,{key:"label-implicit",viewBox:n}):Ko(t)?v.createElement(gO,{key:"label-implicit",viewBox:n,value:t}):e.isValidElement(t)?t.type===gO?e.cloneElement(t,{key:"label-implicit",viewBox:n}):v.createElement(gO,{key:"label-implicit",content:t,viewBox:n}):jr(t)?v.createElement(gO,{key:"label-implicit",content:t,viewBox:n}):gr(t)?v.createElement(gO,mO({viewBox:n},t,{key:"label-implicit"})):null:null}(t.label,n||o);return[s].concat(uO(a))};var xO=function(e){var t=null==e?0:e.length;return t?e[t-1]:void 0};const wO=y(xO);function jO(e){return jO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jO(e)}var OO=["valueAccessor"],SO=["data","dataKey","clockWise","id","textBreakAll"];function PO(e){return function(e){if(Array.isArray(e))return AO(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return AO(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return AO(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function AO(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function kO(){return kO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},kO.apply(this,arguments)}function _O(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function EO(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_O(Object(n),!0).forEach((function(t){NO(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_O(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function NO(e,t,n){return t=function(e){var t=function(e,t){if("object"!=jO(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=jO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jO(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function MO(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var TO=function(e){return Array.isArray(e.value)?wO(e.value):e.value};function CO(e){var t=e.valueAccessor,n=void 0===t?TO:t,r=MO(e,OO),i=r.data,o=r.dataKey,a=r.clockWise,s=r.id,c=r.textBreakAll,l=MO(r,SO);return i&&i.length?v.createElement(Ia,{className:"recharts-label-list"},i.map((function(e,t){var r=vo(o)?n(e,t):Oj(e&&e.payload,o),i=vo(s)?{}:{id:"".concat(s,"-").concat(t)};return v.createElement(gO,kO({},Sa(e,!0),l,i,{parentViewBox:e.parentViewBox,value:r,textBreakAll:c,viewBox:gO.parseViewBox(vo(a)?e:EO(EO({},e),{},{clockWise:a})),key:"label-".concat(t),index:t}))}))):null}function DO(e){return DO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},DO(e)}function IO(){return IO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},IO.apply(this,arguments)}function RO(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function BO(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?RO(Object(n),!0).forEach((function(t){LO(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):RO(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function LO(e,t,n){return t=function(e){var t=function(e,t){if("object"!=DO(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=DO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==DO(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}CO.displayName="LabelList",CO.renderCallByParent=function(t,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||!t.children&&r&&!t.label)return null;var i=xa(t.children,CO).map((function(t,r){return e.cloneElement(t,{data:n,key:"labelList-".concat(r)})}));return r?[function(e,t){return e?!0===e?v.createElement(CO,{key:"labelList-implicit",data:t}):v.isValidElement(e)||jr(e)?v.createElement(CO,{key:"labelList-implicit",data:t,content:e}):gr(e)?v.createElement(CO,kO({data:t},e,{key:"labelList-implicit"})):null:null}(t.label,n)].concat(PO(i)):i};var zO=function(e){var t=e.cx,n=e.cy,r=e.radius,i=e.angle,o=e.sign,a=e.isExternal,s=e.cornerRadius,c=e.cornerIsExternal,l=s*(a?1:-1)+r,u=Math.asin(s/l)/eO,f=c?i:i+o*u,d=c?i-o*u:i;return{center:nO(t,n,l,f),circleTangency:nO(t,n,r,f),lineTangency:nO(t,n,l*Math.cos(u*eO),d),theta:u}},FO=function(e){var t=e.cx,n=e.cy,r=e.innerRadius,i=e.outerRadius,o=e.startAngle,a=function(e,t){return Yo(t-e)*Math.min(Math.abs(t-e),359.999)}(o,e.endAngle),s=o+a,c=nO(t,n,i,o),l=nO(t,n,i,s),u="M ".concat(c.x,",").concat(c.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(a)>180),",").concat(+(o>s),",\n    ").concat(l.x,",").concat(l.y,"\n  ");if(r>0){var f=nO(t,n,r,o),d=nO(t,n,r,s);u+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(r,",").concat(r,",0,\n            ").concat(+(Math.abs(a)>180),",").concat(+(o<=s),",\n            ").concat(f.x,",").concat(f.y," Z")}else u+="L ".concat(t,",").concat(n," Z");return u},$O={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},UO=function(e){var t=BO(BO({},$O),e),n=t.cx,r=t.cy,i=t.innerRadius,o=t.outerRadius,a=t.cornerRadius,s=t.forceCornerRadius,c=t.cornerIsExternal,l=t.startAngle,u=t.endAngle,f=t.className;if(o<i||l===u)return null;var d,p=m("recharts-sector",f),h=o-i,y=Jo(a,h,0,!0);return d=y>0&&Math.abs(l-u)<360?function(e){var t=e.cx,n=e.cy,r=e.innerRadius,i=e.outerRadius,o=e.cornerRadius,a=e.forceCornerRadius,s=e.cornerIsExternal,c=e.startAngle,l=e.endAngle,u=Yo(l-c),f=zO({cx:t,cy:n,radius:i,angle:c,sign:u,cornerRadius:o,cornerIsExternal:s}),d=f.circleTangency,p=f.lineTangency,h=f.theta,y=zO({cx:t,cy:n,radius:i,angle:l,sign:-u,cornerRadius:o,cornerIsExternal:s}),m=y.circleTangency,v=y.lineTangency,g=y.theta,b=s?Math.abs(c-l):Math.abs(c-l)-h-g;if(b<0)return a?"M ".concat(p.x,",").concat(p.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*-o,",0\n      "):FO({cx:t,cy:n,innerRadius:r,outerRadius:i,startAngle:c,endAngle:l});var x="M ".concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(u<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(b>180),",").concat(+(u<0),",").concat(m.x,",").concat(m.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(u<0),",").concat(v.x,",").concat(v.y,"\n  ");if(r>0){var w=zO({cx:t,cy:n,radius:r,angle:c,sign:u,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),j=w.circleTangency,O=w.lineTangency,S=w.theta,P=zO({cx:t,cy:n,radius:r,angle:l,sign:-u,isExternal:!0,cornerRadius:o,cornerIsExternal:s}),A=P.circleTangency,k=P.lineTangency,_=P.theta,E=s?Math.abs(c-l):Math.abs(c-l)-S-_;if(E<0&&0===o)return"".concat(x,"L").concat(t,",").concat(n,"Z");x+="L".concat(k.x,",").concat(k.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(u<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(r,",").concat(r,",0,").concat(+(E>180),",").concat(+(u>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(u<0),",").concat(O.x,",").concat(O.y,"Z")}else x+="L".concat(t,",").concat(n,"Z");return x}({cx:n,cy:r,innerRadius:i,outerRadius:o,cornerRadius:Math.min(y,h/2),forceCornerRadius:s,cornerIsExternal:c,startAngle:l,endAngle:u}):FO({cx:n,cy:r,innerRadius:i,outerRadius:o,startAngle:l,endAngle:u}),v.createElement("path",IO({},Sa(t,!0),{className:p,d:d,role:"img"}))};function WO(e){return WO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},WO(e)}function qO(){return qO=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qO.apply(this,arguments)}function HO(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function VO(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?HO(Object(n),!0).forEach((function(t){YO(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):HO(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function YO(e,t,n){return t=function(e){var t=function(e,t){if("object"!=WO(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=WO(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==WO(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var XO={curveBasisClosed:function(e){return new Js(e)},curveBasisOpen:function(e){return new ec(e)},curveBasis:function(e){return new Qs(e)},curveBumpX:function(e){return new Ts(e,!0)},curveBumpY:function(e){return new Ts(e,!1)},curveLinearClosed:function(e){return new tc(e)},curveLinear:ks,curveMonotoneX:function(e){return new ac(e)},curveMonotoneY:function(e){return new sc(e)},curveNatural:function(e){return new lc(e)},curveStep:function(e){return new fc(e,.5)},curveStepAfter:function(e){return new fc(e,1)},curveStepBefore:function(e){return new fc(e,0)}},GO=function(e){return e.x===+e.x&&e.y===+e.y},KO=function(e){return e.x},ZO=function(e){return e.y},QO=function(e){var t,n=e.type,r=void 0===n?"linear":n,i=e.points,o=void 0===i?[]:i,a=e.baseLine,s=e.layout,c=e.connectNulls,l=void 0!==c&&c,u=function(e,t){if(jr(e))return e;var n="curve".concat(fs(e));return"curveMonotone"!==n&&"curveBump"!==n||!t?XO[n]||ks:XO["".concat(n).concat("vertical"===t?"Y":"X")]}(r,s),f=l?o.filter((function(e){return GO(e)})):o;if(Array.isArray(a)){var d=l?a.filter((function(e){return GO(e)})):a,p=f.map((function(e,t){return VO(VO({},e),{},{base:d[t]})}));return t="vertical"===s?Ms().y(ZO).x1(KO).x0((function(e){return e.base.x})):Ms().x(KO).y1(ZO).y0((function(e){return e.base.y})),t.defined(GO).curve(u),t(p)}return(t="vertical"===s&&Go(a)?Ms().y(ZO).x1(KO).x0(a):Go(a)?Ms().x(KO).y1(ZO).y0(a):Ns().x(KO).y(ZO)).defined(GO).curve(u),t(f)},JO=function(e){var t=e.className,n=e.points,r=e.path,i=e.pathRef;if(!(n&&n.length||r))return null;var o=n&&n.length?QO(e):r;return v.createElement("path",qO({},Sa(e,!1),la(e),{className:m("recharts-curve",t),d:o,ref:i}))},eS=Object.getOwnPropertyNames,tS=Object.getOwnPropertySymbols,nS=Object.prototype.hasOwnProperty;function rS(e,t){return function(n,r,i){return e(n,r,i)&&t(n,r,i)}}function iS(e){return function(t,n,r){if(!t||!n||"object"!=typeof t||"object"!=typeof n)return e(t,n,r);var i=r.cache,o=i.get(t),a=i.get(n);if(o&&a)return o===n&&a===t;i.set(t,n),i.set(n,t);var s=e(t,n,r);return i.delete(t),i.delete(n),s}}function oS(e){return eS(e).concat(tS(e))}var aS=Object.hasOwn||function(e,t){return nS.call(e,t)};function sS(e,t){return e||t?e===t:e===t||e!=e&&t!=t}var cS="_owner",lS=Object.getOwnPropertyDescriptor,uS=Object.keys;function fS(e,t,n){var r=e.length;if(t.length!==r)return!1;for(;r-- >0;)if(!n.equals(e[r],t[r],r,r,e,t,n))return!1;return!0}function dS(e,t){return sS(e.getTime(),t.getTime())}function pS(e,t,n){if(e.size!==t.size)return!1;for(var r,i,o={},a=e.entries(),s=0;(r=a.next())&&!r.done;){for(var c=t.entries(),l=!1,u=0;(i=c.next())&&!i.done;){var f=r.value,d=f[0],p=f[1],h=i.value,y=h[0],m=h[1];l||o[u]||!(l=n.equals(d,y,s,u,e,t,n)&&n.equals(p,m,d,y,e,t,n))||(o[u]=!0),u++}if(!l)return!1;s++}return!0}function hS(e,t,n){var r,i=uS(e),o=i.length;if(uS(t).length!==o)return!1;for(;o-- >0;){if((r=i[o])===cS&&(e.$$typeof||t.$$typeof)&&e.$$typeof!==t.$$typeof)return!1;if(!aS(t,r)||!n.equals(e[r],t[r],r,r,e,t,n))return!1}return!0}function yS(e,t,n){var r,i,o,a=oS(e),s=a.length;if(oS(t).length!==s)return!1;for(;s-- >0;){if((r=a[s])===cS&&(e.$$typeof||t.$$typeof)&&e.$$typeof!==t.$$typeof)return!1;if(!aS(t,r))return!1;if(!n.equals(e[r],t[r],r,r,e,t,n))return!1;if(i=lS(e,r),o=lS(t,r),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable))return!1}return!0}function mS(e,t){return sS(e.valueOf(),t.valueOf())}function vS(e,t){return e.source===t.source&&e.flags===t.flags}function gS(e,t,n){if(e.size!==t.size)return!1;for(var r,i,o={},a=e.values();(r=a.next())&&!r.done;){for(var s=t.values(),c=!1,l=0;(i=s.next())&&!i.done;)c||o[l]||!(c=n.equals(r.value,i.value,r.value,i.value,e,t,n))||(o[l]=!0),l++;if(!c)return!1}return!0}function bS(e,t){var n=e.length;if(t.length!==n)return!1;for(;n-- >0;)if(e[n]!==t[n])return!1;return!0}var xS=Array.isArray,wS="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,jS=Object.assign,OS=Object.prototype.toString.call.bind(Object.prototype.toString);var SS=PS();function PS(e){void 0===e&&(e={});var t,n=e.circular,r=void 0!==n&&n,i=e.createInternalComparator,o=e.createState,a=e.strict,s=void 0!==a&&a,c=function(e){var t=e.circular,n=e.createCustomConfig,r=e.strict,i={areArraysEqual:r?yS:fS,areDatesEqual:dS,areMapsEqual:r?rS(pS,yS):pS,areObjectsEqual:r?yS:hS,arePrimitiveWrappersEqual:mS,areRegExpsEqual:vS,areSetsEqual:r?rS(gS,yS):gS,areTypedArraysEqual:r?yS:bS};if(n&&(i=jS({},i,n(i))),t){var o=iS(i.areArraysEqual),a=iS(i.areMapsEqual),s=iS(i.areObjectsEqual),c=iS(i.areSetsEqual);i=jS({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:s,areSetsEqual:c})}return i}(e),l=function(e){var t=e.areArraysEqual,n=e.areDatesEqual,r=e.areMapsEqual,i=e.areObjectsEqual,o=e.arePrimitiveWrappersEqual,a=e.areRegExpsEqual,s=e.areSetsEqual,c=e.areTypedArraysEqual;return function(e,l,u){if(e===l)return!0;if(null==e||null==l||"object"!=typeof e||"object"!=typeof l)return e!=e&&l!=l;var f=e.constructor;if(f!==l.constructor)return!1;if(f===Object)return i(e,l,u);if(xS(e))return t(e,l,u);if(null!=wS&&wS(e))return c(e,l,u);if(f===Date)return n(e,l,u);if(f===RegExp)return a(e,l,u);if(f===Map)return r(e,l,u);if(f===Set)return s(e,l,u);var d=OS(e);return"[object Date]"===d?n(e,l,u):"[object RegExp]"===d?a(e,l,u):"[object Map]"===d?r(e,l,u):"[object Set]"===d?s(e,l,u):"[object Object]"===d?"function"!=typeof e.then&&"function"!=typeof l.then&&i(e,l,u):"[object Arguments]"===d?i(e,l,u):("[object Boolean]"===d||"[object Number]"===d||"[object String]"===d)&&o(e,l,u)}}(c),u=i?i(l):(t=l,function(e,n,r,i,o,a,s){return t(e,n,s)});return function(e){var t=e.circular,n=e.comparator,r=e.createState,i=e.equals,o=e.strict;if(r)return function(e,a){var s=r(),c=s.cache,l=void 0===c?t?new WeakMap:void 0:c,u=s.meta;return n(e,a,{cache:l,equals:i,meta:u,strict:o})};if(t)return function(e,t){return n(e,t,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(e,t){return n(e,t,a)}}({circular:r,comparator:l,createState:o,equals:u,strict:s})}function AS(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1;requestAnimationFrame((function r(i){n<0&&(n=i),i-n>t?(e(i),n=-1):function(e){"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(e)}(r)}))}function kS(e){return kS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kS(e)}function _S(e){return function(e){if(Array.isArray(e))return e}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return ES(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ES(e,t)}(e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ES(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function NS(){var e=function(){return null},t=!1,n=function n(r){if(!t){if(Array.isArray(r)){if(!r.length)return;var i=_S(r),o=i[0],a=i.slice(1);return"number"==typeof o?void AS(n.bind(null,a),o):(n(o),void AS(n.bind(null,a)))}"object"===kS(r)&&e(r),"function"==typeof r&&r()}};return{stop:function(){t=!0},start:function(e){t=!1,n(e)},subscribe:function(t){return e=t,function(){e=function(){return null}}}}}function MS(e){return(MS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function TS(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function CS(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?TS(Object(n),!0).forEach((function(t){DS(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):TS(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function DS(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==MS(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==MS(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===MS(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}PS({strict:!0}),PS({circular:!0}),PS({circular:!0,strict:!0}),PS({createInternalComparator:function(){return sS}}),PS({strict:!0,createInternalComparator:function(){return sS}}),PS({circular:!0,createInternalComparator:function(){return sS}}),PS({circular:!0,createInternalComparator:function(){return sS},strict:!0});var IS=function(e){return e},RS=function(e,t){return Object.keys(t).reduce((function(n,r){return CS(CS({},n),{},DS({},r,e(r,t[r])))}),{})},BS=function(e,t,n){return e.map((function(e){return"".concat((r=e,r.replace(/([A-Z])/g,(function(e){return"-".concat(e.toLowerCase())})))," ").concat(t,"ms ").concat(n);var r})).join(",")};function LS(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||FS(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zS(e){return function(e){if(Array.isArray(e))return $S(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||FS(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function FS(e,t){if(e){if("string"==typeof e)return $S(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$S(e,t):void 0}}function $S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var US=1e-4,WS=function(e,t){return[0,3*e,3*t-6*e,3*e-3*t+1]},qS=function(e,t){return e.map((function(e,n){return e*Math.pow(t,n)})).reduce((function(e,t){return e+t}))},HS=function(e,t){return function(n){var r=WS(e,t);return qS(r,n)}},VS=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0],i=t[1],o=t[2],a=t[3];if(1===t.length)switch(t[0]){case"linear":r=0,i=0,o=1,a=1;break;case"ease":r=.25,i=.1,o=.25,a=1;break;case"ease-in":r=.42,i=0,o=1,a=1;break;case"ease-out":r=.42,i=0,o=.58,a=1;break;case"ease-in-out":r=0,i=0,o=.58,a=1;break;default:var s=t[0].split("(");if("cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length){var c=s[1].split(")")[0].split(",").map((function(e){return parseFloat(e)})),l=LS(c,4);r=l[0],i=l[1],o=l[2],a=l[3]}}var u,f,d=HS(r,o),p=HS(i,a),h=(u=r,f=o,function(e){var t=WS(u,f),n=[].concat(zS(t.map((function(e,t){return e*t})).slice(1)),[0]);return qS(n,e)}),y=function(e){for(var t,n=e>1?1:e,r=n,i=0;i<8;++i){var o=d(r)-n,a=h(r);if(Math.abs(o-n)<US||a<US)return p(r);r=(t=r-o/a)>1?1:t<0?0:t}return p(r)};return y.isStepper=!1,y},YS=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0];if("string"==typeof r)switch(r){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return VS(r);case"spring":return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.stiff,n=void 0===t?100:t,r=e.damping,i=void 0===r?8:r,o=e.dt,a=void 0===o?17:o,s=function(e,t,r){var o=r+(-(e-t)*n-r*i)*a/1e3,s=r*a/1e3+e;return Math.abs(s-t)<US&&Math.abs(o)<US?[t,0]:[s,o]};return s.isStepper=!0,s.dt=a,s}();default:if("cubic-bezier"===r.split("(")[0])return VS(r)}return"function"==typeof r?r:null};function XS(e){return XS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},XS(e)}function GS(e){return function(e){if(Array.isArray(e))return tP(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||eP(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function KS(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ZS(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?KS(Object(n),!0).forEach((function(t){QS(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):KS(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function QS(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==XS(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==XS(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===XS(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function JS(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||eP(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eP(e,t){if(e){if("string"==typeof e)return tP(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?tP(e,t):void 0}}function tP(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var nP=function(e,t,n){return e+(t-e)*n},rP=function(e){return e.from!==e.to},iP=function e(t,n,r){var i=RS((function(e,n){if(rP(n)){var r=JS(t(n.from,n.to,n.velocity),2),i=r[0],o=r[1];return ZS(ZS({},n),{},{from:i,velocity:o})}return n}),n);return r<1?RS((function(e,t){return rP(t)?ZS(ZS({},t),{},{velocity:nP(t.velocity,i[e].velocity,r),from:nP(t.from,i[e].from,r)}):t}),n):e(t,i,r-1)};const oP=function(e,t,n,r,i){var o,a,s,c,l=(o=e,a=t,[Object.keys(o),Object.keys(a)].reduce((function(e,t){return e.filter((function(e){return t.includes(e)}))}))),u=l.reduce((function(n,r){return ZS(ZS({},n),{},QS({},r,[e[r],t[r]]))}),{}),f=l.reduce((function(n,r){return ZS(ZS({},n),{},QS({},r,{from:e[r],velocity:0,to:t[r]}))}),{}),d=-1,p=function(){return null};return p=n.isStepper?function(r){s||(s=r);var o=(r-s)/n.dt;f=iP(n,f,o),i(ZS(ZS(ZS({},e),t),RS((function(e,t){return t.from}),f))),s=r,Object.values(f).filter(rP).length&&(d=requestAnimationFrame(p))}:function(o){c||(c=o);var a=(o-c)/r,s=RS((function(e,t){return nP.apply(void 0,GS(t).concat([n(a)]))}),u);if(i(ZS(ZS(ZS({},e),t),s)),a<1)d=requestAnimationFrame(p);else{var l=RS((function(e,t){return nP.apply(void 0,GS(t).concat([n(1)]))}),u);i(ZS(ZS(ZS({},e),t),l))}},function(){return requestAnimationFrame(p),function(){cancelAnimationFrame(d)}}};function aP(e){return aP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},aP(e)}var sP=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function cP(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function lP(e){return function(e){if(Array.isArray(e))return uP(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return uP(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return uP(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uP(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function fP(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function dP(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fP(Object(n),!0).forEach((function(t){pP(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fP(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function pP(e,t,n){return(t=yP(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function hP(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,yP(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function yP(e){var t=function(e,t){if("object"!==aP(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!==aP(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===aP(t)?t:String(t)}function mP(e,t){return mP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},mP(e,t)}function vP(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(_o){return!1}}();return function(){var n,r=xP(e);if(t){var i=xP(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return gP(this,n)}}function gP(e,t){if(t&&("object"===aP(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return bP(e)}function bP(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xP(e){return xP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xP(e)}var wP=function(){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&mP(e,t)}(n,e.PureComponent);var t=vP(n);function n(e,r){var i;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,n);var o=(i=t.call(this,e,r)).props,a=o.isActive,s=o.attributeName,c=o.from,l=o.to,u=o.steps,f=o.children,d=o.duration;if(i.handleStyleChange=i.handleStyleChange.bind(bP(i)),i.changeStyle=i.changeStyle.bind(bP(i)),!a||d<=0)return i.state={style:{}},"function"==typeof f&&(i.state={style:l}),gP(i);if(u&&u.length)i.state={style:u[0].style};else if(c){if("function"==typeof f)return i.state={style:c},gP(i);i.state={style:s?pP({},s,c):c}}else i.state={style:{}};return i}return hP(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.isActive,n=e.canBegin;this.mounted=!0,t&&n&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(e){var t=this.props,n=t.isActive,r=t.canBegin,i=t.attributeName,o=t.shouldReAnimate,a=t.to,s=t.from,c=this.state.style;if(r)if(n){if(!(SS(e.to,a)&&e.canBegin&&e.isActive)){var l=!e.canBegin||!e.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var u=l||o?s:e.to;if(this.state&&c){var f={style:i?pP({},i,u):u};(i&&c[i]!==u||!i&&c!==u)&&this.setState(f)}this.runAnimation(dP(dP({},this.props),{},{from:u,begin:0}))}}else{var d={style:i?pP({},i,a):a};this.state&&c&&(i&&c[i]!==a||!i&&c!==a)&&this.setState(d)}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var e=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}},{key:"handleStyleChange",value:function(e){this.changeStyle(e)}},{key:"changeStyle",value:function(e){this.mounted&&this.setState({style:e})}},{key:"runJSAnimation",value:function(e){var t=this,n=e.from,r=e.to,i=e.duration,o=e.easing,a=e.begin,s=e.onAnimationEnd,c=e.onAnimationStart,l=oP(n,r,YS(o),i,this.changeStyle);this.manager.start([c,a,function(){t.stopJSAnimation=l()},i,s])}},{key:"runStepAnimation",value:function(e){var t=this,n=e.steps,r=e.begin,i=e.onAnimationStart,o=n[0],a=o.style,s=o.duration,c=void 0===s?0:s;return this.manager.start([i].concat(lP(n.reduce((function(e,r,i){if(0===i)return e;var o=r.duration,a=r.easing,s=void 0===a?"ease":a,c=r.style,l=r.properties,u=r.onAnimationEnd,f=i>0?n[i-1]:r,d=l||Object.keys(c);if("function"==typeof s||"spring"===s)return[].concat(lP(e),[t.runJSAnimation.bind(t,{from:f.style,to:c,duration:o,easing:s}),o]);var p=BS(d,o,s),h=dP(dP(dP({},f.style),c),{},{transition:p});return[].concat(lP(e),[h,o,u]).filter(IS)}),[a,Math.max(c,r)])),[e.onAnimationEnd]))}},{key:"runAnimation",value:function(e){this.manager||(this.manager=NS());var t=e.begin,n=e.duration,r=e.attributeName,i=e.to,o=e.easing,a=e.onAnimationStart,s=e.onAnimationEnd,c=e.steps,l=e.children,u=this.manager;if(this.unSubscribe=u.subscribe(this.handleStyleChange),"function"!=typeof o&&"function"!=typeof l&&"spring"!==o)if(c.length>1)this.runStepAnimation(e);else{var f=r?pP({},r,i):i,d=BS(Object.keys(f),n,o);u.start([a,t,dP(dP({},f),{},{transition:d}),n,s])}else this.runJSAnimation(e)}},{key:"render",value:function(){var t=this.props,n=t.children;t.begin;var r=t.duration;t.attributeName,t.easing;var i=t.isActive;t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart;var o=cP(t,sP),a=e.Children.count(n),s=this.state.style;if("function"==typeof n)return n(s);if(!i||0===a||r<=0)return n;var c=function(t){var n=t.props,r=n.style,i=void 0===r?{}:r,a=n.className;return e.cloneElement(t,dP(dP({},o),{},{style:dP(dP({},i),s),className:a}))};return 1===a?c(e.Children.only(n)):v.createElement("div",null,e.Children.map(n,(function(e){return c(e)})))}}]),n}();function jP(e){return jP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jP(e)}function OP(){return OP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},OP.apply(this,arguments)}function SP(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return PP(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return PP(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function PP(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function AP(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function kP(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?AP(Object(n),!0).forEach((function(t){_P(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):AP(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function _P(e,t,n){return t=function(e){var t=function(e,t){if("object"!=jP(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=jP(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jP(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}wP.displayName="Animate",wP.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},wP.propTypes={from:q.oneOfType([q.object,q.string]),to:q.oneOfType([q.object,q.string]),attributeName:q.string,duration:q.number,begin:q.number,easing:q.oneOfType([q.string,q.func]),steps:q.arrayOf(q.shape({duration:q.number.isRequired,style:q.object.isRequired,easing:q.oneOfType([q.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),q.func]),properties:q.arrayOf("string"),onAnimationEnd:q.func})),children:q.oneOfType([q.node,q.func]),isActive:q.bool,canBegin:q.bool,onAnimationEnd:q.func,shouldReAnimate:q.bool,onAnimationStart:q.func,onAnimationReStart:q.func},q.object,q.object,q.object,q.element,q.object,q.object,q.object,q.oneOfType([q.array,q.element]),q.any;var EP=function(e,t,n,r,i){var o,a=Math.min(Math.abs(n)/2,Math.abs(r)/2),s=r>=0?1:-1,c=n>=0?1:-1,l=r>=0&&n>=0||r<0&&n<0?1:0;if(a>0&&i instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=i[f]>a?a:i[f];o="M".concat(e,",").concat(t+s*u[0]),u[0]>0&&(o+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(l,",").concat(e+c*u[0],",").concat(t)),o+="L ".concat(e+n-c*u[1],",").concat(t),u[1]>0&&(o+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(l,",\n        ").concat(e+n,",").concat(t+s*u[1])),o+="L ".concat(e+n,",").concat(t+r-s*u[2]),u[2]>0&&(o+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(l,",\n        ").concat(e+n-c*u[2],",").concat(t+r)),o+="L ".concat(e+c*u[3],",").concat(t+r),u[3]>0&&(o+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(l,",\n        ").concat(e,",").concat(t+r-s*u[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var d=Math.min(a,i);o="M ".concat(e,",").concat(t+s*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+n-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+n,",").concat(t+s*d,"\n            L ").concat(e+n,",").concat(t+r-s*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e+n-c*d,",").concat(t+r,"\n            L ").concat(e+c*d,",").concat(t+r,"\n            A ").concat(d,",").concat(d,",0,0,").concat(l,",").concat(e,",").concat(t+r-s*d," Z")}else o="M ".concat(e,",").concat(t," h ").concat(n," v ").concat(r," h ").concat(-n," Z");return o},NP=function(e,t){if(!e||!t)return!1;var n=e.x,r=e.y,i=t.x,o=t.y,a=t.width,s=t.height;if(Math.abs(a)>0&&Math.abs(s)>0){var c=Math.min(i,i+a),l=Math.max(i,i+a),u=Math.min(o,o+s),f=Math.max(o,o+s);return n>=c&&n<=l&&r>=u&&r<=f}return!1},MP={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},TP=function(t){var n=kP(kP({},MP),t),r=e.useRef(),i=SP(e.useState(-1),2),o=i[0],a=i[1];e.useEffect((function(){if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(t){}}),[]);var s=n.x,c=n.y,l=n.width,u=n.height,f=n.radius,d=n.className,p=n.animationEasing,h=n.animationDuration,y=n.animationBegin,g=n.isAnimationActive,b=n.isUpdateAnimationActive;if(s!==+s||c!==+c||l!==+l||u!==+u||0===l||0===u)return null;var x=m("recharts-rectangle",d);return b?v.createElement(wP,{canBegin:o>0,from:{width:l,height:u,x:s,y:c},to:{width:l,height:u,x:s,y:c},duration:h,animationEasing:p,isActive:b},(function(e){var t=e.width,i=e.height,a=e.x,s=e.y;return v.createElement(wP,{canBegin:o>0,from:"0px ".concat(-1===o?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,isActive:g,easing:p},v.createElement("path",OP({},Sa(n,!0),{className:x,d:EP(a,s,t,i,f),ref:r})))})):v.createElement("path",OP({},Sa(n,!0),{className:x,d:EP(s,c,l,u,f)}))},CP=["points","className","baseLinePoints","connectNulls"];function DP(){return DP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},DP.apply(this,arguments)}function IP(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function RP(e){return function(e){if(Array.isArray(e))return BP(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return BP(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return BP(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function BP(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var LP=function(e){return e&&e.x===+e.x&&e.y===+e.y},zP=function(e,t){var n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach((function(e){LP(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])})),LP(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t}(e);t&&(n=[n.reduce((function(e,t){return[].concat(RP(e),RP(t))}),[])]);var r=n.map((function(e){return e.reduce((function(e,t,n){return"".concat(e).concat(0===n?"M":"L").concat(t.x,",").concat(t.y)}),"")})).join("");return 1===n.length?"".concat(r,"Z"):r},FP=function(e){var t=e.points,n=e.className,r=e.baseLinePoints,i=e.connectNulls,o=IP(e,CP);if(!t||!t.length)return null;var a=m("recharts-polygon",n);if(r&&r.length){var s=o.stroke&&"none"!==o.stroke,c=function(e,t,n){var r=zP(e,n);return"".concat("Z"===r.slice(-1)?r.slice(0,-1):r,"L").concat(zP(t.reverse(),n).slice(1))}(t,r,i);return v.createElement("g",{className:a},v.createElement("path",DP({},Sa(o,!0),{fill:"Z"===c.slice(-1)?o.fill:"none",stroke:"none",d:c})),s?v.createElement("path",DP({},Sa(o,!0),{fill:"none",d:zP(t,i)})):null,s?v.createElement("path",DP({},Sa(o,!0),{fill:"none",d:zP(r,i)})):null)}var l=zP(t,i);return v.createElement("path",DP({},Sa(o,!0),{fill:"Z"===l.slice(-1)?o.fill:"none",className:a,d:l}))};function $P(){return $P=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$P.apply(this,arguments)}var UP=function(e){var t=e.cx,n=e.cy,r=e.r,i=e.className,o=m("recharts-dot",i);return t===+t&&n===+n&&r===+r?v.createElement("circle",$P({},Sa(e,!1),la(e),{className:o,cx:t,cy:n,r:r})):null};function WP(e){return WP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},WP(e)}var qP=["x","y","top","left","width","height","className"];function HP(){return HP=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},HP.apply(this,arguments)}function VP(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function YP(e,t,n){return t=function(e){var t=function(e,t){if("object"!=WP(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=WP(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==WP(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function XP(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var GP=function(e,t,n,r,i,o){return"M".concat(e,",").concat(i,"v").concat(r,"M").concat(o,",").concat(t,"h").concat(n)},KP=function(e){var t=e.x,n=void 0===t?0:t,r=e.y,i=void 0===r?0:r,o=e.top,a=void 0===o?0:o,s=e.left,c=void 0===s?0:s,l=e.width,u=void 0===l?0:l,f=e.height,d=void 0===f?0:f,p=e.className,h=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?VP(Object(n),!0).forEach((function(t){YP(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):VP(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({x:n,y:i,top:a,left:c,width:u,height:d},XP(e,qP));return Go(n)&&Go(i)&&Go(u)&&Go(d)&&Go(a)&&Go(c)?v.createElement("path",HP({},Sa(h,!0),{className:m("recharts-cross",p),d:GP(n,i,u,d,a,c)})):null},ZP=kx,QP=_x,JP=ad;var eA=function(e,t){return e&&e.length?ZP(e,JP(t),QP):void 0};const tA=y(eA);var nA=kx,rA=ad,iA=Dx;var oA=function(e,t){return e&&e.length?nA(e,rA(t),iA):void 0};const aA=y(oA);var sA=["cx","cy","angle","ticks","axisLine"],cA=["ticks","tick","angle","tickFormatter","stroke"];function lA(e){return lA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},lA(e)}function uA(){return uA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},uA.apply(this,arguments)}function fA(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function dA(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?fA(Object(n),!0).forEach((function(t){bA(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):fA(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function pA(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function hA(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,xA(r.key),r)}}function yA(e,t,n){return t=vA(t),function(e,t){if(t&&("object"===lA(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,mA()?Reflect.construct(t,n||[],vA(e).constructor):t.apply(e,n))}function mA(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(mA=function(){return!!e})()}function vA(e){return vA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},vA(e)}function gA(e,t){return gA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},gA(e,t)}function bA(e,t,n){return(t=xA(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xA(e){var t=function(e,t){if("object"!=lA(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=lA(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lA(t)?t:t+""}var wA=function(){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),yA(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&gA(e,t)}(t,e.PureComponent),n=t,r=[{key:"getTickValueCoord",value:function(e){var t=e.coordinate,n=this.props,r=n.angle,i=n.cx,o=n.cy;return nO(i,o,t,r)}},{key:"getTickTextAnchor",value:function(){var e;switch(this.props.orientation){case"left":e="end";break;case"right":e="start";break;default:e="middle"}return e}},{key:"getViewBox",value:function(){var e=this.props,t=e.cx,n=e.cy,r=e.angle,i=e.ticks,o=tA(i,(function(e){return e.coordinate||0}));return{cx:t,cy:n,startAngle:r,endAngle:r,innerRadius:aA(i,(function(e){return e.coordinate||0})).coordinate||0,outerRadius:o.coordinate||0}}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,n=e.cy,r=e.angle,i=e.ticks,o=e.axisLine,a=pA(e,sA),s=i.reduce((function(e,t){return[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)]}),[1/0,-1/0]),c=nO(t,n,s[0],r),l=nO(t,n,s[1],r),u=dA(dA(dA({},Sa(a,!1)),{},{fill:"none"},Sa(o,!1)),{},{x1:c.x,y1:c.y,x2:l.x,y2:l.y});return v.createElement("line",uA({className:"recharts-polar-radius-axis-line"},u))}},{key:"renderTicks",value:function(){var e=this,n=this.props,r=n.ticks,i=n.tick,o=n.angle,a=n.tickFormatter,s=n.stroke,c=pA(n,cA),l=this.getTickTextAnchor(),u=Sa(c,!1),f=Sa(i,!1),d=r.map((function(n,r){var c=e.getTickValueCoord(n),d=dA(dA(dA(dA({textAnchor:l,transform:"rotate(".concat(90-o,", ").concat(c.x,", ").concat(c.y,")")},u),{},{stroke:"none",fill:s},f),{},{index:r},c),{},{payload:n});return v.createElement(Ia,uA({className:m("recharts-polar-radius-axis-tick",sO(i)),key:"tick-".concat(n.coordinate)},ua(e.props,n,r)),t.renderTickItem(i,d,a?a(n.value,r):n.value))}));return v.createElement(Ia,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var e=this.props,t=e.ticks,n=e.axisLine,r=e.tick;return t&&t.length?v.createElement(Ia,{className:m("recharts-polar-radius-axis",this.props.className)},n&&this.renderAxisLine(),r&&this.renderTicks(),gO.renderCallByParent(this.props,this.getViewBox())):null}}],i=[{key:"renderTickItem",value:function(e,t,n){return v.isValidElement(e)?v.cloneElement(e,t):jr(e)?e(t):v.createElement(Wy,uA({},t,{className:"recharts-polar-radius-axis-tick-value"}),n)}}],r&&hA(n.prototype,r),i&&hA(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();function jA(e){return jA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jA(e)}function OA(){return OA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},OA.apply(this,arguments)}function SA(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function PA(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?SA(Object(n),!0).forEach((function(t){MA(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):SA(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AA(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,TA(r.key),r)}}function kA(e,t,n){return t=EA(t),function(e,t){if(t&&("object"===jA(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,_A()?Reflect.construct(t,n||[],EA(e).constructor):t.apply(e,n))}function _A(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_A=function(){return!!e})()}function EA(e){return EA=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},EA(e)}function NA(e,t){return NA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},NA(e,t)}function MA(e,t,n){return(t=TA(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function TA(e){var t=function(e,t){if("object"!=jA(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=jA(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jA(t)?t:t+""}bA(wA,"displayName","PolarRadiusAxis"),bA(wA,"axisType","radiusAxis"),bA(wA,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var CA=Math.PI/180,DA=1e-5,IA=function(){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),kA(this,t,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&NA(e,t)}(t,e.PureComponent),n=t,r=[{key:"getTickLineCoord",value:function(e){var t=this.props,n=t.cx,r=t.cy,i=t.radius,o=t.orientation,a=t.tickSize||8,s=nO(n,r,i,e.coordinate),c=nO(n,r,i+("inner"===o?-1:1)*a,e.coordinate);return{x1:s.x,y1:s.y,x2:c.x,y2:c.y}}},{key:"getTickTextAnchor",value:function(e){var t=this.props.orientation,n=Math.cos(-e.coordinate*CA);return n>DA?"outer"===t?"start":"end":n<-DA?"outer"===t?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.cx,n=e.cy,r=e.radius,i=e.axisLine,o=e.axisLineType,a=PA(PA({},Sa(this.props,!1)),{},{fill:"none"},Sa(i,!1));if("circle"===o)return v.createElement(UP,OA({className:"recharts-polar-angle-axis-line"},a,{cx:t,cy:n,r:r}));var s=this.props.ticks.map((function(e){return nO(t,n,r,e.coordinate)}));return v.createElement(FP,OA({className:"recharts-polar-angle-axis-line"},a,{points:s}))}},{key:"renderTicks",value:function(){var e=this,n=this.props,r=n.ticks,i=n.tick,o=n.tickLine,a=n.tickFormatter,s=n.stroke,c=Sa(this.props,!1),l=Sa(i,!1),u=PA(PA({},c),{},{fill:"none"},Sa(o,!1)),f=r.map((function(n,r){var f=e.getTickLineCoord(n),d=PA(PA(PA({textAnchor:e.getTickTextAnchor(n)},c),{},{stroke:"none",fill:s},l),{},{index:r,payload:n,x:f.x2,y:f.y2});return v.createElement(Ia,OA({className:m("recharts-polar-angle-axis-tick",sO(i)),key:"tick-".concat(n.coordinate)},ua(e.props,n,r)),o&&v.createElement("line",OA({className:"recharts-polar-angle-axis-tick-line"},u,f)),i&&t.renderTickItem(i,d,a?a(n.value,r):n.value))}));return v.createElement(Ia,{className:"recharts-polar-angle-axis-ticks"},f)}},{key:"render",value:function(){var e=this.props,t=e.ticks,n=e.radius,r=e.axisLine;return n<=0||!t||!t.length?null:v.createElement(Ia,{className:m("recharts-polar-angle-axis",this.props.className)},r&&this.renderAxisLine(),this.renderTicks())}}],i=[{key:"renderTickItem",value:function(e,t,n){return v.isValidElement(e)?v.cloneElement(e,t):jr(e)?e(t):v.createElement(Wy,OA({},t,{className:"recharts-polar-angle-axis-tick-value"}),n)}}],r&&AA(n.prototype,r),i&&AA(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();MA(IA,"displayName","PolarAngleAxis"),MA(IA,"axisType","angleAxis"),MA(IA,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var RA=fu(Object.getPrototypeOf,Object),BA=sr,LA=RA,zA=cr,FA=Function.prototype,$A=Object.prototype,UA=FA.toString,WA=$A.hasOwnProperty,qA=UA.call(Object);const HA=y((function(e){if(!zA(e)||"[object Object]"!=BA(e))return!1;var t=LA(e);if(null===t)return!0;var n=WA.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&UA.call(n)==qA}));var VA=sr,YA=cr;const XA=y((function(e){return!0===e||!1===e||YA(e)&&"[object Boolean]"==VA(e)}));function GA(e){return GA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},GA(e)}function KA(){return KA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},KA.apply(this,arguments)}function ZA(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return QA(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return QA(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function QA(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function JA(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ek(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?JA(Object(n),!0).forEach((function(t){tk(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):JA(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function tk(e,t,n){return t=function(e){var t=function(e,t){if("object"!=GA(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=GA(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==GA(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var nk,rk=function(e,t,n,r,i){var o,a=n-r;return o="M ".concat(e,",").concat(t),o+="L ".concat(e+n,",").concat(t),o+="L ".concat(e+n-a/2,",").concat(t+i),o+="L ".concat(e+n-a/2-r,",").concat(t+i),o+="L ".concat(e,",").concat(t," Z")},ik={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},ok=function(t){var n=ek(ek({},ik),t),r=e.useRef(),i=ZA(e.useState(-1),2),o=i[0],a=i[1];e.useEffect((function(){if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(t){}}),[]);var s=n.x,c=n.y,l=n.upperWidth,u=n.lowerWidth,f=n.height,d=n.className,p=n.animationEasing,h=n.animationDuration,y=n.animationBegin,g=n.isUpdateAnimationActive;if(s!==+s||c!==+c||l!==+l||u!==+u||f!==+f||0===l&&0===u||0===f)return null;var b=m("recharts-trapezoid",d);return g?v.createElement(wP,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:f,x:s,y:c},to:{upperWidth:l,lowerWidth:u,height:f,x:s,y:c},duration:h,animationEasing:p,isActive:g},(function(e){var t=e.upperWidth,i=e.lowerWidth,a=e.height,s=e.x,c=e.y;return v.createElement(wP,{canBegin:o>0,from:"0px ".concat(-1===o?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,easing:p},v.createElement("path",KA({},Sa(n,!0),{className:b,d:rk(s,c,t,i,a),ref:r})))})):v.createElement("g",null,v.createElement("path",KA({},Sa(n,!0),{className:b,d:rk(s,c,l,u,f)})))},ak=["option","shapeType","propTransformer","activeClassName","isActive"];function sk(e){return sk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sk(e)}function ck(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function lk(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function uk(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lk(Object(n),!0).forEach((function(t){fk(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lk(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function fk(e,t,n){return t=function(e){var t=function(e,t){if("object"!=sk(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=sk(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==sk(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function dk(e,t){return uk(uk({},t),e)}function pk(e){var t=e.shapeType,n=e.elementProps;switch(t){case"rectangle":return v.createElement(TP,n);case"trapezoid":return v.createElement(ok,n);case"sector":return v.createElement(UO,n);case"symbols":if(function(e){return"symbols"===e}(t))return v.createElement(Pc,n);break;default:return null}}function hk(t){var n,r=t.option,i=t.shapeType,o=t.propTransformer,a=void 0===o?dk:o,s=t.activeClassName,c=void 0===s?"recharts-active-shape":s,l=t.isActive,u=ck(t,ak);if(e.isValidElement(r))n=e.cloneElement(r,uk(uk({},u),function(t){return e.isValidElement(t)?t.props:t}(r)));else if(jr(r))n=r(u);else if(HA(r)&&!XA(r)){var f=a(r,u);n=v.createElement(pk,{shapeType:i,elementProps:f})}else{var d=u;n=v.createElement(pk,{shapeType:i,elementProps:d})}return l?v.createElement(Ia,{className:c},n):n}function yk(e,t){return null!=t&&"trapezoids"in e.props}function mk(e,t){return null!=t&&"sectors"in e.props}function vk(e,t){return null!=t&&"points"in e.props}function gk(e,t){var n,r,i=e.x===(null==t||null===(n=t.labelViewBox)||void 0===n?void 0:n.x)||e.x===t.x,o=e.y===(null==t||null===(r=t.labelViewBox)||void 0===r?void 0:r.y)||e.y===t.y;return i&&o}function bk(e,t){var n=e.endAngle===t.endAngle,r=e.startAngle===t.startAngle;return n&&r}function xk(e,t){var n=e.x===t.x,r=e.y===t.y,i=e.z===t.z;return n&&r&&i}function wk(e){var t=e.activeTooltipItem,n=e.graphicalItem,r=e.itemData,i=function(e,t){var n;return yk(e,t)?n="trapezoids":mk(e,t)?n="sectors":vk(e,t)&&(n="points"),n}(n,t),o=function(e,t){var n,r;return yk(e,t)?null===(n=t.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:mk(e,t)?null===(r=t.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:vk(e,t)?t.payload:{}}(n,t),a=r.filter((function(e,r){var a=Xx(o,e),s=n.props[i].filter((function(e){var r=function(e,t){var n;return yk(e,t)?n=gk:mk(e,t)?n=bk:vk(e,t)&&(n=xk),n}(n,t);return r(e,t)})),c=n.props[i].indexOf(s[s.length-1]);return a&&r===c}));return r.indexOf(a[a.length-1])}function jk(e){return jk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jk(e)}function Ok(){return Ok=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ok.apply(this,arguments)}function Sk(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pk(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Sk(Object(n),!0).forEach((function(t){Mk(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Sk(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Ak(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Tk(r.key),r)}}function kk(e,t,n){return t=Ek(t),function(e,t){if(t&&("object"===jk(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,_k()?Reflect.construct(t,n||[],Ek(e).constructor):t.apply(e,n))}function _k(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_k=function(){return!!e})()}function Ek(e){return Ek=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ek(e)}function Nk(e,t){return Nk=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Nk(e,t)}function Mk(e,t,n){return(t=Tk(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Tk(e){var t=function(e,t){if("object"!=jk(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=jk(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jk(t)?t:t+""}var Ck=function(){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),Mk(n=kk(this,t,[e]),"pieRef",null),Mk(n,"sectorRefs",[]),Mk(n,"id",Qo("recharts-pie-")),Mk(n,"handleAnimationEnd",(function(){var e=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),jr(e)&&e()})),Mk(n,"handleAnimationStart",(function(){var e=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),jr(e)&&e()})),n.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nk(e,t)}(t,e.PureComponent),n=t,r=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,r=n.label,i=n.labelLine,o=n.dataKey,a=n.valueKey,s=Sa(this.props,!1),c=Sa(r,!1),l=Sa(i,!1),u=r&&r.offsetRadius||20,f=e.map((function(e,n){var f=(e.startAngle+e.endAngle)/2,d=nO(e.cx,e.cy,e.outerRadius+u,f),p=Pk(Pk(Pk(Pk({},s),e),{},{stroke:"none"},c),{},{index:n,textAnchor:t.getTextAnchor(d.x,e.cx)},d),h=Pk(Pk(Pk(Pk({},s),e),{},{fill:"none",stroke:e.fill},l),{},{index:n,points:[nO(e.cx,e.cy,e.outerRadius,f),d]}),y=o;return vo(o)&&vo(a)?y="value":vo(o)&&(y=a),v.createElement(Ia,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(n)},i&&t.renderLabelLineItem(i,h,"line"),t.renderLabelItem(r,p,Oj(e,y)))}));return v.createElement(Ia,{className:"recharts-pie-labels"},f)}},{key:"renderSectorsStatically",value:function(e){var t=this,n=this.props,r=n.activeShape,i=n.blendStroke,o=n.inactiveShape;return e.map((function(n,a){if(0===(null==n?void 0:n.startAngle)&&0===(null==n?void 0:n.endAngle)&&1!==e.length)return null;var s=t.isActiveIndex(a),c=o&&t.hasActiveIndex()?o:null,l=s?r:c,u=Pk(Pk({},n),{},{stroke:i?n.fill:n.stroke,tabIndex:-1});return v.createElement(Ia,Ok({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},ua(t.props,n,a),{key:"sector-".concat(null==n?void 0:n.startAngle,"-").concat(null==n?void 0:n.endAngle,"-").concat(n.midAngle,"-").concat(a)}),v.createElement(hk,Ok({option:l,isActive:s,shapeType:"sector"},u)))}))}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,n=t.sectors,r=t.isAnimationActive,i=t.animationBegin,o=t.animationDuration,a=t.animationEasing,s=t.animationId,c=this.state,l=c.prevSectors,u=c.prevIsAnimationActive;return v.createElement(wP,{begin:i,duration:o,isActive:r,easing:a,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},(function(t){var r=t.t,i=[],o=(n&&n[0]).startAngle;return n.forEach((function(e,t){var n=l&&l[t],a=t>0?mo(e,"paddingAngle",0):0;if(n){var s=ta(n.endAngle-n.startAngle,e.endAngle-e.startAngle),c=Pk(Pk({},e),{},{startAngle:o+a,endAngle:o+s(r)+a});i.push(c),o=c.endAngle}else{var u=e.endAngle,f=e.startAngle,d=ta(0,u-f)(r),p=Pk(Pk({},e),{},{startAngle:o+a,endAngle:o+d+a});i.push(p),o=p.endAngle}})),v.createElement(Ia,null,e.renderSectorsStatically(i))}))}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var n=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"ArrowRight":var r=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,n=e.isAnimationActive,r=this.state.prevSectors;return!(n&&t&&t.length)||r&&Xx(r,t)?this.renderSectorsStatically(t):this.renderSectorsWithAnimation()}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,n=t.hide,r=t.sectors,i=t.className,o=t.label,a=t.cx,s=t.cy,c=t.innerRadius,l=t.outerRadius,u=t.isAnimationActive,f=this.state.isAnimationFinished;if(n||!r||!r.length||!Go(a)||!Go(s)||!Go(c)||!Go(l))return null;var d=m("recharts-pie",i);return v.createElement(Ia,{tabIndex:this.props.rootTabIndex,className:d,ref:function(t){e.pieRef=t}},this.renderSectors(),o&&this.renderLabels(r),gO.renderCallByParent(this.props,null,!1),(!u||f)&&CO.renderCallByParent(this.props,r,!1))}}],i=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,n){if(v.isValidElement(e))return v.cloneElement(e,t);if(jr(e))return e(t);var r=m("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return v.createElement(JO,Ok({},t,{key:n,type:"linear",className:r}))}},{key:"renderLabelItem",value:function(e,t,n){if(v.isValidElement(e))return v.cloneElement(e,t);var r=n;if(jr(e)&&(r=e(t),v.isValidElement(r)))return r;var i=m("recharts-pie-label-text","boolean"==typeof e||jr(e)?"":e.className);return v.createElement(Wy,Ok({},t,{alignmentBaseline:"middle",className:i}),r)}}],r&&Ak(n.prototype,r),i&&Ak(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();nk=Ck,Mk(Ck,"displayName","Pie"),Mk(Ck,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!vh.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),Mk(Ck,"parseDeltaAngle",(function(e,t){return Yo(t-e)*Math.min(Math.abs(t-e),360)})),Mk(Ck,"getRealPieData",(function(e){var t=e.data,n=e.children,r=Sa(e,!1),i=xa(n,ay);return t&&t.length?t.map((function(e,t){return Pk(Pk(Pk({payload:e},r),e),i&&i[t]&&i[t].props)})):i&&i.length?i.map((function(e){return Pk(Pk({},r),e.props)})):[]})),Mk(Ck,"parseCoordinateOfPie",(function(e,t){var n=t.top,r=t.left,i=t.width,o=t.height,a=rO(i,o);return{cx:r+Jo(e.cx,i,i/2),cy:n+Jo(e.cy,o,o/2),innerRadius:Jo(e.innerRadius,a,0),outerRadius:Jo(e.outerRadius,a,.8*a),maxRadius:e.maxRadius||Math.sqrt(i*i+o*o)/2}})),Mk(Ck,"getComposedData",(function(e){var t=e.item,n=e.offset,r=void 0!==t.type.defaultProps?Pk(Pk({},t.type.defaultProps),t.props):t.props,i=nk.getRealPieData(r);if(!i||!i.length)return null;var o=r.cornerRadius,a=r.startAngle,s=r.endAngle,c=r.paddingAngle,l=r.dataKey,u=r.nameKey,f=r.valueKey,d=r.tooltipType,p=Math.abs(r.minAngle),h=nk.parseCoordinateOfPie(r,n),y=nk.parseDeltaAngle(a,s),m=Math.abs(y),v=l;vo(l)&&vo(f)?(Ra(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),v="value"):vo(l)&&(Ra(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),v=f);var g,b,x=i.filter((function(e){return 0!==Oj(e,v,0)})).length,w=m-x*p-(m>=360?x:x-1)*c,j=i.reduce((function(e,t){var n=Oj(t,v,0);return e+(Go(n)?n:0)}),0);j>0&&(g=i.map((function(e,t){var n,r=Oj(e,v,0),i=Oj(e,u,t),s=(Go(r)?r:0)/j,l=(n=t?b.endAngle+Yo(y)*c*(0!==r?1:0):a)+Yo(y)*((0!==r?p:0)+s*w),f=(n+l)/2,m=(h.innerRadius+h.outerRadius)/2,g=[{name:i,value:r,payload:e,dataKey:v,type:d}],x=nO(h.cx,h.cy,m,f);return b=Pk(Pk(Pk({percent:s,cornerRadius:o,name:i,tooltipPayload:g,midAngle:f,middleRadius:m,tooltipPosition:x},e),h),{},{value:Oj(e,v),startAngle:n,endAngle:l,payload:e,paddingAngle:Yo(y)*c})})));return Pk(Pk({},h),{},{sectors:g,data:i})}));var Dk=Math.ceil,Ik=Math.max;var Rk=Wh,Bk=1/0;var Lk=function(e){return e?(e=Rk(e))===Bk||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0},zk=function(e,t,n,r){for(var i=-1,o=Ik(Dk((t-e)/(n||1)),0),a=Array(o);o--;)a[r?o:++i]=e,e+=n;return a},Fk=Fp,$k=Lk;const Uk=y(function(e){return function(t,n,r){return r&&"number"!=typeof r&&Fk(t,n,r)&&(n=r=void 0),t=$k(t),void 0===n?(n=t,t=0):n=$k(n),r=void 0===r?t<n?1:-1:$k(r),zk(t,n,r,e)}}());function Wk(e){return Wk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wk(e)}function qk(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Hk(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qk(Object(n),!0).forEach((function(t){Vk(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qk(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vk(e,t,n){return t=function(e){var t=function(e,t){if("object"!=Wk(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Wk(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Wk(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Yk=["Webkit","Moz","O","ms"];function Xk(e){return Xk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xk(e)}function Gk(){return Gk=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gk.apply(this,arguments)}function Kk(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Zk(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Kk(Object(n),!0).forEach((function(t){r_(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kk(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Qk(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,i_(r.key),r)}}function Jk(e,t,n){return t=t_(t),function(e,t){if(t&&("object"===Xk(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,e_()?Reflect.construct(t,n||[],t_(e).constructor):t.apply(e,n))}function e_(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(e_=function(){return!!e})()}function t_(e){return t_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},t_(e)}function n_(e,t){return n_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},n_(e,t)}function r_(e,t,n){return(t=i_(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i_(e){var t=function(e,t){if("object"!=Xk(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Xk(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Xk(t)?t:t+""}var o_=function(e){return e.changedTouches&&!!e.changedTouches.length},a_=function(){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r_(n=Jk(this,t,[e]),"handleDrag",(function(e){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(e):n.state.isSlideMoving&&n.handleSlideDrag(e)})),r_(n,"handleTouchMove",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&n.handleDrag(e.changedTouches[0])})),r_(n,"handleDragEnd",(function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},(function(){var e=n.props,t=e.endIndex,r=e.onDragEnd,i=e.startIndex;null==r||r({endIndex:t,startIndex:i})})),n.detachDragEndListener()})),r_(n,"handleLeaveWrapper",(function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))})),r_(n,"handleEnterSlideOrTraveller",(function(){n.setState({isTextActive:!0})})),r_(n,"handleLeaveSlideOrTraveller",(function(){n.setState({isTextActive:!1})})),r_(n,"handleSlideDragStart",(function(e){var t=o_(e)?e.changedTouches[0]:e;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:t.pageX}),n.attachDragEndListener()})),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n_(e,t)}(t,e.PureComponent),n=t,r=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(e){var n=e.startX,r=e.endX,i=this.state.scaleValues,o=this.props,a=o.gap,s=o.data.length-1,c=Math.min(n,r),l=Math.max(n,r),u=t.getIndexInRange(i,c),f=t.getIndexInRange(i,l);return{startIndex:u-u%a,endIndex:f===s?s:f-f%a}}},{key:"getTextOfTick",value:function(e){var t=this.props,n=t.data,r=t.tickFormatter,i=t.dataKey,o=Oj(n[e],i,e);return jr(r)?r(o,e):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(e){var t=this.state,n=t.slideMoveStartX,r=t.startX,i=t.endX,o=this.props,a=o.x,s=o.width,c=o.travellerWidth,l=o.startIndex,u=o.endIndex,f=o.onChange,d=e.pageX-n;d>0?d=Math.min(d,a+s-c-i,a+s-c-r):d<0&&(d=Math.max(d,a-r,a-i));var p=this.getIndex({startX:r+d,endX:i+d});p.startIndex===l&&p.endIndex===u||!f||f(p),this.setState({startX:r+d,endX:i+d,slideMoveStartX:e.pageX})}},{key:"handleTravellerDragStart",value:function(e,t){var n=o_(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:n.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(e){var t=this.state,n=t.brushMoveStartX,r=t.movingTravellerId,i=t.endX,o=t.startX,a=this.state[r],s=this.props,c=s.x,l=s.width,u=s.travellerWidth,f=s.onChange,d=s.gap,p=s.data,h={startX:this.state.startX,endX:this.state.endX},y=e.pageX-n;y>0?y=Math.min(y,c+l-u-a):y<0&&(y=Math.max(y,c-a)),h[r]=a+y;var m=this.getIndex(h),v=m.startIndex,g=m.endIndex;this.setState(r_(r_({},r,a+y),"brushMoveStartX",e.pageX),(function(){var e;f&&(e=p.length-1,("startX"===r&&(i>o?v%d==0:g%d==0)||i<o&&g===e||"endX"===r&&(i>o?g%d==0:v%d==0)||i>o&&g===e)&&f(m))}))}},{key:"handleTravellerMoveKeyboard",value:function(e,t){var n=this,r=this.state,i=r.scaleValues,o=r.startX,a=r.endX,s=this.state[t],c=i.indexOf(s);if(-1!==c){var l=c+e;if(!(-1===l||l>=i.length)){var u=i[l];"startX"===t&&u>=a||"endX"===t&&u<=o||this.setState(r_({},t,u),(function(){n.props.onChange(n.getIndex({startX:n.state.startX,endX:n.state.endX}))}))}}}},{key:"renderBackground",value:function(){var e=this.props,t=e.x,n=e.y,r=e.width,i=e.height,o=e.fill,a=e.stroke;return v.createElement("rect",{stroke:a,fill:o,x:t,y:n,width:r,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,n=t.x,r=t.y,i=t.width,o=t.height,a=t.data,s=t.children,c=t.padding,l=e.Children.only(s);return l?v.cloneElement(l,{x:n,y:r,width:i,height:o,margin:c,compact:!0,data:a}):null}},{key:"renderTravellerLayer",value:function(e,n){var r,i,o=this,a=this.props,s=a.y,c=a.travellerWidth,l=a.height,u=a.traveller,f=a.ariaLabel,d=a.data,p=a.startIndex,h=a.endIndex,y=Math.max(e,this.props.x),m=Zk(Zk({},Sa(this.props,!1)),{},{x:y,y:s,width:c,height:l}),g=f||"Min value: ".concat(null===(r=d[p])||void 0===r?void 0:r.name,", Max value: ").concat(null===(i=d[h])||void 0===i?void 0:i.name);return v.createElement(Ia,{tabIndex:0,role:"slider","aria-label":g,"aria-valuenow":e,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[n],onTouchStart:this.travellerDragStartHandlers[n],onKeyDown:function(e){["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),o.handleTravellerMoveKeyboard("ArrowRight"===e.key?1:-1,n))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},t.renderTraveller(u,m))}},{key:"renderSlide",value:function(e,t){var n=this.props,r=n.y,i=n.height,o=n.stroke,a=n.travellerWidth,s=Math.min(e,t)+a,c=Math.max(Math.abs(t-e)-a,0);return v.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:o,fillOpacity:.2,x:s,y:r,width:c,height:i})}},{key:"renderText",value:function(){var e=this.props,t=e.startIndex,n=e.endIndex,r=e.y,i=e.height,o=e.travellerWidth,a=e.stroke,s=this.state,c=s.startX,l=s.endX,u={pointerEvents:"none",fill:a};return v.createElement(Ia,{className:"recharts-brush-texts"},v.createElement(Wy,Gk({textAnchor:"end",verticalAnchor:"middle",x:Math.min(c,l)-5,y:r+i/2},u),this.getTextOfTick(t)),v.createElement(Wy,Gk({textAnchor:"start",verticalAnchor:"middle",x:Math.max(c,l)+o+5,y:r+i/2},u),this.getTextOfTick(n)))}},{key:"render",value:function(){var e=this.props,t=e.data,n=e.className,r=e.children,i=e.x,o=e.y,a=e.width,s=e.height,c=e.alwaysShowText,l=this.state,u=l.startX,f=l.endX,d=l.isTextActive,p=l.isSlideMoving,h=l.isTravellerMoving,y=l.isTravellerFocused;if(!t||!t.length||!Go(i)||!Go(o)||!Go(a)||!Go(s)||a<=0||s<=0)return null;var g,b,x,w,j=m("recharts-brush",n),O=1===v.Children.count(r),S=(b="none",x=(g="userSelect").replace(/(\w)/,(function(e){return e.toUpperCase()})),(w=Yk.reduce((function(e,t){return Hk(Hk({},e),{},Vk({},t+x,b))}),{}))[g]=b,w);return v.createElement(Ia,{className:j,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:S},this.renderBackground(),O&&this.renderPanorama(),this.renderSlide(u,f),this.renderTravellerLayer(u,"startX"),this.renderTravellerLayer(f,"endX"),(d||p||h||y||c)&&this.renderText())}}],i=[{key:"renderDefaultTraveller",value:function(e){var t=e.x,n=e.y,r=e.width,i=e.height,o=e.stroke,a=Math.floor(n+i/2)-1;return v.createElement(v.Fragment,null,v.createElement("rect",{x:t,y:n,width:r,height:i,fill:o,stroke:"none"}),v.createElement("line",{x1:t+1,y1:a,x2:t+r-1,y2:a,fill:"none",stroke:"#fff"}),v.createElement("line",{x1:t+1,y1:a+2,x2:t+r-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(e,n){return v.isValidElement(e)?v.cloneElement(e,n):jr(e)?e(n):t.renderDefaultTraveller(n)}},{key:"getDerivedStateFromProps",value:function(e,t){var n=e.data,r=e.width,i=e.x,o=e.travellerWidth,a=e.updateId,s=e.startIndex,c=e.endIndex;if(n!==t.prevData||a!==t.prevUpdateId)return Zk({prevData:n,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:r},n&&n.length?function(e){var t=e.data,n=e.startIndex,r=e.endIndex,i=e.x,o=e.width,a=e.travellerWidth;if(!t||!t.length)return{};var s=t.length,c=gm().domain(Uk(0,s)).range([i,i+o-a]),l=c.domain().map((function(e){return c(e)}));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(n),endX:c(r),scale:c,scaleValues:l}}({data:n,width:r,x:i,travellerWidth:o,startIndex:s,endIndex:c}):{scale:null,scaleValues:null});if(t.scale&&(r!==t.prevWidth||i!==t.prevX||o!==t.prevTravellerWidth)){t.scale.range([i,i+r-o]);var l=t.scale.domain().map((function(e){return t.scale(e)}));return{prevData:n,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:r,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(e,t){for(var n=0,r=e.length-1;r-n>1;){var i=Math.floor((n+r)/2);e[i]>t?r=i:n=i}return t>=e[r]?r:n}}],r&&Qk(n.prototype,r),i&&Qk(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();r_(a_,"displayName","Brush"),r_(a_,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var s_=np;var c_=tl,l_=ad,u_=function(e,t){var n;return s_(e,(function(e,r,i){return!(n=t(e,r,i))})),!!n},f_=Hn,d_=Fp;const p_=y((function(e,t,n){var r=f_(e)?c_:u_;return n&&d_(e,t,n)&&(t=void 0),r(e,l_(t))}));var h_=function(e,t){var n=e.alwaysShow,r=e.ifOverflow;return n&&(r="extendDomain"),r===t},y_=Ap;var m_=function(e,t,n){"__proto__"==t&&y_?y_(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},v_=m_,g_=ep,b_=ad;var x_=function(e,t){var n={};return t=b_(t),g_(e,(function(e,r,i){v_(n,r,t(e,r,i))})),n};const w_=y(x_);var j_=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(!t(e[n],n,e))return!1;return!0},O_=np;var S_=j_,P_=function(e,t){var n=!0;return O_(e,(function(e,r,i){return n=!!t(e,r,i)})),n},A_=ad,k_=Hn,__=Fp;const E_=y((function(e,t,n){var r=k_(e)?S_:P_;return n&&__(e,t,n)&&(t=void 0),r(e,A_(t))}));var N_=["x","y"];function M_(e){return(M_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function T_(){return T_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},T_.apply(this,arguments)}function C_(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function D_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?C_(Object(n),!0).forEach((function(t){I_(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):C_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function I_(e,t,n){return t=function(e){var t=function(e,t){if("object"!=M_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=M_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==M_(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function R_(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function B_(e,t){var n=e.x,r=e.y,i=R_(e,N_),o="".concat(n),a=parseInt(o,10),s="".concat(r),c=parseInt(s,10),l="".concat(t.height||i.height),u=parseInt(l,10),f="".concat(t.width||i.width),d=parseInt(f,10);return D_(D_(D_(D_(D_({},t),i),a?{x:a}:{}),c?{y:c}:{}),{},{height:u,width:d,name:t.name,radius:t.radius})}function L_(e){return v.createElement(hk,T_({shapeType:"rectangle",propTransformer:B_,activeClassName:"recharts-active-bar"},e))}var z_,F_=["value","background"];function $_(e){return $_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$_(e)}function U_(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function W_(){return W_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},W_.apply(this,arguments)}function q_(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function H_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?q_(Object(n),!0).forEach((function(t){Z_(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):q_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function V_(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Q_(r.key),r)}}function Y_(e,t,n){return t=G_(t),function(e,t){if(t&&("object"===$_(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,X_()?Reflect.construct(t,n||[],G_(e).constructor):t.apply(e,n))}function X_(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(X_=function(){return!!e})()}function G_(e){return G_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},G_(e)}function K_(e,t){return K_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},K_(e,t)}function Z_(e,t,n){return(t=Q_(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Q_(e){var t=function(e,t){if("object"!=$_(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=$_(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==$_(t)?t:t+""}var J_=function(){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return Z_(e=Y_(this,t,[].concat(r)),"state",{isAnimationFinished:!1}),Z_(e,"id",Qo("recharts-bar-")),Z_(e,"handleAnimationEnd",(function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),t&&t()})),Z_(e,"handleAnimationStart",(function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),t&&t()})),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&K_(e,t)}(t,e.PureComponent),n=t,r=[{key:"renderRectanglesStatically",value:function(e){var t=this,n=this.props,r=n.shape,i=n.dataKey,o=n.activeIndex,a=n.activeBar,s=Sa(this.props,!1);return e&&e.map((function(e,n){var c=n===o,l=c?a:r,u=H_(H_(H_({},s),e),{},{isActive:c,option:l,index:n,dataKey:i,onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd});return v.createElement(Ia,W_({className:"recharts-bar-rectangle"},ua(t.props,e,n),{key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value)}),v.createElement(L_,u))}))}},{key:"renderRectanglesWithAnimation",value:function(){var e=this,t=this.props,n=t.data,r=t.layout,i=t.isAnimationActive,o=t.animationBegin,a=t.animationDuration,s=t.animationEasing,c=t.animationId,l=this.state.prevData;return v.createElement(wP,{begin:o,duration:a,isActive:i,easing:s,from:{t:0},to:{t:1},key:"bar-".concat(c),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(t){var i=t.t,o=n.map((function(e,t){var n=l&&l[t];if(n){var o=ta(n.x,e.x),a=ta(n.y,e.y),s=ta(n.width,e.width),c=ta(n.height,e.height);return H_(H_({},e),{},{x:o(i),y:a(i),width:s(i),height:c(i)})}if("horizontal"===r){var u=ta(0,e.height)(i);return H_(H_({},e),{},{y:e.y+e.height-u,height:u})}var f=ta(0,e.width)(i);return H_(H_({},e),{},{width:f})}));return v.createElement(Ia,null,e.renderRectanglesStatically(o))}))}},{key:"renderRectangles",value:function(){var e=this.props,t=e.data,n=e.isAnimationActive,r=this.state.prevData;return!(n&&t&&t.length)||r&&Xx(r,t)?this.renderRectanglesStatically(t):this.renderRectanglesWithAnimation()}},{key:"renderBackground",value:function(){var e=this,t=this.props,n=t.data,r=t.dataKey,i=t.activeIndex,o=Sa(this.props.background,!1);return n.map((function(t,n){t.value;var a=t.background,s=U_(t,F_);if(!a)return null;var c=H_(H_(H_(H_(H_({},s),{},{fill:"#eee"},a),o),ua(e.props,t,n)),{},{onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd,dataKey:r,index:n,className:"recharts-bar-background-rectangle"});return v.createElement(L_,W_({key:"background-bar-".concat(n),option:e.props.background,isActive:n===i},c))}))}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,r=n.data,i=n.xAxis,o=n.yAxis,a=n.layout,s=xa(n.children,fj);if(!s)return null;var c="vertical"===a?r[0].height/2:r[0].width/2,l=function(e,t){var n=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:n,errorVal:Oj(e,t)}},u={clipPath:e?"url(#clipPath-".concat(t,")"):null};return v.createElement(Ia,u,s.map((function(e){return v.cloneElement(e,{key:"error-bar-".concat(t,"-").concat(e.props.dataKey),data:r,xAxis:i,yAxis:o,layout:a,offset:c,dataPointFormatter:l})})))}},{key:"render",value:function(){var e=this.props,t=e.hide,n=e.data,r=e.className,i=e.xAxis,o=e.yAxis,a=e.left,s=e.top,c=e.width,l=e.height,u=e.isAnimationActive,f=e.background,d=e.id;if(t||!n||!n.length)return null;var p=this.state.isAnimationFinished,h=m("recharts-bar",r),y=i&&i.allowDataOverflow,g=o&&o.allowDataOverflow,b=y||g,x=vo(d)?this.id:d;return v.createElement(Ia,{className:h},y||g?v.createElement("defs",null,v.createElement("clipPath",{id:"clipPath-".concat(x)},v.createElement("rect",{x:y?a:a-c/2,y:g?s:s-l/2,width:y?c:2*c,height:g?l:2*l}))):null,v.createElement(Ia,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(x,")"):null},f?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,x),(!u||p)&&CO.renderCallByParent(this.props,n))}}],i=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curData:e.data,prevData:t.curData}:e.data!==t.curData?{curData:e.data}:null}}],r&&V_(n.prototype,r),i&&V_(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();function eE(e){return eE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},eE(e)}function tE(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,oE(r.key),r)}}function nE(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function rE(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?nE(Object(n),!0).forEach((function(t){iE(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):nE(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function iE(e,t,n){return(t=oE(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oE(e){var t=function(e,t){if("object"!=eE(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=eE(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eE(t)?t:t+""}z_=J_,Z_(J_,"displayName","Bar"),Z_(J_,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!vh.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),Z_(J_,"getComposedData",(function(e){var t=e.props,n=e.item,r=e.barPosition,i=e.bandSize,o=e.xAxis,a=e.yAxis,s=e.xAxisTicks,c=e.yAxisTicks,l=e.stackedData,u=e.dataStartIndex,f=e.displayedData,d=e.offset,p=function(e,t){if(!e)return null;for(var n=0,r=e.length;n<r;n++)if(e[n].item===t)return e[n].position;return null}(r,n);if(!p)return null;var h=t.layout,y=n.type.defaultProps,m=void 0!==y?H_(H_({},y),n.props):n.props,v=m.dataKey,g=m.children,b=m.minPointSize,x="horizontal"===h?a:o,w=l?x.scale.domain():null,j=function(e){var t=e.numericAxis,n=t.scale.domain();if("number"===t.type){var r=Math.min(n[0],n[1]),i=Math.max(n[0],n[1]);return r<=0&&i>=0?0:i<0?i:r}return n[0]}({numericAxis:x}),O=xa(g,ay),S=f.map((function(e,t){var r,f,d,y,m,g;l?r=function(e,t){if(!t||2!==t.length||!Go(t[0])||!Go(t[1]))return e;var n=Math.min(t[0],t[1]),r=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!Go(e[0])||e[0]<n)&&(i[0]=n),(!Go(e[1])||e[1]>r)&&(i[1]=r),i[0]>r&&(i[0]=r),i[1]<n&&(i[1]=n),i}(l[u+t],w):(r=Oj(e,v),Array.isArray(r)||(r=[j,r]));var x=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(n,r){if("number"==typeof e)return e;var i="number"==typeof n;return i?e(n,r):(i||Zw(),t)}}(b,z_.defaultProps.minPointSize)(r[1],t);if("horizontal"===h){var S,P=[a.scale(r[0]),a.scale(r[1])],A=P[0],k=P[1];f=Fj({axis:o,ticks:s,bandSize:i,offset:p.offset,entry:e,index:t}),d=null!==(S=null!=k?k:A)&&void 0!==S?S:void 0,y=p.size;var _=A-k;if(m=Number.isNaN(_)?0:_,g={x:f,y:a.y,width:y,height:a.height},Math.abs(x)>0&&Math.abs(m)<Math.abs(x)){var E=Yo(m||x)*(Math.abs(x)-Math.abs(m));d-=E,m+=E}}else{var N=[o.scale(r[0]),o.scale(r[1])],M=N[0],T=N[1];if(f=M,d=Fj({axis:a,ticks:c,bandSize:i,offset:p.offset,entry:e,index:t}),y=T-M,m=p.size,g={x:o.x,y:d,width:o.width,height:m},Math.abs(x)>0&&Math.abs(y)<Math.abs(x))y+=Yo(y||x)*(Math.abs(x)-Math.abs(y))}return H_(H_(H_({},e),{},{x:f,y:d,width:y,height:m,value:l?r:r[1],payload:e,background:g},O&&O[t]&&O[t].props),{},{tooltipPayload:[Yj(n,e)],tooltipPosition:{x:f+y/2,y:d+m/2}})}));return H_({data:S,layout:h},d)}));var aE=function(e,t,n,r,i){var o=e.width,a=e.height,s=e.layout,c=e.children,l=Object.keys(t),u={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:a-n.bottom,bottomMirror:a-n.bottom},f=!!wa(c,J_);return l.reduce((function(o,a){var c,l,d,p,h,y=t[a],m=y.orientation,v=y.domain,g=y.padding,b=void 0===g?{}:g,x=y.mirror,w=y.reversed,j="".concat(m).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var O=v[1]-v[0],S=1/0,P=y.categoricalDomain.sort();if(P.forEach((function(e,t){t>0&&(S=Math.min((e||0)-(P[t-1]||0),S))})),Number.isFinite(S)){var A=S/O,k="vertical"===y.layout?n.height:n.width;if("gap"===y.padding&&(c=A*k/2),"no-gap"===y.padding){var _=Jo(e.barCategoryGap,A*k),E=A*k/2;c=E-_-(E-_)/k*_}}}l="xAxis"===r?[n.left+(b.left||0)+(c||0),n.left+n.width-(b.right||0)-(c||0)]:"yAxis"===r?"horizontal"===s?[n.top+n.height-(b.bottom||0),n.top+(b.top||0)]:[n.top+(b.top||0)+(c||0),n.top+n.height-(b.bottom||0)-(c||0)]:y.range,w&&(l=[l[1],l[0]]);var N=Cj(y,i,f),M=N.scale,T=N.realScaleType;M.domain(v).range(l),Ij(M);var C=Lj(M,rE(rE({},y),{},{realScaleType:T}));"xAxis"===r?(h="top"===m&&!x||"bottom"===m&&x,d=n.left,p=u[j]-h*y.height):"yAxis"===r&&(h="left"===m&&!x||"right"===m&&x,d=u[j]-h*y.width,p=n.top);var D=rE(rE(rE({},y),C),{},{realScaleType:T,x:d,y:p,scale:M,width:"xAxis"===r?n.width:y.width,height:"yAxis"===r?n.height:y.height});return D.bandSize=Hj(D,C),y.hide||"xAxis"!==r?y.hide||(u[j]+=(h?-1:1)*D.width):u[j]+=(h?-1:1)*D.height,rE(rE({},o),{},iE({},a,D))}),{})},sE=function(e,t){var n=e.x,r=e.y,i=t.x,o=t.y;return{x:Math.min(n,i),y:Math.min(r,o),width:Math.abs(i-n),height:Math.abs(o-r)}},cE=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.scale=t}return t=e,n=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.bandAware,r=t.position;if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(e)+o}if(n){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}},{key:"isInRange",value:function(e){var t=this.range(),n=t[0],r=t[t.length-1];return n<=r?e>=n&&e<=r:e>=r&&e<=n}}],r=[{key:"create",value:function(t){return new e(t)}}],n&&tE(t.prototype,n),r&&tE(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();iE(cE,"EPS",1e-4);var lE=function(e){var t=Object.keys(e).reduce((function(t,n){return rE(rE({},t),{},iE({},n,cE.create(e[n])))}),{});return rE(rE({},t),{},{apply:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.bandAware,i=n.position;return w_(e,(function(e,n){return t[n].apply(e,{bandAware:r,position:i})}))},isInRange:function(e){return E_(e,(function(e,n){return t[n].isInRange(e)}))}})};var uE=ad,fE=bu,dE=Ou;var pE=function(e){return function(t,n,r){var i=Object(t);if(!fE(t)){var o=uE(n);t=dE(t),n=function(e){return o(i[e],e,i)}}var a=e(t,n,r);return a>-1?i[o?t[a]:a]:void 0}},hE=Lk;var yE=sd,mE=ad,vE=function(e){var t=hE(e),n=t%1;return t==t?n?t-n:t:0},gE=Math.max;var bE=function(e,t,n){var r=null==e?0:e.length;if(!r)return-1;var i=null==n?0:vE(n);return i<0&&(i=gE(r+i,0)),yE(e,mE(t),i)};const xE=y(pE(bE));var wE=Ui((function(e){return{x:e.left,y:e.top,width:e.width,height:e.height}}),(function(e){return["l",e.left,"t",e.top,"w",e.width,"h",e.height].join("")})),jE=e.createContext(void 0),OE=e.createContext(void 0),SE=e.createContext(void 0),PE=e.createContext({}),AE=e.createContext(void 0),kE=e.createContext(0),_E=e.createContext(0),EE=function(e){var t=e.state,n=t.xAxisMap,r=t.yAxisMap,i=t.offset,o=e.clipPathId,a=e.children,s=e.width,c=e.height,l=wE(i);return v.createElement(jE.Provider,{value:n},v.createElement(OE.Provider,{value:r},v.createElement(PE.Provider,{value:i},v.createElement(SE.Provider,{value:l},v.createElement(AE.Provider,{value:o},v.createElement(kE.Provider,{value:c},v.createElement(_E.Provider,{value:s},a)))))))},NE=function(t){var n=e.useContext(jE);null==n&&Zw();var r=n[t];return null==r&&Zw(),r},ME=function(t){var n=e.useContext(OE);null==n&&Zw();var r=n[t];return null==r&&Zw(),r},TE=function(){return e.useContext(_E)},CE=function(){return e.useContext(kE)};function DE(e){return DE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},DE(e)}function IE(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,WE(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function RE(e,t,n){return t=LE(t),function(e,t){if(t&&("object"===DE(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,BE()?Reflect.construct(t,n||[],LE(e).constructor):t.apply(e,n))}function BE(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(BE=function(){return!!e})()}function LE(e){return LE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},LE(e)}function zE(e,t){return zE=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zE(e,t)}function FE(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $E(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?FE(Object(n),!0).forEach((function(t){UE(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):FE(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function UE(e,t,n){return(t=WE(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function WE(e){var t=function(e,t){if("object"!=DE(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=DE(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==DE(t)?t:t+""}function qE(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return HE(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return HE(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function HE(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function VE(){return VE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},VE.apply(this,arguments)}function YE(t){var n=t.x,r=t.y,i=t.segment,o=t.xAxisId,a=t.yAxisId,s=t.shape,c=t.className,l=t.alwaysShow,u=e.useContext(AE),f=NE(o),d=ME(a),p=e.useContext(SE);if(!u||!p)return null;Ra(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=function(e,t,n,r,i,o,a,s,c){var l=i.x,u=i.y,f=i.width,d=i.height;if(n){var p=c.y,h=e.y.apply(p,{position:o});if(h_(c,"discard")&&!e.y.isInRange(h))return null;var y=[{x:l+f,y:h},{x:l,y:h}];return"left"===s?y.reverse():y}if(t){var m=c.x,v=e.x.apply(m,{position:o});if(h_(c,"discard")&&!e.x.isInRange(v))return null;var g=[{x:v,y:u+d},{x:v,y:u}];return"top"===a?g.reverse():g}if(r){var b=c.segment.map((function(t){return e.apply(t,{position:o})}));return h_(c,"discard")&&p_(b,(function(t){return!e.isInRange(t)}))?null:b}return null}(lE({x:f.scale,y:d.scale}),Ko(n),Ko(r),i&&2===i.length,p,t.position,f.orientation,d.orientation,t);if(!h)return null;var y=qE(h,2),g=y[0],b=g.x,x=g.y,w=y[1],j=w.x,O=w.y,S=$E($E({clipPath:h_(t,"hidden")?"url(#".concat(u,")"):void 0},Sa(t,!0)),{},{x1:b,y1:x,x2:j,y2:O});return v.createElement(Ia,{className:m("recharts-reference-line",c)},function(e,t){return v.isValidElement(e)?v.cloneElement(e,t):jr(e)?e(t):v.createElement("line",VE({},t,{className:"recharts-reference-line-line"}))}(s,S),gO.renderCallByParent(t,function(e){var t=e.x1,n=e.y1,r=e.x2,i=e.y2;return sE({x:t,y:n},{x:r,y:i})}({x1:b,y1:x,x2:j,y2:O})))}var XE=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),RE(this,e,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zE(e,t)}(e,v.Component),IE(e,[{key:"render",value:function(){return v.createElement(YE,this.props)}}])}();function GE(){return GE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},GE.apply(this,arguments)}function KE(e){return KE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},KE(e)}function ZE(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function QE(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ZE(Object(n),!0).forEach((function(t){iN(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ZE(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function JE(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,oN(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function eN(e,t,n){return t=nN(t),function(e,t){if(t&&("object"===KE(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,tN()?Reflect.construct(t,n||[],nN(e).constructor):t.apply(e,n))}function tN(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(tN=function(){return!!e})()}function nN(e){return nN=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},nN(e)}function rN(e,t){return rN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},rN(e,t)}function iN(e,t,n){return(t=oN(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function oN(e){var t=function(e,t){if("object"!=KE(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=KE(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==KE(t)?t:t+""}UE(XE,"displayName","ReferenceLine"),UE(XE,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var aN=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),eN(this,e,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&rN(e,t)}(e,v.Component),JE(e,[{key:"render",value:function(){var t=this.props,n=t.x,r=t.y,i=t.r,o=t.alwaysShow,a=t.clipPathId,s=Ko(n),c=Ko(r);if(Ra(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!s||!c)return null;var l=function(e){var t=e.x,n=e.y,r=e.xAxis,i=e.yAxis,o=lE({x:r.scale,y:i.scale}),a=o.apply({x:t,y:n},{bandAware:!0});return h_(e,"discard")&&!o.isInRange(a)?null:a}(this.props);if(!l)return null;var u=l.x,f=l.y,d=this.props,p=d.shape,h=d.className,y=QE(QE({clipPath:h_(this.props,"hidden")?"url(#".concat(a,")"):void 0},Sa(this.props,!0)),{},{cx:u,cy:f});return v.createElement(Ia,{className:m("recharts-reference-dot",h)},e.renderDot(p,y),gO.renderCallByParent(this.props,{x:u-i,y:f-i,width:2*i,height:2*i}))}}])}();function sN(){return sN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},sN.apply(this,arguments)}function cN(e){return cN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cN(e)}function lN(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function uN(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?lN(Object(n),!0).forEach((function(t){mN(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):lN(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function fN(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,vN(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function dN(e,t,n){return t=hN(t),function(e,t){if(t&&("object"===cN(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,pN()?Reflect.construct(t,n||[],hN(e).constructor):t.apply(e,n))}function pN(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(pN=function(){return!!e})()}function hN(e){return hN=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},hN(e)}function yN(e,t){return yN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},yN(e,t)}function mN(e,t,n){return(t=vN(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vN(e){var t=function(e,t){if("object"!=cN(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=cN(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==cN(t)?t:t+""}iN(aN,"displayName","ReferenceDot"),iN(aN,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),iN(aN,"renderDot",(function(e,t){return v.isValidElement(e)?v.cloneElement(e,t):jr(e)?e(t):v.createElement(UP,GE({},t,{cx:t.cx,cy:t.cy,className:"recharts-reference-dot-dot"}))}));var gN=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),dN(this,e,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&yN(e,t)}(e,v.Component),fN(e,[{key:"render",value:function(){var t=this.props,n=t.x1,r=t.x2,i=t.y1,o=t.y2,a=t.className,s=t.alwaysShow,c=t.clipPathId;Ra(void 0===s,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=Ko(n),u=Ko(r),f=Ko(i),d=Ko(o),p=this.props.shape;if(!(l||u||f||d||p))return null;var h=function(e,t,n,r,i){var o=i.x1,a=i.x2,s=i.y1,c=i.y2,l=i.xAxis,u=i.yAxis;if(!l||!u)return null;var f=lE({x:l.scale,y:u.scale}),d={x:e?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:n?f.y.apply(s,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:r?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!h_(i,"discard")||f.isInRange(d)&&f.isInRange(p)?sE(d,p):null}(l,u,f,d,this.props);if(!h&&!p)return null;var y=h_(this.props,"hidden")?"url(#".concat(c,")"):void 0;return v.createElement(Ia,{className:m("recharts-reference-area",a)},e.renderRect(p,uN(uN({clipPath:y},Sa(this.props,!0)),h)),gO.renderCallByParent(this.props,h))}}])}();function bN(e,t,n){if(t<1)return[];if(1===t&&void 0===n)return e;for(var r=[],i=0;i<e.length;i+=t)r.push(e[i]);return r}function xN(e,t,n){return function(e){var t=e.width,n=e.height,r=function(e){return(e%180+180)%180}(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0),i=r*Math.PI/180,o=Math.atan(n/t),a=i>o&&i<Math.PI-o?n/Math.sin(i):t/Math.cos(i);return Math.abs(a)}({width:e.width+t.width,height:e.height+t.height},n)}function wN(e,t,n,r,i){if(e*t<e*r||e*t>e*i)return!1;var o=n();return e*(t-e*o/2-r)>=0&&e*(t+e*o/2-i)<=0}function jN(e){return jN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jN(e)}function ON(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function SN(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ON(Object(n),!0).forEach((function(t){PN(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ON(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function PN(e,t,n){return t=function(e){var t=function(e,t){if("object"!=jN(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=jN(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==jN(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function AN(e,t,n){var r=e.tick,i=e.ticks,o=e.viewBox,a=e.minTickGap,s=e.orientation,c=e.interval,l=e.tickFormatter,u=e.unit,f=e.angle;if(!i||!i.length||!r)return[];if(Go(c)||vh.isSsr)return function(e,t){return bN(e,t+1)}(i,"number"==typeof c&&Go(c)?c:0);var d=[],p="top"===s||"bottom"===s?"width":"height",h=u&&"width"===p?hy(u,{fontSize:t,letterSpacing:n}):{width:0,height:0},y=function(e,r){var i=jr(l)?l(e.value,r):e.value;return"width"===p?xN(hy(i,{fontSize:t,letterSpacing:n}),h,f):hy(i,{fontSize:t,letterSpacing:n})[p]},m=i.length>=2?Yo(i[1].coordinate-i[0].coordinate):1,v=function(e,t,n){var r="width"===n,i=e.x,o=e.y,a=e.width,s=e.height;return 1===t?{start:r?i:o,end:r?i+a:o+s}:{start:r?i+a:o+s,end:r?i:o}}(o,m,p);return"equidistantPreserveStart"===c?function(e,t,n,r,i){for(var o,a=(r||[]).slice(),s=t.start,c=t.end,l=0,u=1,f=s,d=function(){var t=null==r?void 0:r[l];if(void 0===t)return{v:bN(r,u)};var o,a=l,d=function(){return void 0===o&&(o=n(t,a)),o},p=t.coordinate,h=0===l||wN(e,p,d,f,c);h||(l=0,f=s,u+=1),h&&(f=p+e*(d()/2+i),l+=u)};u<=a.length;)if(o=d())return o.v;return[]}(m,v,y,i,a):(d="preserveStart"===c||"preserveStartEnd"===c?function(e,t,n,r,i,o){var a=(r||[]).slice(),s=a.length,c=t.start,l=t.end;if(o){var u=r[s-1],f=n(u,s-1),d=e*(u.coordinate+e*f/2-l);a[s-1]=u=SN(SN({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),wN(e,u.tickCoord,(function(){return f}),c,l)&&(l=u.tickCoord-e*(f/2+i),a[s-1]=SN(SN({},u),{},{isShow:!0}))}for(var p=o?s-1:s,h=function(t){var r,o=a[t],s=function(){return void 0===r&&(r=n(o,t)),r};if(0===t){var u=e*(o.coordinate-e*s()/2-c);a[t]=o=SN(SN({},o),{},{tickCoord:u<0?o.coordinate-u*e:o.coordinate})}else a[t]=o=SN(SN({},o),{},{tickCoord:o.coordinate});wN(e,o.tickCoord,s,c,l)&&(c=o.tickCoord+e*(s()/2+i),a[t]=SN(SN({},o),{},{isShow:!0}))},y=0;y<p;y++)h(y);return a}(m,v,y,i,a,"preserveStartEnd"===c):function(e,t,n,r,i){for(var o=(r||[]).slice(),a=o.length,s=t.start,c=t.end,l=function(t){var r,l=o[t],u=function(){return void 0===r&&(r=n(l,t)),r};if(t===a-1){var f=e*(l.coordinate+e*u()/2-c);o[t]=l=SN(SN({},l),{},{tickCoord:f>0?l.coordinate-f*e:l.coordinate})}else o[t]=l=SN(SN({},l),{},{tickCoord:l.coordinate});wN(e,l.tickCoord,u,s,c)&&(c=l.tickCoord-e*(u()/2+i),o[t]=SN(SN({},l),{},{isShow:!0}))},u=a-1;u>=0;u--)l(u);return o}(m,v,y,i,a),d.filter((function(e){return e.isShow})))}mN(gN,"displayName","ReferenceArea"),mN(gN,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),mN(gN,"renderRect",(function(e,t){return v.isValidElement(e)?v.cloneElement(e,t):jr(e)?e(t):v.createElement(TP,sN({},t,{className:"recharts-reference-area-rect"}))}));var kN=["viewBox"],_N=["viewBox"],EN=["ticks"];function NN(e){return NN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},NN(e)}function MN(){return MN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},MN.apply(this,arguments)}function TN(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function CN(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?TN(Object(n),!0).forEach((function(t){FN(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):TN(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function DN(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function IN(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,$N(r.key),r)}}function RN(e,t,n){return t=LN(t),function(e,t){if(t&&("object"===NN(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,BN()?Reflect.construct(t,n||[],LN(e).constructor):t.apply(e,n))}function BN(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(BN=function(){return!!e})()}function LN(e){return LN=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},LN(e)}function zN(e,t){return zN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},zN(e,t)}function FN(e,t,n){return(t=$N(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function $N(e){var t=function(e,t){if("object"!=NN(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=NN(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==NN(t)?t:t+""}var UN=function(){function t(e){var n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(n=RN(this,t,[e])).state={fontSize:"",letterSpacing:""},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&zN(e,t)}(t,e.Component),n=t,r=[{key:"shouldComponentUpdate",value:function(e,t){var n=e.viewBox,r=DN(e,kN),i=this.props,o=i.viewBox,a=DN(i,_N);return!ra(n,o)||!ra(r,a)||!ra(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,n,r,i,o,a,s=this.props,c=s.x,l=s.y,u=s.width,f=s.height,d=s.orientation,p=s.tickSize,h=s.mirror,y=s.tickMargin,m=h?-1:1,v=e.tickSize||p,g=Go(e.tickCoord)?e.tickCoord:e.coordinate;switch(d){case"top":t=n=e.coordinate,a=(r=(i=l+ +!h*f)-m*v)-m*y,o=g;break;case"left":r=i=e.coordinate,o=(t=(n=c+ +!h*u)-m*v)-m*y,a=g;break;case"right":r=i=e.coordinate,o=(t=(n=c+ +h*u)+m*v)+m*y,a=g;break;default:t=n=e.coordinate,a=(r=(i=l+ +h*f)+m*v)+m*y,o=g}return{line:{x1:t,y1:r,x2:n,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,n=t.orientation,r=t.mirror;switch(n){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,n=e.mirror,r="end";switch(t){case"left":case"right":r="middle";break;case"top":r=n?"start":"end";break;default:r=n?"end":"start"}return r}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,n=e.y,r=e.width,i=e.height,o=e.orientation,a=e.mirror,s=e.axisLine,c=CN(CN(CN({},Sa(this.props,!1)),Sa(s,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var l=+("top"===o&&!a||"bottom"===o&&a);c=CN(CN({},c),{},{x1:t,y1:n+l*i,x2:t+r,y2:n+l*i})}else{var u=+("left"===o&&!a||"right"===o&&a);c=CN(CN({},c),{},{x1:t+u*r,y1:n,x2:t+u*r,y2:n+i})}return v.createElement("line",MN({},c,{className:m("recharts-cartesian-axis-line",mo(s,"className"))}))}},{key:"renderTicks",value:function(e,n,r){var i=this,o=this.props,a=o.tickLine,s=o.stroke,c=o.tick,l=o.tickFormatter,u=o.unit,f=AN(CN(CN({},this.props),{},{ticks:e}),n,r),d=this.getTickTextAnchor(),p=this.getTickVerticalAnchor(),h=Sa(this.props,!1),y=Sa(c,!1),g=CN(CN({},h),{},{fill:"none"},Sa(a,!1)),b=f.map((function(e,n){var r=i.getTickLineCoord(e),o=r.line,b=r.tick,x=CN(CN(CN(CN({textAnchor:d,verticalAnchor:p},h),{},{stroke:"none",fill:s},y),b),{},{index:n,payload:e,visibleTicksCount:f.length,tickFormatter:l});return v.createElement(Ia,MN({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},ua(i.props,e,n)),a&&v.createElement("line",MN({},g,o,{className:m("recharts-cartesian-axis-tick-line",mo(a,"className"))})),c&&t.renderTickItem(c,x,"".concat(jr(l)?l(e.value,n):e.value).concat(u||"")))}));return v.createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var e=this,t=this.props,n=t.axisLine,r=t.width,i=t.height,o=t.ticksGenerator,a=t.className;if(t.hide)return null;var s=this.props,c=s.ticks,l=DN(s,EN),u=c;return jr(o)&&(u=c&&c.length>0?o(this.props):o(l)),r<=0||i<=0||!u||!u.length?null:v.createElement(Ia,{className:m("recharts-cartesian-axis",a),ref:function(t){e.layerReference=t}},n&&this.renderAxisLine(),this.renderTicks(u,this.state.fontSize,this.state.letterSpacing),gO.renderCallByParent(this.props))}}],i=[{key:"renderTickItem",value:function(e,t,n){return v.isValidElement(e)?v.cloneElement(e,t):jr(e)?e(t):v.createElement(Wy,MN({},t,{className:"recharts-cartesian-axis-tick-value"}),n)}}],r&&IN(n.prototype,r),i&&IN(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();FN(UN,"displayName","CartesianAxis"),FN(UN,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var WN=["x1","y1","x2","y2","key"],qN=["offset"];function HN(e){return HN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},HN(e)}function VN(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function YN(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?VN(Object(n),!0).forEach((function(t){XN(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):VN(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function XN(e,t,n){return t=function(e){var t=function(e,t){if("object"!=HN(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=HN(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==HN(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function GN(){return GN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},GN.apply(this,arguments)}function KN(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var ZN=function(e){var t=e.fill;if(!t||"none"===t)return null;var n=e.fillOpacity,r=e.x,i=e.y,o=e.width,a=e.height,s=e.ry;return v.createElement("rect",{x:r,y:i,ry:s,width:o,height:a,stroke:"none",fill:t,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function QN(e,t){var n;if(v.isValidElement(e))n=v.cloneElement(e,t);else if(jr(e))n=e(t);else{var r=t.x1,i=t.y1,o=t.x2,a=t.y2,s=t.key,c=KN(t,WN),l=Sa(c,!1);l.offset;var u=KN(l,qN);n=v.createElement("line",GN({},u,{x1:r,y1:i,x2:o,y2:a,fill:"none",key:s}))}return n}function JN(e){var t=e.x,n=e.width,r=e.horizontal,i=void 0===r||r,o=e.horizontalPoints;if(!i||!o||!o.length)return null;var a=o.map((function(r,o){var a=YN(YN({},e),{},{x1:t,y1:r,x2:t+n,y2:r,key:"line-".concat(o),index:o});return QN(i,a)}));return v.createElement("g",{className:"recharts-cartesian-grid-horizontal"},a)}function eM(e){var t=e.y,n=e.height,r=e.vertical,i=void 0===r||r,o=e.verticalPoints;if(!i||!o||!o.length)return null;var a=o.map((function(r,o){var a=YN(YN({},e),{},{x1:r,y1:t,x2:r,y2:t+n,key:"line-".concat(o),index:o});return QN(i,a)}));return v.createElement("g",{className:"recharts-cartesian-grid-vertical"},a)}function tM(e){var t=e.horizontalFill,n=e.fillOpacity,r=e.x,i=e.y,o=e.width,a=e.height,s=e.horizontalPoints,c=e.horizontal;if(!(void 0===c||c)||!t||!t.length)return null;var l=s.map((function(e){return Math.round(e+i-i)})).sort((function(e,t){return e-t}));i!==l[0]&&l.unshift(0);var u=l.map((function(e,s){var c=!l[s+1]?i+a-e:l[s+1]-e;if(c<=0)return null;var u=s%t.length;return v.createElement("rect",{key:"react-".concat(s),y:e,x:r,height:c,width:o,stroke:"none",fill:t[u],fillOpacity:n,className:"recharts-cartesian-grid-bg"})}));return v.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},u)}function nM(e){var t=e.vertical,n=void 0===t||t,r=e.verticalFill,i=e.fillOpacity,o=e.x,a=e.y,s=e.width,c=e.height,l=e.verticalPoints;if(!n||!r||!r.length)return null;var u=l.map((function(e){return Math.round(e+o-o)})).sort((function(e,t){return e-t}));o!==u[0]&&u.unshift(0);var f=u.map((function(e,t){var n=!u[t+1]?o+s-e:u[t+1]-e;if(n<=0)return null;var l=t%r.length;return v.createElement("rect",{key:"react-".concat(t),x:e,y:a,width:n,height:c,stroke:"none",fill:r[l],fillOpacity:i,className:"recharts-cartesian-grid-bg"})}));return v.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var rM=function(e,t){var n=e.xAxis,r=e.width,i=e.height,o=e.offset;return Ej(AN(YN(YN(YN({},UN.defaultProps),n),{},{ticks:Nj(n,!0),viewBox:{x:0,y:0,width:r,height:i}})),o.left,o.left+o.width,t)},iM=function(e,t){var n=e.yAxis,r=e.width,i=e.height,o=e.offset;return Ej(AN(YN(YN(YN({},UN.defaultProps),n),{},{ticks:Nj(n,!0),viewBox:{x:0,y:0,width:r,height:i}})),o.top,o.top+o.height,t)},oM={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function aM(t){var n,r,i,o,a,s,c,l,u=TE(),f=CE(),d=e.useContext(PE),p=YN(YN({},t),{},{stroke:null!==(n=t.stroke)&&void 0!==n?n:oM.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:oM.fill,horizontal:null!==(i=t.horizontal)&&void 0!==i?i:oM.horizontal,horizontalFill:null!==(o=t.horizontalFill)&&void 0!==o?o:oM.horizontalFill,vertical:null!==(a=t.vertical)&&void 0!==a?a:oM.vertical,verticalFill:null!==(s=t.verticalFill)&&void 0!==s?s:oM.verticalFill,x:Go(t.x)?t.x:d.left,y:Go(t.y)?t.y:d.top,width:Go(t.width)?t.width:d.width,height:Go(t.height)?t.height:d.height}),h=p.x,y=p.y,m=p.width,g=p.height,b=p.syncWithTicks,x=p.horizontalValues,w=p.verticalValues,j=(c=e.useContext(jE),ea(c)),O=(l=e.useContext(OE),xE(l,(function(e){return E_(e.domain,Number.isFinite)}))||ea(l));if(!Go(m)||m<=0||!Go(g)||g<=0||!Go(h)||h!==+h||!Go(y)||y!==+y)return null;var S=p.verticalCoordinatesGenerator||rM,P=p.horizontalCoordinatesGenerator||iM,A=p.horizontalPoints,k=p.verticalPoints;if((!A||!A.length)&&jr(P)){var _=x&&x.length,E=P({yAxis:O?YN(YN({},O),{},{ticks:_?x:O.ticks}):void 0,width:u,height:f,offset:d},!!_||b);Ra(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(HN(E),"]")),Array.isArray(E)&&(A=E)}if((!k||!k.length)&&jr(S)){var N=w&&w.length,M=S({xAxis:j?YN(YN({},j),{},{ticks:N?w:j.ticks}):void 0,width:u,height:f,offset:d},!!N||b);Ra(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(HN(M),"]")),Array.isArray(M)&&(k=M)}return v.createElement("g",{className:"recharts-cartesian-grid"},v.createElement(ZN,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),v.createElement(JN,GN({},p,{offset:d,horizontalPoints:A,xAxis:j,yAxis:O})),v.createElement(eM,GN({},p,{offset:d,verticalPoints:k,xAxis:j,yAxis:O})),v.createElement(tM,GN({},p,{horizontalPoints:A})),v.createElement(nM,GN({},p,{verticalPoints:k})))}aM.displayName="CartesianGrid";var sM=["type","layout","connectNulls","ref"],cM=["key"];function lM(e){return lM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},lM(e)}function uM(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function fM(){return fM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},fM.apply(this,arguments)}function dM(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function pM(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?dM(Object(n),!0).forEach((function(t){wM(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):dM(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function hM(e){return function(e){if(Array.isArray(e))return yM(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return yM(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return yM(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yM(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function mM(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,jM(r.key),r)}}function vM(e,t,n){return t=bM(t),function(e,t){if(t&&("object"===lM(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,gM()?Reflect.construct(t,n||[],bM(e).constructor):t.apply(e,n))}function gM(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(gM=function(){return!!e})()}function bM(e){return bM=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},bM(e)}function xM(e,t){return xM=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},xM(e,t)}function wM(e,t,n){return(t=jM(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jM(e){var t=function(e,t){if("object"!=lM(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=lM(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==lM(t)?t:t+""}var OM=function(){function t(){var e;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return wM(e=vM(this,t,[].concat(r)),"state",{isAnimationFinished:!0,totalLength:0}),wM(e,"generateSimpleStrokeDasharray",(function(e,t){return"".concat(t,"px ").concat(e-t,"px")})),wM(e,"getStrokeDasharray",(function(n,r,i){var o=i.reduce((function(e,t){return e+t}));if(!o)return e.generateSimpleStrokeDasharray(r,n);for(var a=Math.floor(n/o),s=n%o,c=r-n,l=[],u=0,f=0;u<i.length;f+=i[u],++u)if(f+i[u]>s){l=[].concat(hM(i.slice(0,u)),[s-f]);break}var d=l.length%2==0?[0,c]:[c];return[].concat(hM(t.repeat(i,a)),hM(l),d).map((function(e){return"".concat(e,"px")})).join(", ")})),wM(e,"id",Qo("recharts-line-")),wM(e,"pathRef",(function(t){e.mainCurve=t})),wM(e,"handleAnimationEnd",(function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()})),wM(e,"handleAnimationStart",(function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()})),e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xM(e,t)}(t,e.PureComponent),n=t,r=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var n=this.props,r=n.points,i=n.xAxis,o=n.yAxis,a=n.layout,s=xa(n.children,fj);if(!s)return null;var c=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:Oj(e.payload,t)}},l={clipPath:e?"url(#clipPath-".concat(t,")"):null};return v.createElement(Ia,l,s.map((function(e){return v.cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:r,xAxis:i,yAxis:o,layout:a,dataPointFormatter:c})})))}},{key:"renderDots",value:function(e,n,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,o=i.dot,a=i.points,s=i.dataKey,c=Sa(this.props,!1),l=Sa(o,!0),u=a.map((function(e,n){var r=pM(pM(pM({key:"dot-".concat(n),r:3},c),l),{},{value:e.value,dataKey:s,cx:e.x,cy:e.y,index:n,payload:e.payload});return t.renderDotItem(o,r)})),f={clipPath:e?"url(#clipPath-".concat(n?"":"dots-").concat(r,")"):null};return v.createElement(Ia,fM({className:"recharts-line-dots",key:"dots"},f),u)}},{key:"renderCurveStatically",value:function(e,t,n,r){var i=this.props,o=i.type,a=i.layout,s=i.connectNulls;i.ref;var c=uM(i,sM),l=pM(pM(pM({},Sa(c,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(n,")"):null,points:e},r),{},{type:o,layout:a,connectNulls:s});return v.createElement(JO,fM({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var n=this,r=this.props,i=r.points,o=r.strokeDasharray,a=r.isAnimationActive,s=r.animationBegin,c=r.animationDuration,l=r.animationEasing,u=r.animationId,f=r.animateNewValues,d=r.width,p=r.height,h=this.state,y=h.prevPoints,m=h.totalLength;return v.createElement(wP,{begin:s,duration:c,isActive:a,easing:l,from:{t:0},to:{t:1},key:"line-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},(function(r){var a=r.t;if(y){var s=y.length/i.length,c=i.map((function(e,t){var n=Math.floor(t*s);if(y[n]){var r=y[n],i=ta(r.x,e.x),o=ta(r.y,e.y);return pM(pM({},e),{},{x:i(a),y:o(a)})}if(f){var c=ta(2*d,e.x),l=ta(p/2,e.y);return pM(pM({},e),{},{x:c(a),y:l(a)})}return pM(pM({},e),{},{x:e.x,y:e.y})}));return n.renderCurveStatically(c,e,t)}var l,u=ta(0,m)(a);if(o){var h="".concat(o).split(/[,\s]+/gim).map((function(e){return parseFloat(e)}));l=n.getStrokeDasharray(u,m,h)}else l=n.generateSimpleStrokeDasharray(m,u);return n.renderCurveStatically(i,e,t,{strokeDasharray:l})}))}},{key:"renderCurve",value:function(e,t){var n=this.props,r=n.points,i=n.isAnimationActive,o=this.state,a=o.prevPoints,s=o.totalLength;return i&&r&&r.length&&(!a&&s>0||!Xx(a,r))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(r,e,t)}},{key:"render",value:function(){var e,t=this.props,n=t.hide,r=t.dot,i=t.points,o=t.className,a=t.xAxis,s=t.yAxis,c=t.top,l=t.left,u=t.width,f=t.height,d=t.isAnimationActive,p=t.id;if(n||!i||!i.length)return null;var h=this.state.isAnimationFinished,y=1===i.length,g=m("recharts-line",o),b=a&&a.allowDataOverflow,x=s&&s.allowDataOverflow,w=b||x,j=vo(p)?this.id:p,O=null!==(e=Sa(r,!1))&&void 0!==e?e:{r:3,strokeWidth:2},S=O.r,P=void 0===S?3:S,A=O.strokeWidth,k=void 0===A?2:A,_=(function(e){return e&&"object"===ha(e)&&"clipDot"in e}(r)?r:{}).clipDot,E=void 0===_||_,N=2*P+k;return v.createElement(Ia,{className:g},b||x?v.createElement("defs",null,v.createElement("clipPath",{id:"clipPath-".concat(j)},v.createElement("rect",{x:b?l:l-u/2,y:x?c:c-f/2,width:b?u:2*u,height:x?f:2*f})),!E&&v.createElement("clipPath",{id:"clipPath-dots-".concat(j)},v.createElement("rect",{x:l-N/2,y:c-N/2,width:u+N,height:f+N}))):null,!y&&this.renderCurve(w,j),this.renderErrorBar(w,j),(y||r)&&this.renderDots(w,E,j),(!d||h)&&CO.renderCallByParent(this.props,i))}}],i=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var n=e.length%2!=0?[].concat(hM(e),[0]):e,r=[],i=0;i<t;++i)r=[].concat(hM(r),hM(n));return r}},{key:"renderDotItem",value:function(e,t){var n;if(v.isValidElement(e))n=v.cloneElement(e,t);else if(jr(e))n=e(t);else{var r=t.key,i=uM(t,cM),o=m("recharts-line-dot","boolean"!=typeof e?e.className:"");n=v.createElement(UP,fM({key:r},i,{className:o}))}return n}}],r&&mM(n.prototype,r),i&&mM(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,i}();function SM(e){return SM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},SM(e)}function PM(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,MM(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function AM(e,t,n){return t=_M(t),function(e,t){if(t&&("object"===SM(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,kM()?Reflect.construct(t,n||[],_M(e).constructor):t.apply(e,n))}function kM(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(kM=function(){return!!e})()}function _M(e){return _M=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_M(e)}function EM(e,t){return EM=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},EM(e,t)}function NM(e,t,n){return(t=MM(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function MM(e){var t=function(e,t){if("object"!=SM(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=SM(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==SM(t)?t:t+""}function TM(){return TM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},TM.apply(this,arguments)}function CM(e){var t=e.xAxisId,n=TE(),r=CE(),i=NE(t);return null==i?null:v.createElement(UN,TM({},i,{className:m("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:n,height:r},ticksGenerator:function(e){return Nj(e,!0)}}))}wM(OM,"displayName","Line"),wM(OM,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!vh.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),wM(OM,"getComposedData",(function(e){var t=e.props,n=e.xAxis,r=e.yAxis,i=e.xAxisTicks,o=e.yAxisTicks,a=e.dataKey,s=e.bandSize,c=e.displayedData,l=e.offset,u=t.layout;return pM({points:c.map((function(e,t){var c=Oj(e,a);return"horizontal"===u?{x:zj({axis:n,ticks:i,bandSize:s,entry:e,index:t}),y:vo(c)?null:r.scale(c),value:c,payload:e}:{x:vo(c)?null:n.scale(c),y:zj({axis:r,ticks:o,bandSize:s,entry:e,index:t}),value:c,payload:e}})),layout:u},l)}));var DM=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),AM(this,e,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&EM(e,t)}(e,v.Component),PM(e,[{key:"render",value:function(){return v.createElement(CM,this.props)}}])}();function IM(e){return IM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},IM(e)}function RM(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,UM(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function BM(e,t,n){return t=zM(t),function(e,t){if(t&&("object"===IM(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,LM()?Reflect.construct(t,n||[],zM(e).constructor):t.apply(e,n))}function LM(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(LM=function(){return!!e})()}function zM(e){return zM=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},zM(e)}function FM(e,t){return FM=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},FM(e,t)}function $M(e,t,n){return(t=UM(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function UM(e){var t=function(e,t){if("object"!=IM(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=IM(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==IM(t)?t:t+""}function WM(){return WM=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},WM.apply(this,arguments)}NM(DM,"displayName","XAxis"),NM(DM,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var qM=function(e){var t=e.yAxisId,n=TE(),r=CE(),i=ME(t);return null==i?null:v.createElement(UN,WM({},i,{className:m("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:n,height:r},ticksGenerator:function(e){return Nj(e,!0)}}))},HM=function(){function e(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),BM(this,e,arguments)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&FM(e,t)}(e,v.Component),RM(e,[{key:"render",value:function(){return v.createElement(qM,this.props)}}])}();function VM(e){return function(e){if(Array.isArray(e))return YM(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return YM(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return YM(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function YM(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}$M(HM,"displayName","YAxis"),$M(HM,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var XM=function(e,t,n,r,i){var o=xa(e,XE),a=xa(e,aN),s=[].concat(VM(o),VM(a)),c=xa(e,gN),l="".concat(r,"Id"),u=r[0],f=t;if(s.length&&(f=s.reduce((function(e,t){if(t.props[l]===n&&h_(t.props,"extendDomain")&&Go(t.props[u])){var r=t.props[u];return[Math.min(e[0],r),Math.max(e[1],r)]}return e}),f)),c.length){var d="".concat(u,"1"),p="".concat(u,"2");f=c.reduce((function(e,t){if(t.props[l]===n&&h_(t.props,"extendDomain")&&Go(t.props[d])&&Go(t.props[p])){var r=t.props[d],i=t.props[p];return[Math.min(e[0],r,i),Math.max(e[1],r,i)]}return e}),f)}return i&&i.length&&(f=i.reduce((function(e,t){return Go(t)?[Math.min(e[0],t),Math.max(e[1],t)]:e}),f)),f},GM={exports:{}};!function(e){var t=Object.prototype.hasOwnProperty,n="~";function r(){}function i(e,t,n){this.fn=e,this.context=t,this.once=n||!1}function o(e,t,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var s=new i(r,o||e,a),c=n?n+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],s]:e._events[c].push(s):(e._events[c]=s,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new r:delete e._events[t]}function s(){this._events=new r,this._eventsCount=0}Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(n=!1)),s.prototype.eventNames=function(){var e,r,i=[];if(0===this._eventsCount)return i;for(r in e=this._events)t.call(e,r)&&i.push(n?r.slice(1):r);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},s.prototype.listeners=function(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,o=r.length,a=new Array(o);i<o;i++)a[i]=r[i].fn;return a},s.prototype.listenerCount=function(e){var t=n?n+e:e,r=this._events[t];return r?r.fn?1:r.length:0},s.prototype.emit=function(e,t,r,i,o,a){var s=n?n+e:e;if(!this._events[s])return!1;var c,l,u=this._events[s],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,r),!0;case 4:return u.fn.call(u.context,t,r,i),!0;case 5:return u.fn.call(u.context,t,r,i,o),!0;case 6:return u.fn.call(u.context,t,r,i,o,a),!0}for(l=1,c=new Array(f-1);l<f;l++)c[l-1]=arguments[l];u.fn.apply(u.context,c)}else{var d,p=u.length;for(l=0;l<p;l++)switch(u[l].once&&this.removeListener(e,u[l].fn,void 0,!0),f){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,t);break;case 3:u[l].fn.call(u[l].context,t,r);break;case 4:u[l].fn.call(u[l].context,t,r,i);break;default:if(!c)for(d=1,c=new Array(f-1);d<f;d++)c[d-1]=arguments[d];u[l].fn.apply(u[l].context,c)}}return!0},s.prototype.on=function(e,t,n){return o(this,e,t,n,!1)},s.prototype.once=function(e,t,n){return o(this,e,t,n,!0)},s.prototype.removeListener=function(e,t,r,i){var o=n?n+e:e;if(!this._events[o])return this;if(!t)return a(this,o),this;var s=this._events[o];if(s.fn)s.fn!==t||i&&!s.once||r&&s.context!==r||a(this,o);else{for(var c=0,l=[],u=s.length;c<u;c++)(s[c].fn!==t||i&&!s[c].once||r&&s[c].context!==r)&&l.push(s[c]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},s.prototype.removeAllListeners=function(e){var t;return e?(t=n?n+e:e,this._events[t]&&a(this,t)):(this._events=new r,this._eventsCount=0),this},s.prototype.off=s.prototype.removeListener,s.prototype.addListener=s.prototype.on,s.prefixed=n,s.EventEmitter=s,e.exports=s}(GM);var KM=new(y(GM.exports)),ZM="recharts.syncMouseEvents";function QM(e){return QM="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},QM(e)}function JM(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,tT(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function eT(e,t,n){return(t=tT(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function tT(e){var t=function(e,t){if("object"!=QM(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t);if("object"!=QM(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==QM(t)?t:t+""}var nT=function(){return JM((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),eT(this,"activeIndex",0),eT(this,"coordinateList",[]),eT(this,"layout","horizontal")}),[{key:"setDetails",value:function(e){var t,n=e.coordinateList,r=void 0===n?null:n,i=e.container,o=void 0===i?null:i,a=e.layout,s=void 0===a?null:a,c=e.offset,l=void 0===c?null:c,u=e.mouseHandlerCallback,f=void 0===u?null:u;this.coordinateList=null!==(t=null!=r?r:this.coordinateList)&&void 0!==t?t:[],this.container=null!=o?o:this.container,this.layout=null!=s?s:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(e){if(0!==this.coordinateList.length)switch(e.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(e){this.activeIndex=e}},{key:"spoofMouse",value:function(){var e,t;if("horizontal"===this.layout&&0!==this.coordinateList.length){var n=this.container.getBoundingClientRect(),r=n.x,i=n.y,o=n.height,a=this.coordinateList[this.activeIndex].coordinate,s=(null===(e=window)||void 0===e?void 0:e.scrollX)||0,c=(null===(t=window)||void 0===t?void 0:t.scrollY)||0,l=r+a+s,u=i+this.offset.top+o/2+c;this.mouseHandlerCallback({pageX:l,pageY:u})}}}])}();function rT(e){var t=e.cx,n=e.cy,r=e.radius,i=e.startAngle,o=e.endAngle;return{points:[nO(t,n,r,i),nO(t,n,r,o)],cx:t,cy:n,radius:r,startAngle:i,endAngle:o}}function iT(e,t,n){var r,i,o,a;if("horizontal"===e)o=r=t.x,i=n.top,a=n.top+n.height;else if("vertical"===e)a=i=t.y,r=n.left,o=n.left+n.width;else if(null!=t.cx&&null!=t.cy){if("centric"!==e)return rT(t);var s=t.cx,c=t.cy,l=t.innerRadius,u=t.outerRadius,f=t.angle,d=nO(s,c,l,f),p=nO(s,c,u,f);r=d.x,i=d.y,o=p.x,a=p.y}return[{x:r,y:i},{x:o,y:a}]}function oT(e){return oT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},oT(e)}function aT(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function sT(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?aT(Object(n),!0).forEach((function(t){cT(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):aT(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function cT(e,t,n){return t=function(e){var t=function(e,t){if("object"!=oT(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=oT(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==oT(t)?t:t+""}(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function lT(t){var n,r,i,o=t.element,a=t.tooltipEventType,s=t.isActive,c=t.activeCoordinate,l=t.activePayload,u=t.offset,f=t.activeTooltipIndex,d=t.tooltipAxisBandSize,p=t.layout,h=t.chartName,y=null!==(n=o.props.cursor)&&void 0!==n?n:null===(r=o.type.defaultProps)||void 0===r?void 0:r.cursor;if(!o||!y||!s||!c||"ScatterChart"!==h&&"axis"!==a)return null;var v=JO;if("ScatterChart"===h)i=c,v=KP;else if("BarChart"===h)i=function(e,t,n,r){var i=r/2;return{stroke:"none",fill:"#ccc",x:"horizontal"===e?t.x-i:n.left+.5,y:"horizontal"===e?n.top+.5:t.y-i,width:"horizontal"===e?r:n.width-1,height:"horizontal"===e?n.height-1:r}}(p,c,u,d),v=TP;else if("radial"===p){var g=rT(c),b=g.cx,x=g.cy,w=g.radius;i={cx:b,cy:x,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},v=UO}else i={points:iT(p,c,u)},v=JO;var j=sT(sT(sT(sT({stroke:"#ccc",pointerEvents:"none"},u),i),Sa(y,!1)),{},{payload:l,payloadIndex:f,className:m("recharts-tooltip-cursor",y.className)});return e.isValidElement(y)?e.cloneElement(y,j):e.createElement(v,j)}var uT=["item"],fT=["children","className","width","height","style","compact","title","desc"];function dT(e){return(dT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function pT(){return pT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},pT.apply(this,arguments)}function hT(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,o,a,s=[],c=!0,l=!1;try{if(o=(n=n.call(e)).next,0===t);else for(;!(c=(r=o.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(u){l=!0,i=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw i}}return s}}(e,t)||jT(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yT(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function mT(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,kT(r.key),r)}}(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e}function vT(e,t,n){return t=bT(t),function(e,t){if(t&&("object"===dT(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,gT()?Reflect.construct(t,n||[],bT(e).constructor):t.apply(e,n))}function gT(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(gT=function(){return!!e})()}function bT(e){return(bT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function xT(e,t){return(xT=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function wT(e){return function(e){if(Array.isArray(e))return OT(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||jT(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jT(e,t){if(e){if("string"==typeof e)return OT(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?OT(e,t):void 0}}function OT(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function ST(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function PT(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ST(Object(n),!0).forEach((function(t){AT(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ST(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function AT(e,t,n){return(t=kT(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kT(e){var t=function(e,t){if("object"!=dT(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=dT(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==dT(t)?t:t+""}var _T={xAxis:["bottom","top"],yAxis:["left","right"]},ET={width:"100%",height:"100%"},NT={x:0,y:0};function MT(e){return e}var TT=function(e,t){var n=t.graphicalItems,r=t.dataStartIndex,i=t.dataEndIndex,o=(null!=n?n:[]).reduce((function(e,t){var n=t.props.data;return n&&n.length?[].concat(wT(e),wT(n)):e}),[]);return o.length>0?o:e&&e.length&&Go(r)&&Go(i)?e.slice(r,i+1):[]};function CT(e){return"number"===e?[0,"auto"]:void 0}var DT=function(e,t,n,r){var i=e.graphicalItems,o=e.tooltipAxis,a=TT(t,e);return n<0||!i||!i.length||n>=a.length?null:i.reduce((function(i,s){var c,l,u=null!==(c=s.props.data)&&void 0!==c?c:t;(u&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=n&&(u=u.slice(e.dataStartIndex,e.dataEndIndex+1)),o.dataKey&&!o.allowDuplicatedCategory)?l=na(void 0===u?a:u,o.dataKey,r):l=u&&u[n]||a[n];return l?[].concat(wT(i),[Yj(s,l)]):i}),[])},IT=function(e,t,n,r){var i=r||{x:e.chartX,y:e.chartY},o=function(e,t){return"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius}(i,n),a=e.orderedTooltipTicks,s=e.tooltipAxis,c=e.tooltipTicks,l=function(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!==(t=null==n?void 0:n.length)&&void 0!==t?t:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&Math.abs(Math.abs(i.range[1]-i.range[0])-360)<=1e-6)for(var s=i.range,c=0;c<a;c++){var l=c>0?r[c-1].coordinate:r[a-1].coordinate,u=r[c].coordinate,f=c>=a-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if(Yo(u-l)!==Yo(f-u)){var p=[];if(Yo(f-u)===Yo(s[1]-s[0])){d=f;var h=u+s[1]-s[0];p[0]=Math.min(h,(h+l)/2),p[1]=Math.max(h,(h+l)/2)}else{d=l;var y=f+s[1]-s[0];p[0]=Math.min(u,(y+u)/2),p[1]=Math.max(u,(y+u)/2)}var m=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>m[0]&&e<=m[1]||e>=p[0]&&e<=p[1]){o=r[c].index;break}}else{var v=Math.min(l,f),g=Math.max(l,f);if(e>(v+u)/2&&e<=(g+u)/2){o=r[c].index;break}}}else for(var b=0;b<a;b++)if(0===b&&e<=(n[b].coordinate+n[b+1].coordinate)/2||b>0&&b<a-1&&e>(n[b].coordinate+n[b-1].coordinate)/2&&e<=(n[b].coordinate+n[b+1].coordinate)/2||b===a-1&&e>(n[b].coordinate+n[b-1].coordinate)/2){o=n[b].index;break}return o}(o,a,c,s);if(l>=0&&c){var u=c[l]&&c[l].value,f=DT(e,t,l,u),d=function(e,t,n,r){var i=t.find((function(e){return e&&e.index===n}));if(i){if("horizontal"===e)return{x:i.coordinate,y:r.y};if("vertical"===e)return{x:r.x,y:i.coordinate};if("centric"===e){var o=i.coordinate,a=r.radius;return PT(PT(PT({},r),nO(r.cx,r.cy,a,o)),{},{angle:o,radius:a})}var s=i.coordinate,c=r.angle;return PT(PT(PT({},r),nO(r.cx,r.cy,s,c)),{},{angle:c,radius:s})}return NT}(n,a,l,i);return{activeTooltipIndex:l,activeLabel:u,activePayload:f,activeCoordinate:d}}return null},RT=function(e,t){var n=t.axes,r=t.graphicalItems,i=t.axisType,o=t.axisIdKey,a=t.stackGroups,s=t.dataStartIndex,c=t.dataEndIndex,l=e.layout,u=e.children,f=e.stackOffset,d=_j(l,i);return n.reduce((function(t,n){var p,h=void 0!==n.type.defaultProps?PT(PT({},n.type.defaultProps),n.props):n.props,y=h.type,m=h.dataKey,v=h.allowDataOverflow,g=h.allowDuplicatedCategory,b=h.scale,x=h.ticks,w=h.includeHidden,j=h[o];if(t[j])return t;var O,S,P,A=TT(e.data,{graphicalItems:r.filter((function(e){var t;return(o in e.props?e.props[o]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[o])===j})),dataStartIndex:s,dataEndIndex:c}),k=A.length;(function(e,t,n){if("number"===n&&!0===t&&Array.isArray(e)){var r=null==e?void 0:e[0],i=null==e?void 0:e[1];if(r&&i&&Go(r)&&Go(i))return!0}return!1})(h.domain,v,y)&&(O=qj(h.domain,null,v),!d||"number"!==y&&"auto"===b||(P=Sj(A,m,"category")));var _=CT(y);if(!O||0===O.length){var E,N=null!==(E=h.domain)&&void 0!==E?E:_;if(m){if(O=Sj(A,m,y),"category"===y&&d){var M=function(e){if(!Array.isArray(e))return!1;for(var t=e.length,n={},r=0;r<t;r++){if(n[e[r]])return!0;n[e[r]]=!0}return!1}(O);g&&M?(S=O,O=Uk(0,k)):g||(O=Vj(N,O,n).reduce((function(e,t){return e.indexOf(t)>=0?e:[].concat(wT(e),[t])}),[]))}else if("category"===y)O=g?O.filter((function(e){return""!==e&&!vo(e)})):Vj(N,O,n).reduce((function(e,t){return e.indexOf(t)>=0||""===t||vo(t)?e:[].concat(wT(e),[t])}),[]);else if("number"===y){var T=function(e,t,n,r,i){var o=t.map((function(t){return Aj(e,t,n,i,r)})).filter((function(e){return!vo(e)}));return o&&o.length?o.reduce((function(e,t){return[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}),[1/0,-1/0]):null}(A,r.filter((function(e){var t,n,r=o in e.props?e.props[o]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[o],i="hide"in e.props?e.props.hide:null===(n=e.type.defaultProps)||void 0===n?void 0:n.hide;return r===j&&(w||!i)})),m,i,l);T&&(O=T)}!d||"number"!==y&&"auto"===b||(P=Sj(A,m,"category"))}else O=d?Uk(0,k):a&&a[j]&&a[j].hasStack&&"number"===y?"expand"===f?[0,1]:$j(a[j].stackGroups,s,c):kj(A,r.filter((function(e){var t=o in e.props?e.props[o]:e.type.defaultProps[o],n="hide"in e.props?e.props.hide:e.type.defaultProps.hide;return t===j&&(w||!n)})),y,l,!0);if("number"===y)O=XM(u,O,j,i,x),N&&(O=qj(N,O,v));else if("category"===y&&N){var C=N;O.every((function(e){return C.indexOf(e)>=0}))&&(O=C)}}return PT(PT({},t),{},AT({},j,PT(PT({},h),{},{axisType:i,domain:O,categoricalDomain:P,duplicateDomain:S,originalDomain:null!==(p=h.domain)&&void 0!==p?p:_,isCategorical:d,layout:l})))}),{})},BT=function(e,t){var n=t.axisType,r=void 0===n?"xAxis":n,i=t.AxisComp,o=t.graphicalItems,a=t.stackGroups,s=t.dataStartIndex,c=t.dataEndIndex,l=e.children,u="".concat(r,"Id"),f=xa(l,i),d={};return f&&f.length?d=RT(e,{axes:f,graphicalItems:o,axisType:r,axisIdKey:u,stackGroups:a,dataStartIndex:s,dataEndIndex:c}):o&&o.length&&(d=function(e,t){var n=t.graphicalItems,r=t.Axis,i=t.axisType,o=t.axisIdKey,a=t.stackGroups,s=t.dataStartIndex,c=t.dataEndIndex,l=e.layout,u=e.children,f=TT(e.data,{graphicalItems:n,dataStartIndex:s,dataEndIndex:c}),d=f.length,p=_j(l,i),h=-1;return n.reduce((function(e,t){var y,m=(void 0!==t.type.defaultProps?PT(PT({},t.type.defaultProps),t.props):t.props)[o],v=CT("number");return e[m]?e:(h++,p?y=Uk(0,d):a&&a[m]&&a[m].hasStack?(y=$j(a[m].stackGroups,s,c),y=XM(u,y,m,i)):(y=qj(v,kj(f,n.filter((function(e){var t,n,r=o in e.props?e.props[o]:null===(t=e.type.defaultProps)||void 0===t?void 0:t[o],i="hide"in e.props?e.props.hide:null===(n=e.type.defaultProps)||void 0===n?void 0:n.hide;return r===m&&!i})),"number",l),r.defaultProps.allowDataOverflow),y=XM(u,y,m,i)),PT(PT({},e),{},AT({},m,PT(PT({axisType:i},r.defaultProps),{},{hide:!0,orientation:mo(_T,"".concat(i,".").concat(h%2),null),domain:y,originalDomain:v,isCategorical:p,layout:l}))))}),{})}(e,{Axis:i,graphicalItems:o,axisType:r,axisIdKey:u,stackGroups:a,dataStartIndex:s,dataEndIndex:c})),d},LT=function(e){var t=e.children,n=e.defaultShowTooltip,r=wa(t,a_),i=0,o=0;return e.data&&0!==e.data.length&&(o=e.data.length-1),r&&r.props&&(r.props.startIndex>=0&&(i=r.props.startIndex),r.props.endIndex>=0&&(o=r.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:Boolean(n)}},zT=function(e){return"horizontal"===e?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===e?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===e?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},FT=function(e,t){var n=e.props,r=(e.graphicalItems,e.xAxisMap),i=void 0===r?{}:r,o=e.yAxisMap,a=void 0===o?{}:o,s=n.width,c=n.height,l=n.children,u=n.margin||{},f=wa(l,a_),d=wa(l,qd),p=Object.keys(a).reduce((function(e,t){var n=a[t],r=n.orientation;return n.mirror||n.hide?e:PT(PT({},e),{},AT({},r,e[r]+n.width))}),{left:u.left||0,right:u.right||0}),h=Object.keys(i).reduce((function(e,t){var n=i[t],r=n.orientation;return n.mirror||n.hide?e:PT(PT({},e),{},AT({},r,mo(e,"".concat(r))+n.height))}),{top:u.top||0,bottom:u.bottom||0}),y=PT(PT({},h),p),m=y.bottom;f&&(y.bottom+=f.props.height||a_.defaultProps.height),d&&t&&(y=function(e,t,n,r){var i=n.children,o=n.width,a=n.margin,s=o-(a.left||0)-(a.right||0),c=mj({children:i,legendWidth:s});if(c){var l=r||{},u=l.width,f=l.height,d=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==d&&Go(e[d]))return wj(wj({},e),{},jj({},d,e[d]+(u||0)));if(("horizontal"===h||"vertical"===h&&"center"===d)&&"middle"!==p&&Go(e[p]))return wj(wj({},e),{},jj({},p,e[p]+(f||0)))}return e}(y,0,n,t));var v=s-y.left-y.right,g=c-y.top-y.bottom;return PT(PT({brushBottom:m},y),{},{width:Math.max(v,0),height:Math.max(g,0)})},$T=function(e,t){return"xAxis"===t?e[t].width:"yAxis"===t?e[t].height:void 0},UT=function(t){var n=t.chartName,r=t.GraphicalChild,i=t.defaultTooltipEventType,o=void 0===i?"axis":i,a=t.validateTooltipEventTypes,s=void 0===a?["axis"]:a,c=t.axisComponents,l=t.legendContent,u=t.formatAxisMap,f=t.defaultProps,d=function(e,t){var n=t.graphicalItems,r=t.stackGroups,i=t.offset,o=t.updateId,a=t.dataStartIndex,s=t.dataEndIndex,l=e.barSize,u=e.layout,f=e.barGap,d=e.barCategoryGap,p=e.maxBarSize,h=zT(u),y=h.numericAxisName,m=h.cateAxisName,v=function(e){return!(!e||!e.length)&&e.some((function(e){var t=ma(e&&e.type);return t&&t.indexOf("Bar")>=0}))}(n),g=[];return n.forEach((function(n,h){var b=TT(e.data,{graphicalItems:[n],dataStartIndex:a,dataEndIndex:s}),x=void 0!==n.type.defaultProps?PT(PT({},n.type.defaultProps),n.props):n.props,w=x.dataKey,j=x.maxBarSize,O=x["".concat(y,"Id")],S=x["".concat(m,"Id")],P=c.reduce((function(e,n){var r=t["".concat(n.axisType,"Map")],i=x["".concat(n.axisType,"Id")];r&&r[i]||"zAxis"===n.axisType||Zw();var o=r[i];return PT(PT({},e),{},AT(AT({},n.axisType,o),"".concat(n.axisType,"Ticks"),Nj(o)))}),{}),A=P[m],k=P["".concat(m,"Ticks")],_=r&&r[O]&&r[O].hasStack&&function(e,t){var n,r=(null!==(n=e.type)&&void 0!==n&&n.defaultProps?wj(wj({},e.type.defaultProps),e.props):e.props).stackId;if(Ko(r)){var i=t[r];if(i){var o=i.items.indexOf(e);return o>=0?i.stackedData[o]:null}}return null}(n,r[O].stackGroups),E=ma(n.type).indexOf("Bar")>=0,N=Hj(A,k),M=[],T=v&&function(e){var t=e.barSize,n=e.totalSize,r=e.stackGroups,i=void 0===r?{}:r;if(!i)return{};for(var o={},a=Object.keys(i),s=0,c=a.length;s<c;s++)for(var l=i[a[s]].stackGroups,u=Object.keys(l),f=0,d=u.length;f<d;f++){var p=l[u[f]],h=p.items,y=p.cateAxisId,m=h.filter((function(e){return ma(e.type).indexOf("Bar")>=0}));if(m&&m.length){var v=m[0].type.defaultProps,g=void 0!==v?wj(wj({},v),m[0].props):m[0].props,b=g.barSize,x=g[y];o[x]||(o[x]=[]);var w=vo(b)?t:b;o[x].push({item:m[0],stackList:m.slice(1),barSize:vo(w)?void 0:Jo(w,n,0)})}}return o}({barSize:l,stackGroups:r,totalSize:$T(P,m)});if(E){var C,D,I=vo(j)?p:j,R=null!==(C=null!==(D=Hj(A,k,!0))&&void 0!==D?D:I)&&void 0!==C?C:0;M=function(e){var t=e.barGap,n=e.barCategoryGap,r=e.bandSize,i=e.sizeList,o=void 0===i?[]:i,a=e.maxBarSize,s=o.length;if(s<1)return null;var c,l=Jo(t,r,0,!0),u=[];if(o[0].barSize===+o[0].barSize){var f=!1,d=r/s,p=o.reduce((function(e,t){return e+t.barSize||0}),0);(p+=(s-1)*l)>=r&&(p-=(s-1)*l,l=0),p>=r&&d>0&&(f=!0,p=s*(d*=.9));var h={offset:((r-p)/2|0)-l,size:0};c=o.reduce((function(e,t){var n={item:t.item,position:{offset:h.offset+h.size+l,size:f?d:t.barSize}},r=[].concat(gj(e),[n]);return h=r[r.length-1].position,t.stackList&&t.stackList.length&&t.stackList.forEach((function(e){r.push({item:e,position:h})})),r}),u)}else{var y=Jo(n,r,0,!0);r-2*y-(s-1)*l<=0&&(l=0);var m=(r-2*y-(s-1)*l)/s;m>1&&(m>>=0);var v=a===+a?Math.min(m,a):m;c=o.reduce((function(e,t,n){var r=[].concat(gj(e),[{item:t.item,position:{offset:y+(m+l)*n+(m-v)/2,size:v}}]);return t.stackList&&t.stackList.length&&t.stackList.forEach((function(e){r.push({item:e,position:r[r.length-1].position})})),r}),u)}return c}({barGap:f,barCategoryGap:d,bandSize:R!==N?R:N,sizeList:T[S],maxBarSize:I}),R!==N&&(M=M.map((function(e){return PT(PT({},e),{},{position:PT(PT({},e.position),{},{offset:e.position.offset-R/2})})})))}var B,L,z=n&&n.type&&n.type.getComposedData;z&&g.push({props:PT(PT({},z(PT(PT({},P),{},{displayedData:b,props:e,dataKey:w,item:n,bandSize:N,barPosition:M,offset:i,stackedData:_,layout:u,dataStartIndex:a,dataEndIndex:s}))),{},AT(AT(AT({key:n.key||"item-".concat(h)},y,P[y]),m,P[m]),"animationId",o)),childIndex:(B=n,L=e.children,ba(L).indexOf(B)),item:n})})),g},p=function(e,t){var i=e.props,o=e.dataStartIndex,a=e.dataEndIndex,s=e.updateId;if(!ja({props:i}))return null;var l=i.children,f=i.layout,p=i.stackOffset,h=i.data,y=i.reverseStackOrder,m=zT(f),v=m.numericAxisName,g=m.cateAxisName,b=xa(l,r),x=function(e,t,n,r,i,o){if(!e)return null;var a=(o?t.reverse():t).reduce((function(e,t){var i,o=null!==(i=t.type)&&void 0!==i&&i.defaultProps?wj(wj({},t.type.defaultProps),t.props):t.props,a=o.stackId;if(o.hide)return e;var s=o[n],c=e[s]||{hasStack:!1,stackGroups:{}};if(Ko(a)){var l=c.stackGroups[a]||{numericAxisId:n,cateAxisId:r,items:[]};l.items.push(t),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[Qo("_stackId_")]={numericAxisId:n,cateAxisId:r,items:[t]};return wj(wj({},e),{},jj({},s,c))}),{});return Object.keys(a).reduce((function(t,o){var s=a[o];return s.hasStack&&(s.stackGroups=Object.keys(s.stackGroups).reduce((function(t,o){var a=s.stackGroups[o];return wj(wj({},t),{},jj({},o,{numericAxisId:n,cateAxisId:r,items:a.items,stackedData:Bj(e,a.items,i)}))}),{})),wj(wj({},t),{},jj({},o,s))}),{})}(h,b,"".concat(v,"Id"),"".concat(g,"Id"),p,y),w=c.reduce((function(e,t){var n="".concat(t.axisType,"Map");return PT(PT({},e),{},AT({},n,BT(i,PT(PT({},t),{},{graphicalItems:b,stackGroups:t.axisType===v&&x,dataStartIndex:o,dataEndIndex:a}))))}),{}),j=FT(PT(PT({},w),{},{props:i,graphicalItems:b}),null==t?void 0:t.legendBBox);Object.keys(w).forEach((function(e){w[e]=u(i,w[e],j,e.replace("Map",""),n)}));var O,S,P=w["".concat(g,"Map")],A=(O=ea(P),{tooltipTicks:S=Nj(O,!1,!0),orderedTooltipTicks:qp(S,(function(e){return e.coordinate})),tooltipAxis:O,tooltipAxisBandSize:Hj(O,S)}),k=d(i,PT(PT({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:s,graphicalItems:b,stackGroups:x,offset:j}));return PT(PT({formattedGraphicalItems:k,graphicalItems:b,offset:j,stackGroups:x},A),w)},h=function(){function t(r){var i,o,a;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),AT(a=vT(this,t,[r]),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),AT(a,"accessibilityManager",new nT),AT(a,"handleLegendBBoxUpdate",(function(e){if(e){var t=a.state,n=t.dataStartIndex,r=t.dataEndIndex,i=t.updateId;a.setState(PT({legendBBox:e},p({props:a.props,dataStartIndex:n,dataEndIndex:r,updateId:i},PT(PT({},a.state),{},{legendBBox:e}))))}})),AT(a,"handleReceiveSyncEvent",(function(e,t,n){if(a.props.syncId===e){if(n===a.eventEmitterSymbol&&"function"!=typeof a.props.syncMethod)return;a.applySyncEvent(t)}})),AT(a,"handleBrushChange",(function(e){var t=e.startIndex,n=e.endIndex;if(t!==a.state.dataStartIndex||n!==a.state.dataEndIndex){var r=a.state.updateId;a.setState((function(){return PT({dataStartIndex:t,dataEndIndex:n},p({props:a.props,dataStartIndex:t,dataEndIndex:n,updateId:r},a.state))})),a.triggerSyncEvent({dataStartIndex:t,dataEndIndex:n})}})),AT(a,"handleMouseEnter",(function(e){var t=a.getMouseInfo(e);if(t){var n=PT(PT({},t),{},{isTooltipActive:!0});a.setState(n),a.triggerSyncEvent(n);var r=a.props.onMouseEnter;jr(r)&&r(n,e)}})),AT(a,"triggeredAfterMouseMove",(function(e){var t=a.getMouseInfo(e),n=t?PT(PT({},t),{},{isTooltipActive:!0}):{isTooltipActive:!1};a.setState(n),a.triggerSyncEvent(n);var r=a.props.onMouseMove;jr(r)&&r(n,e)})),AT(a,"handleItemMouseEnter",(function(e){a.setState((function(){return{isTooltipActive:!0,activeItem:e,activePayload:e.tooltipPayload,activeCoordinate:e.tooltipPosition||{x:e.cx,y:e.cy}}}))})),AT(a,"handleItemMouseLeave",(function(){a.setState((function(){return{isTooltipActive:!1}}))})),AT(a,"handleMouseMove",(function(e){e.persist(),a.throttleTriggeredAfterMouseMove(e)})),AT(a,"handleMouseLeave",(function(e){a.throttleTriggeredAfterMouseMove.cancel();var t={isTooltipActive:!1};a.setState(t),a.triggerSyncEvent(t);var n=a.props.onMouseLeave;jr(n)&&n(t,e)})),AT(a,"handleOuterEvent",(function(e){var t,n=function(e){var t=e&&e.type;return t&&ya[t]?ya[t]:null}(e),r=mo(a.props,"".concat(n));n&&jr(r)&&r(null!==(t=/.*touch.*/i.test(n)?a.getMouseInfo(e.changedTouches[0]):a.getMouseInfo(e))&&void 0!==t?t:{},e)})),AT(a,"handleClick",(function(e){var t=a.getMouseInfo(e);if(t){var n=PT(PT({},t),{},{isTooltipActive:!0});a.setState(n),a.triggerSyncEvent(n);var r=a.props.onClick;jr(r)&&r(n,e)}})),AT(a,"handleMouseDown",(function(e){var t=a.props.onMouseDown;jr(t)&&t(a.getMouseInfo(e),e)})),AT(a,"handleMouseUp",(function(e){var t=a.props.onMouseUp;jr(t)&&t(a.getMouseInfo(e),e)})),AT(a,"handleTouchMove",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&a.throttleTriggeredAfterMouseMove(e.changedTouches[0])})),AT(a,"handleTouchStart",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&a.handleMouseDown(e.changedTouches[0])})),AT(a,"handleTouchEnd",(function(e){null!=e.changedTouches&&e.changedTouches.length>0&&a.handleMouseUp(e.changedTouches[0])})),AT(a,"triggerSyncEvent",(function(e){void 0!==a.props.syncId&&KM.emit(ZM,a.props.syncId,e,a.eventEmitterSymbol)})),AT(a,"applySyncEvent",(function(e){var t=a.props,n=t.layout,r=t.syncMethod,i=a.state.updateId,o=e.dataStartIndex,s=e.dataEndIndex;if(void 0!==e.dataStartIndex||void 0!==e.dataEndIndex)a.setState(PT({dataStartIndex:o,dataEndIndex:s},p({props:a.props,dataStartIndex:o,dataEndIndex:s,updateId:i},a.state)));else if(void 0!==e.activeTooltipIndex){var c=e.chartX,l=e.chartY,u=e.activeTooltipIndex,f=a.state,d=f.offset,h=f.tooltipTicks;if(!d)return;if("function"==typeof r)u=r(h,e);else if("value"===r){u=-1;for(var y=0;y<h.length;y++)if(h[y].value===e.activeLabel){u=y;break}}var m=PT(PT({},d),{},{x:d.left,y:d.top}),v=Math.min(c,m.x+m.width),g=Math.min(l,m.y+m.height),b=h[u]&&h[u].value,x=DT(a.state,a.props.data,u),w=h[u]?{x:"horizontal"===n?h[u].coordinate:v,y:"horizontal"===n?g:h[u].coordinate}:NT;a.setState(PT(PT({},e),{},{activeLabel:b,activeCoordinate:w,activePayload:x,activeTooltipIndex:u}))}else a.setState(e)})),AT(a,"renderCursor",(function(e){var t,r=a.state,i=r.isTooltipActive,o=r.activeCoordinate,s=r.activePayload,c=r.offset,l=r.activeTooltipIndex,u=r.tooltipAxisBandSize,f=a.getTooltipEventType(),d=null!==(t=e.props.active)&&void 0!==t?t:i,p=a.props.layout,h=e.key||"_recharts-cursor";return v.createElement(lT,{key:h,activeCoordinate:o,activePayload:s,activeTooltipIndex:l,chartName:n,element:e,isActive:d,layout:p,offset:c,tooltipAxisBandSize:u,tooltipEventType:f})})),AT(a,"renderPolarAxis",(function(t,n,r){var i=mo(t,"type.axisType"),o=mo(a.state,"".concat(i,"Map")),s=t.type.defaultProps,c=void 0!==s?PT(PT({},s),t.props):t.props,l=o&&o[c["".concat(i,"Id")]];return e.cloneElement(t,PT(PT({},l),{},{className:m(i,l.className),key:t.key||"".concat(n,"-").concat(r),ticks:Nj(l,!0)}))})),AT(a,"renderPolarGrid",(function(t){var n=t.props,r=n.radialLines,i=n.polarAngles,o=n.polarRadius,s=a.state,c=s.radiusAxisMap,l=s.angleAxisMap,u=ea(c),f=ea(l),d=f.cx,p=f.cy,h=f.innerRadius,y=f.outerRadius;return e.cloneElement(t,{polarAngles:Array.isArray(i)?i:Nj(f,!0).map((function(e){return e.coordinate})),polarRadius:Array.isArray(o)?o:Nj(u,!0).map((function(e){return e.coordinate})),cx:d,cy:p,innerRadius:h,outerRadius:y,key:t.key||"polar-grid",radialLines:r})})),AT(a,"renderLegend",(function(){var t=a.state.formattedGraphicalItems,n=a.props,r=n.children,i=n.width,o=n.height,s=a.props.margin||{},c=i-(s.left||0)-(s.right||0),u=mj({children:r,formattedGraphicalItems:t,legendWidth:c,legendContent:l});if(!u)return null;var f=u.item,d=yT(u,uT);return e.cloneElement(f,PT(PT({},d),{},{chartWidth:i,chartHeight:o,margin:s,onBBoxUpdate:a.handleLegendBBoxUpdate}))})),AT(a,"renderTooltip",(function(){var t,n=a.props,r=n.children,i=n.accessibilityLayer,o=wa(r,Eh);if(!o)return null;var s=a.state,c=s.isTooltipActive,l=s.activeCoordinate,u=s.activePayload,f=s.activeLabel,d=s.offset,p=null!==(t=o.props.active)&&void 0!==t?t:c;return e.cloneElement(o,{viewBox:PT(PT({},d),{},{x:d.left,y:d.top}),active:p,label:f,payload:p?u:[],coordinate:l,accessibilityLayer:i})})),AT(a,"renderBrush",(function(t){var n=a.props,r=n.margin,i=n.data,o=a.state,s=o.offset,c=o.dataStartIndex,l=o.dataEndIndex,u=o.updateId;return e.cloneElement(t,{key:t.key||"_recharts-brush",onChange:Tj(a.handleBrushChange,t.props.onChange),data:i,x:Go(t.props.x)?t.props.x:s.left,y:Go(t.props.y)?t.props.y:s.top+s.height+s.brushBottom-(r.bottom||0),width:Go(t.props.width)?t.props.width:s.width,startIndex:c,endIndex:l,updateId:"brush-".concat(u)})})),AT(a,"renderReferenceElement",(function(t,n,r){if(!t)return null;var i=a.clipPathId,o=a.state,s=o.xAxisMap,c=o.yAxisMap,l=o.offset,u=t.type.defaultProps||{},f=t.props,d=f.xAxisId,p=void 0===d?u.xAxisId:d,h=f.yAxisId,y=void 0===h?u.yAxisId:h;return e.cloneElement(t,{key:t.key||"".concat(n,"-").concat(r),xAxis:s[p],yAxis:c[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:i})})),AT(a,"renderActivePoints",(function(e){var n=e.item,r=e.activePoint,i=e.basePoint,o=e.childIndex,a=e.isRange,s=[],c=n.props.key,l=void 0!==n.item.type.defaultProps?PT(PT({},n.item.type.defaultProps),n.item.props):n.item.props,u=l.activeDot,f=PT(PT({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:Pj(n.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},Sa(u,!1)),la(u));return s.push(t.renderActiveDot(u,f,"".concat(c,"-activePoint-").concat(o))),i?s.push(t.renderActiveDot(u,PT(PT({},f),{},{cx:i.x,cy:i.y}),"".concat(c,"-basePoint-").concat(o))):a&&s.push(null),s})),AT(a,"renderGraphicChild",(function(t,n,r){var i=a.filterFormatItem(t,n,r);if(!i)return null;var o=a.getTooltipEventType(),s=a.state,c=s.isTooltipActive,l=s.tooltipAxis,u=s.activeTooltipIndex,f=s.activeLabel,d=wa(a.props.children,Eh),p=i.props,h=p.points,y=p.isRange,m=p.baseLine,v=void 0!==i.item.type.defaultProps?PT(PT({},i.item.type.defaultProps),i.item.props):i.item.props,g=v.activeDot,b=v.hide,x=v.activeBar,w=v.activeShape,j=Boolean(!b&&c&&d&&(g||x||w)),O={};"axis"!==o&&d&&"click"===d.props.trigger?O={onClick:Tj(a.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(O={onMouseLeave:Tj(a.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:Tj(a.handleItemMouseEnter,t.props.onMouseEnter)});var S=e.cloneElement(t,PT(PT({},i.props),O));if(j){if(!(u>=0)){var P,A=(null!==(P=a.getItemByXY(a.state.activeCoordinate))&&void 0!==P?P:{graphicalItem:S}).graphicalItem,k=A.item,_=void 0===k?t:k,E=A.childIndex,N=PT(PT(PT({},i.props),O),{},{activeIndex:E});return[e.cloneElement(_,N),null,null]}var M,T;if(l.dataKey&&!l.allowDuplicatedCategory){var C="function"==typeof l.dataKey?function(e){return"function"==typeof l.dataKey?l.dataKey(e.payload):null}:"payload.".concat(l.dataKey.toString());M=na(h,C,f),T=y&&m&&na(m,C,f)}else M=null==h?void 0:h[u],T=y&&m&&m[u];if(w||x){var D=void 0!==t.props.activeIndex?t.props.activeIndex:u;return[e.cloneElement(t,PT(PT(PT({},i.props),O),{},{activeIndex:D})),null,null]}if(!vo(M))return[S].concat(wT(a.renderActivePoints({item:i,activePoint:M,basePoint:T,childIndex:u,isRange:y})))}return y?[S,null,null]:[S,null]})),AT(a,"renderCustomized",(function(t,n,r){return e.cloneElement(t,PT(PT({key:"recharts-customized-".concat(r)},a.props),a.state))})),AT(a,"renderMap",{CartesianGrid:{handler:MT,once:!0},ReferenceArea:{handler:a.renderReferenceElement},ReferenceLine:{handler:MT},ReferenceDot:{handler:a.renderReferenceElement},XAxis:{handler:MT},YAxis:{handler:MT},Brush:{handler:a.renderBrush,once:!0},Bar:{handler:a.renderGraphicChild},Line:{handler:a.renderGraphicChild},Area:{handler:a.renderGraphicChild},Radar:{handler:a.renderGraphicChild},RadialBar:{handler:a.renderGraphicChild},Scatter:{handler:a.renderGraphicChild},Pie:{handler:a.renderGraphicChild},Funnel:{handler:a.renderGraphicChild},Tooltip:{handler:a.renderCursor,once:!0},PolarGrid:{handler:a.renderPolarGrid,once:!0},PolarAngleAxis:{handler:a.renderPolarAxis},PolarRadiusAxis:{handler:a.renderPolarAxis},Customized:{handler:a.renderCustomized}}),a.clipPathId="".concat(null!==(i=r.id)&&void 0!==i?i:Qo("recharts"),"-clip"),a.throttleTriggeredAfterMouseMove=Qh(a.triggeredAfterMouseMove,null!==(o=r.throttleDelay)&&void 0!==o?o:1e3/60),a.state={},a}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&xT(e,t)}(t,e.Component),mT(t,[{key:"componentDidMount",value:function(){var e,t;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(e=this.props.margin.left)&&void 0!==e?e:0,top:null!==(t=this.props.margin.top)&&void 0!==t?t:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var e=this.props,t=e.children,n=e.data,r=e.height,i=e.layout,o=wa(t,Eh);if(o){var a=o.props.defaultIndex;if(!("number"!=typeof a||a<0||a>this.state.tooltipTicks.length-1)){var s=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=DT(this.state,n,a,s),l=this.state.tooltipTicks[a].coordinate,u=(this.state.offset.top+r)/2,f="horizontal"===i?{x:l,y:u}:{y:l,x:u},d=this.state.formattedGraphicalItems.find((function(e){return"Scatter"===e.item.type.name}));d&&(f=PT(PT({},f),d.props.points[a].tooltipPosition),c=d.props.points[a].tooltipPayload);var p={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:s,activePayload:c,activeCoordinate:f};this.setState(p),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(e,t){if(!this.props.accessibilityLayer)return null;var n,r;(this.state.tooltipTicks!==t.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==e.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==e.margin)&&this.accessibilityManager.setDetails({offset:{left:null!==(n=this.props.margin.left)&&void 0!==n?n:0,top:null!==(r=this.props.margin.top)&&void 0!==r?r:0}});return null}},{key:"componentDidUpdate",value:function(e){Pa([wa(e.children,Eh)],[wa(this.props.children,Eh)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var e=wa(this.props.children,Eh);if(e&&"boolean"==typeof e.props.shared){var t=e.props.shared?"axis":"item";return s.indexOf(t)>=0?t:o}return o}},{key:"getMouseInfo",value:function(e){if(!this.container)return null;var t,n=this.container,r=n.getBoundingClientRect(),i={top:(t=r).top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(e.pageX-i.left),chartY:Math.round(e.pageY-i.top)},a=r.width/n.offsetWidth||1,s=this.inRange(o.chartX,o.chartY,a);if(!s)return null;var c=this.state,l=c.xAxisMap,u=c.yAxisMap;if("axis"!==this.getTooltipEventType()&&l&&u){var f=ea(l).scale,d=ea(u).scale,p=f&&f.invert?f.invert(o.chartX):null,h=d&&d.invert?d.invert(o.chartY):null;return PT(PT({},o),{},{xValue:p,yValue:h})}var y=IT(this.state,this.props.data,this.props.layout,s);return y?PT(PT({},o),y):null}},{key:"inRange",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=this.props.layout,i=e/n,o=t/n;if("horizontal"===r||"vertical"===r){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var s=this.state,c=s.angleAxisMap,l=s.radiusAxisMap;if(c&&l){var u=ea(c);return aO({x:i,y:o},u)}return null}},{key:"parseEventsOfWrapper",value:function(){var e=this.props.children,t=this.getTooltipEventType(),n=wa(e,Eh),r={};return n&&"axis"===t&&(r="click"===n.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd}),PT(PT({},la(this.props,this.handleOuterEvent)),r)}},{key:"addListener",value:function(){KM.on(ZM,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){KM.removeListener(ZM,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(e,t,n){for(var r=this.state.formattedGraphicalItems,i=0,o=r.length;i<o;i++){var a=r[i];if(a.item===e||a.props.key===e.key||t===ma(a.item.type)&&n===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var e=this.clipPathId,t=this.state.offset,n=t.left,r=t.top,i=t.height,o=t.width;return v.createElement("defs",null,v.createElement("clipPath",{id:e},v.createElement("rect",{x:n,y:r,height:i,width:o})))}},{key:"getXScales",value:function(){var e=this.state.xAxisMap;return e?Object.entries(e).reduce((function(e,t){var n=hT(t,2),r=n[0],i=n[1];return PT(PT({},e),{},AT({},r,i.scale))}),{}):null}},{key:"getYScales",value:function(){var e=this.state.yAxisMap;return e?Object.entries(e).reduce((function(e,t){var n=hT(t,2),r=n[0],i=n[1];return PT(PT({},e),{},AT({},r,i.scale))}),{}):null}},{key:"getXScaleByAxisId",value:function(e){var t;return null===(t=this.state.xAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getYScaleByAxisId",value:function(e){var t;return null===(t=this.state.yAxisMap)||void 0===t||null===(t=t[e])||void 0===t?void 0:t.scale}},{key:"getItemByXY",value:function(e){var t=this.state,n=t.formattedGraphicalItems,r=t.activeItem;if(n&&n.length)for(var i=0,o=n.length;i<o;i++){var a=n[i],s=a.props,c=a.item,l=void 0!==c.type.defaultProps?PT(PT({},c.type.defaultProps),c.props):c.props,u=ma(c.type);if("Bar"===u){var f=(s.data||[]).find((function(t){return NP(e,t)}));if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===u){var d=(s.data||[]).find((function(t){return aO(e,t)}));if(d)return{graphicalItem:a,payload:d}}else if(yk(a,r)||mk(a,r)||vk(a,r)){var p=wk({graphicalItem:a,activeTooltipItem:r,itemData:l.data}),h=void 0===l.activeIndex?p:l.activeIndex;return{graphicalItem:PT(PT({},a),{},{childIndex:h}),payload:vk(a,r)?l.data[p]:a.props.data[p]}}}return null}},{key:"render",value:function(){var e=this;if(!ja(this))return null;var t,n,r=this.props,i=r.children,o=r.className,a=r.width,s=r.height,c=r.style,l=r.compact,u=r.title,f=r.desc,d=yT(r,fT),p=Sa(d,!1);if(l)return v.createElement(EE,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},v.createElement(Ma,pT({},p,{width:a,height:s,title:u,desc:f}),this.renderClipPath(),ka(i,this.renderMap)));this.props.accessibilityLayer&&(p.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,p.role=null!==(n=this.props.role)&&void 0!==n?n:"application",p.onKeyDown=function(t){e.accessibilityManager.keyboardEvent(t)},p.onFocus=function(){e.accessibilityManager.focus()});var h=this.parseEventsOfWrapper();return v.createElement(EE,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},v.createElement("div",pT({className:m("recharts-wrapper",o),style:PT({position:"relative",cursor:"default",width:a,height:s},c)},h,{ref:function(t){e.container=t}}),v.createElement(Ma,pT({},p,{width:a,height:s,title:u,desc:f,style:ET}),this.renderClipPath(),ka(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}();return AT(h,"displayName",n),AT(h,"defaultProps",PT({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},f)),AT(h,"getDerivedStateFromProps",(function(e,t){var n=e.dataKey,r=e.data,i=e.children,o=e.width,a=e.height,s=e.layout,c=e.stackOffset,l=e.margin,u=t.dataStartIndex,f=t.dataEndIndex;if(void 0===t.updateId){var d=LT(e);return PT(PT(PT({},d),{},{updateId:0},p(PT(PT({props:e},d),{},{updateId:0}),t)),{},{prevDataKey:n,prevData:r,prevWidth:o,prevHeight:a,prevLayout:s,prevStackOffset:c,prevMargin:l,prevChildren:i})}if(n!==t.prevDataKey||r!==t.prevData||o!==t.prevWidth||a!==t.prevHeight||s!==t.prevLayout||c!==t.prevStackOffset||!ra(l,t.prevMargin)){var h=LT(e),y={chartX:t.chartX,chartY:t.chartY,isTooltipActive:t.isTooltipActive},m=PT(PT({},IT(t,r,s)),{},{updateId:t.updateId+1}),v=PT(PT(PT({},h),y),m);return PT(PT(PT({},v),p(PT({props:e},v),t)),{},{prevDataKey:n,prevData:r,prevWidth:o,prevHeight:a,prevLayout:s,prevStackOffset:c,prevMargin:l,prevChildren:i})}if(!Pa(i,t.prevChildren)){var g,b,x,w,j=wa(i,a_),O=j&&null!==(g=null===(b=j.props)||void 0===b?void 0:b.startIndex)&&void 0!==g?g:u,S=j&&null!==(x=null===(w=j.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,P=O!==u||S!==f,A=!vo(r)&&!P?t.updateId:t.updateId+1;return PT(PT({updateId:A},p(PT(PT({props:e},t),{},{updateId:A,dataStartIndex:O,dataEndIndex:S}),t)),{},{prevChildren:i,dataStartIndex:O,dataEndIndex:S})}return null})),AT(h,"renderActiveDot",(function(t,n,r){var i;return i=e.isValidElement(t)?e.cloneElement(t,n):jr(t)?t(n):v.createElement(UP,n),v.createElement(Ia,{className:"recharts-active-dot",key:r},i)})),function(e){return v.createElement(h,e)}},WT=UT({chartName:"LineChart",GraphicalChild:OM,axisComponents:[{axisType:"xAxis",AxisComp:DM},{axisType:"yAxis",AxisComp:HM}],formatAxisMap:aE}),qT=UT({chartName:"BarChart",GraphicalChild:J_,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:DM},{axisType:"yAxis",AxisComp:HM}],formatAxisMap:aE}),HT=UT({chartName:"PieChart",GraphicalChild:Ck,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:IA},{axisType:"radiusAxis",AxisComp:wA}],formatAxisMap:function(e,t,n,r,i){var o=e.width,a=e.height,s=e.startAngle,c=e.endAngle,l=Jo(e.cx,o,o/2),u=Jo(e.cy,a,a/2),f=rO(o,a,n),d=Jo(e.innerRadius,f,0),p=Jo(e.outerRadius,f,.8*f);return Object.keys(t).reduce((function(e,n){var o,a=t[n],f=a.domain,h=a.reversed;if(vo(a.range))"angleAxis"===r?o=[s,c]:"radiusAxis"===r&&(o=[d,p]),h&&(o=[o[1],o[0]]);else{var y=Qj(o=a.range,2);s=y[0],c=y[1]}var m=Cj(a,i),v=m.realScaleType,g=m.scale;g.domain(f).range(o),Ij(g);var b=Lj(g,Kj(Kj({},a),{},{realScaleType:v})),x=Kj(Kj(Kj({},a),b),{},{range:o,radius:p,realScaleType:v,scale:g,cx:l,cy:u,innerRadius:d,outerRadius:p,startAngle:s,endAngle:c});return Kj(Kj({},e),{},Zj({},n,x))}),{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});const VT=()=>{const[n,r]=e.useState("7d"),i={totalSubmissions:1250,completionRate:78.5,avgRiskScore:65.2,leadConversionRate:12.8,submissionsByRisk:[{name:"High Risk",value:320,color:"#ef4444"},{name:"Medium Risk",value:580,color:"#f59e0b"},{name:"Low Risk",value:350,color:"#10b981"}],submissionsTrend:[{date:"2024-01-01",submissions:45,completions:35},{date:"2024-01-02",submissions:52,completions:41},{date:"2024-01-03",submissions:38,completions:30},{date:"2024-01-04",submissions:61,completions:48},{date:"2024-01-05",submissions:44,completions:35},{date:"2024-01-06",submissions:55,completions:43},{date:"2024-01-07",submissions:48,completions:37}],industryBreakdown:[{industry:"Technology",count:285,avgRisk:58.3},{industry:"Healthcare",count:195,avgRisk:72.1},{industry:"Finance",count:160,avgRisk:45.8},{industry:"Retail",count:140,avgRisk:68.9},{industry:"Manufacturing",count:125,avgRisk:61.4},{industry:"Other",count:345,avgRisk:64.7}],emailMetrics:{totalSent:1180,openRate:24.5,responseRate:8.2,pendingEmails:15}};return t.jsxs("div",{className:"space-y-6 p-6",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("h2",{className:"text-2xl font-bold",children:"Analytics Dashboard"}),t.jsxs("div",{className:"flex gap-4",children:[t.jsxs(zt,{value:n,onValueChange:r,children:[t.jsx($t,{className:"w-32",children:t.jsx(Ft,{})}),t.jsxs(qt,{children:[t.jsx(Ht,{value:"7d",children:"Last 7 days"}),t.jsx(Ht,{value:"30d",children:"Last 30 days"}),t.jsx(Ht,{value:"90d",children:"Last 90 days"}),t.jsx(Ht,{value:"1y",children:"Last year"})]})]}),t.jsxs(U,{variant:"outline",size:"sm",children:[t.jsx(g,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Total Submissions"}),t.jsx(a,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsx("div",{className:"text-2xl font-bold",children:i.totalSubmissions.toLocaleString()}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"+12% from last period"})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Completion Rate"}),t.jsx(d,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[i.completionRate,"%"]}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"+2.5% from last period"})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Avg Risk Score"}),t.jsx(b,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsx("div",{className:"text-2xl font-bold",children:i.avgRiskScore}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"-3.2% from last period"})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Lead Conversion"}),t.jsx(l,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[i.leadConversionRate,"%"]}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"+1.8% from last period"})]})]})]}),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t.jsxs(L,{children:[t.jsx(z,{children:t.jsx(F,{children:"Submissions Trend"})}),t.jsx($,{children:t.jsx(oy,{width:"100%",height:300,children:t.jsxs(WT,{data:i.submissionsTrend,children:[t.jsx(aM,{strokeDasharray:"3 3"}),t.jsx(DM,{dataKey:"date"}),t.jsx(HM,{}),t.jsx(Eh,{}),t.jsx(OM,{type:"monotone",dataKey:"submissions",stroke:"#3b82f6",strokeWidth:2}),t.jsx(OM,{type:"monotone",dataKey:"completions",stroke:"#10b981",strokeWidth:2})]})})})]}),t.jsxs(L,{children:[t.jsx(z,{children:t.jsx(F,{children:"Risk Level Distribution"})}),t.jsx($,{children:t.jsx(oy,{width:"100%",height:300,children:t.jsxs(HT,{children:[t.jsx(Ck,{data:i.submissionsByRisk,cx:"50%",cy:"50%",labelLine:!1,label:({name:e,percent:t})=>`${e}: ${(100*t).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"value",children:i.submissionsByRisk.map(((e,n)=>t.jsx(ay,{fill:e.color},`cell-${n}`)))}),t.jsx(Eh,{})]})})})]})]}),t.jsxs(L,{children:[t.jsx(z,{children:t.jsx(F,{children:"Industry Breakdown"})}),t.jsx($,{children:t.jsx(oy,{width:"100%",height:400,children:t.jsxs(qT,{data:i.industryBreakdown,children:[t.jsx(aM,{strokeDasharray:"3 3"}),t.jsx(DM,{dataKey:"industry"}),t.jsx(HM,{yAxisId:"left"}),t.jsx(HM,{yAxisId:"right",orientation:"right"}),t.jsx(Eh,{}),t.jsx(J_,{yAxisId:"left",dataKey:"count",fill:"#3b82f6"}),t.jsx(J_,{yAxisId:"right",dataKey:"avgRisk",fill:"#f59e0b"})]})})})]}),t.jsxs(L,{children:[t.jsx(z,{children:t.jsx(F,{children:"Email Campaign Metrics"})}),t.jsx($,{children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-blue-600",children:i.emailMetrics.totalSent}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Sent"})]}),t.jsxs("div",{className:"text-center",children:[t.jsxs("div",{className:"text-2xl font-bold text-green-600",children:[i.emailMetrics.openRate,"%"]}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Open Rate"})]}),t.jsxs("div",{className:"text-center",children:[t.jsxs("div",{className:"text-2xl font-bold text-orange-600",children:[i.emailMetrics.responseRate,"%"]}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Response Rate"})]}),t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"text-2xl font-bold text-red-600",children:i.emailMetrics.pendingEmails}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Pending"})]})]})})]})]})},YT=()=>{const{tests:n,loading:r,error:i,getTestResults:o,createTest:a,refreshData:s}=(()=>{const[t,n]=e.useState([]),[r,i]=e.useState([]),[o,a]=e.useState([]),[s,c]=e.useState(!0),[l,u]=e.useState(null);e.useEffect((()=>{f()}),[]);const f=async()=>{try{c(!0),u(null),n([]),i([]),a([])}catch(e){u("Failed to load A/B testing data"),n([]),i([]),a([])}finally{c(!1)}};return{tests:t,variants:r,participations:o,loading:s,error:l,getTestResults:e=>{const t=o.filter((t=>t.test_id===e)),n=r.filter((t=>t.test_id===e));return n.map((e=>{const n=t.filter((t=>t.variant_id===e.id)),r=n.filter((e=>e.converted)).length,i=n.length>0?r/n.length*100:0;return{variant:e,participations:n.length,conversions:r,conversionRate:Math.round(100*i)/100}}))},createTest:async e=>{try{return null}catch(t){throw t}},participateInTest:async(e,t,n)=>{try{return null}catch(r){throw r}},recordConversion:async(e,t)=>{try{return null}catch(n){throw n}},refreshData:f}})(),[c,l]=e.useState(!1),[u,f]=e.useState({name:"",description:"",test_type:"cta_button",hypothesis:"",success_metric:"conversion_rate"}),d=()=>n.filter((e=>e.is_active));return r?t.jsx("div",{className:"flex items-center justify-center p-8",children:t.jsx("div",{className:"text-muted-foreground",children:"Loading A/B tests..."})}):i?t.jsx("div",{className:"flex items-center justify-center p-8",children:t.jsxs("div",{className:"text-red-600",children:["Error: ",i]})}):t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("h2",{className:"text-2xl font-bold",children:"A/B Testing Dashboard"}),t.jsxs(U,{onClick:()=>l(!0),children:[t.jsx(x,{className:"h-4 w-4 mr-2"}),"Create New Test"]})]}),c&&t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{children:"Create New A/B Test"}),t.jsx(H,{children:"Set up a new A/B test to measure conversion improvements"})]}),t.jsxs($,{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx(je,{htmlFor:"test-name",children:"Test Name"}),t.jsx(Z,{id:"test-name",value:u.name,onChange:e=>f((t=>({...t,name:e.target.value}))),placeholder:"e.g., Homepage CTA Button Test"})]}),t.jsxs("div",{children:[t.jsx(je,{htmlFor:"test-description",children:"Description"}),t.jsx(Oe,{id:"test-description",value:u.description,onChange:e=>f((t=>({...t,description:e.target.value}))),placeholder:"Brief description of what you're testing"})]}),t.jsxs("div",{children:[t.jsx(je,{htmlFor:"test-type",children:"Test Type"}),t.jsxs(zt,{value:u.test_type,onValueChange:e=>f((t=>({...t,test_type:e}))),children:[t.jsx($t,{children:t.jsx(Ft,{placeholder:"Select test type"})}),t.jsxs(qt,{children:[t.jsx(Ht,{value:"cta_button",children:"CTA Button"}),t.jsx(Ht,{value:"headline",children:"Headline"}),t.jsx(Ht,{value:"layout",children:"Page Layout"}),t.jsx(Ht,{value:"form",children:"Form Design"})]})]})]}),t.jsxs("div",{children:[t.jsx(je,{htmlFor:"hypothesis",children:"Hypothesis"}),t.jsx(Oe,{id:"hypothesis",value:u.hypothesis,onChange:e=>f((t=>({...t,hypothesis:e.target.value}))),placeholder:"What do you expect to happen and why?"})]}),t.jsxs("div",{className:"flex justify-end space-x-2",children:[t.jsx(U,{variant:"outline",onClick:()=>l(!1),children:"Cancel"}),t.jsx(U,{onClick:async()=>{try{await a({...u,is_active:!0,start_date:(new Date).toISOString()}),l(!1),f({name:"",description:"",test_type:"cta_button",hypothesis:"",success_metric:"conversion_rate"}),s()}catch(e){}},children:"Create Test"})]})]})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:0===n.length?t.jsx(L,{className:"col-span-full",children:t.jsxs($,{className:"p-8 text-center",children:[t.jsx(x,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No A/B Tests Yet"}),t.jsx("p",{className:"text-muted-foreground mb-4",children:"Create your first A/B test to start measuring conversion improvements."}),t.jsx(U,{onClick:()=>l(!0),children:"Create Your First Test"})]})}):n.map((e=>{const n=o(e.id);return t.jsxs(L,{children:[t.jsx(z,{children:t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{children:[t.jsx(F,{className:"text-lg",children:e.name}),t.jsx(H,{children:e.description})]}),t.jsx(W,{variant:e.is_active?"default":"secondary",children:e.is_active?"Active":"Inactive"})]})}),t.jsx($,{children:t.jsxs("div",{className:"space-y-3",children:[t.jsxs("div",{children:[t.jsx("div",{className:"text-sm font-medium",children:"Test Type"}),t.jsx("div",{className:"text-sm text-muted-foreground capitalize",children:e.test_type.replace("_"," ")})]}),t.jsxs("div",{children:[t.jsx("div",{className:"text-sm font-medium",children:"Success Metric"}),t.jsx("div",{className:"text-sm text-muted-foreground",children:e.success_metric})]}),n.length>0&&t.jsxs("div",{children:[t.jsx("div",{className:"text-sm font-medium mb-2",children:"Results"}),n.map(((e,n)=>t.jsxs("div",{className:"text-xs space-y-1",children:[t.jsxs("div",{className:"flex justify-between",children:[t.jsx("span",{children:e.variant.name}),t.jsxs("span",{children:[e.conversionRate,"%"]})]}),t.jsxs("div",{className:"text-muted-foreground",children:[e.participations," participants, ",e.conversions," conversions"]})]},n)))]}),t.jsxs("div",{className:"flex justify-between items-center pt-2",children:[t.jsxs(U,{variant:"outline",size:"sm",children:[t.jsx(w,{className:"h-3 w-3 mr-1"}),"View Details"]}),t.jsx(U,{variant:"outline",size:"sm",children:e.is_active?t.jsxs(t.Fragment,{children:[t.jsx(j,{className:"h-3 w-3 mr-1"}),"Pause"]}):t.jsxs(t.Fragment,{children:[t.jsx(O,{className:"h-3 w-3 mr-1"}),"Resume"]})})]})]})})]},e.id)}))}),d().length>0&&t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{children:"Active Tests Summary"}),t.jsxs(H,{children:["Currently running ",d().length," active test",1!==d().length?"s":""]})]}),t.jsx($,{children:t.jsxs("div",{className:"text-center py-4",children:[t.jsx("div",{className:"text-2xl font-bold text-blue-600",children:d().length}),t.jsx("div",{className:"text-sm text-muted-foreground",children:"Active A/B Tests"})]})})]})]})},XT=()=>{const{radarData:e,loading:n,error:r}=I();if(n)return t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[...Array(4)].map(((e,n)=>t.jsxs(L,{className:"animate-pulse",children:[t.jsx(z,{className:"pb-2",children:t.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),t.jsxs($,{children:[t.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),t.jsx("div",{className:"h-3 bg-gray-200 rounded w-full"})]})]},n)))});if(r)return t.jsx(L,{className:"border-red-200 bg-red-50",children:t.jsx($,{className:"pt-6",children:t.jsxs("div",{className:"flex items-center gap-2 text-red-600",children:[t.jsx(s,{className:"h-5 w-5"}),t.jsx("span",{children:"Failed to load threat intelligence data"})]})})});if(!e)return null;const i=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),o=e=>e>0?t.jsx(d,{className:"h-4 w-4 text-red-500"}):e<0?t.jsx(A,{className:"h-4 w-4 text-green-500"}):null,a=e=>e>0?"text-red-600":e<0?"text-green-600":"text-gray-600";return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Global Phishing"}),t.jsx(l,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsx("div",{className:"text-2xl font-bold",children:i(e.phishing.total)}),t.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o(e.phishing.trend),t.jsxs("span",{className:a(e.phishing.trend),children:[Math.abs(e.phishing.trend),"% this week"]})]})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Spoofing Attempts"}),t.jsx(S,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsx("div",{className:"text-2xl font-bold",children:i(e.spoofing.total)}),t.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o(e.spoofing.trend),t.jsxs("span",{className:a(e.spoofing.trend),children:[Math.abs(e.spoofing.trend),"% this week"]})]})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"DMARC Adoption"}),t.jsx(P,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[e.dmarc.adoptionRate,"%"]}),t.jsxs("div",{className:"flex items-center gap-1 text-xs text-muted-foreground",children:[o(e.dmarc.trend),t.jsxs("span",{className:a(e.dmarc.trend),children:[Math.abs(e.dmarc.trend),"% this week"]})]})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"DMARC Compliance"}),t.jsx(S,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[e.dmarc.compliance,"%"]}),t.jsx("div",{className:"text-xs text-muted-foreground",children:"Strict policy enforcement"})]})]})]}),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{className:"text-lg",children:"Top Phishing Targets"}),t.jsx(H,{children:"Most targeted industries this week"})]}),t.jsx($,{children:t.jsx("div",{className:"space-y-3",children:e.phishing.topTargets.map(((e,n)=>t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm font-medium",children:e}),t.jsxs(W,{variant:0===n?"destructive":1===n?"secondary":"outline",children:["#",n+1]})]},e)))})})]}),t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{className:"text-lg",children:"Industry Risk Levels"}),t.jsx(H,{children:"Risk scoring by industry sector"})]}),t.jsx($,{children:t.jsx("div",{className:"space-y-3",children:Object.entries(e.industryRisks).sort((([,e],[,t])=>t-e)).slice(0,6).map((([e,n])=>t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("span",{className:"text-sm font-medium",children:e}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"w-20 h-2 bg-gray-200 rounded-full overflow-hidden",children:t.jsx("div",{className:"h-full rounded-full "+(n>=8?"bg-red-500":n>=6?"bg-orange-500":"bg-yellow-500"),style:{width:n/10*100+"%"}})}),t.jsxs("span",{className:"text-sm text-muted-foreground w-8",children:[n,"/10"]})]})]},e)))})})]})]}),t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{className:"text-lg",children:"Common Spoofing Methods"}),t.jsx(H,{children:"Most prevalent spoofing techniques detected globally"})]}),t.jsx($,{children:t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:e.spoofing.topMethods.map(((e,n)=>t.jsxs("div",{className:"text-center p-3 border rounded-lg",children:[t.jsx("div",{className:"text-sm font-medium",children:e}),t.jsxs(W,{variant:"outline",className:"mt-1",children:["Top ",n+1]})]},e)))})})]}),t.jsxs("div",{className:"text-xs text-muted-foreground text-center",children:["Last updated: ",new Date(e.lastUpdated).toLocaleString()]})]})},GT=()=>{const{radarData:e,loading:n,error:r}=I();if(n)return t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[...Array(4)].map(((e,n)=>t.jsxs(L,{className:"animate-pulse",children:[t.jsx(z,{className:"pb-2",children:t.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),t.jsxs($,{children:[t.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),t.jsx("div",{className:"h-3 bg-gray-200 rounded w-full"})]})]},n)))});if(r||!e?.emailSecurity)return t.jsx(L,{className:"border-orange-200 bg-orange-50",children:t.jsx($,{className:"pt-6",children:t.jsxs("div",{className:"flex items-center gap-2 text-orange-600",children:[t.jsx(s,{className:"h-5 w-5"}),t.jsx("span",{children:"Email security data temporarily unavailable"})]})})});const{emailSecurity:i}=e,o=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),a=e=>e>=80?"text-green-600":e>=60?"text-yellow-600":"text-red-600",c=e=>e<=5?"text-green-600":e<=15?"text-yellow-600":"text-red-600";return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx(l,{className:"h-5 w-5 text-blue-600"}),t.jsx("h3",{className:"text-lg font-semibold",children:"Email Security Analytics"}),t.jsx(W,{variant:"outline",className:"text-xs",children:"Enhanced"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Malicious Detection"}),t.jsx(S,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[i.malicious.detectionRate.toFixed(1),"%"]}),t.jsxs("div",{className:"text-xs text-muted-foreground",children:[o(i.malicious.detected)," detected of"," ",o(i.malicious.detected+i.malicious.clean)," total"]}),t.jsx("div",{className:"mt-2",children:t.jsx(W,{variant:i.malicious.detectionRate<=5?"default":"destructive",className:"text-xs",children:i.malicious.detectionRate<=5?"Low threat":"High threat"})})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"DKIM Authentication"}),t.jsx(k,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[i.dkim.adoptionRate.toFixed(1),"%"]}),t.jsxs("div",{className:"text-xs text-muted-foreground",children:[o(i.dkim.pass)," pass / ",o(i.dkim.fail)," fail"]}),t.jsx("div",{className:"mt-2",children:t.jsx(W,{variant:i.dkim.adoptionRate>=80?"default":"secondary",className:"text-xs",children:i.dkim.adoptionRate>=80?"Good adoption":"Needs improvement"})})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"SPF Validation"}),t.jsx(k,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[i.spf.adoptionRate.toFixed(1),"%"]}),t.jsxs("div",{className:"text-xs text-muted-foreground",children:[o(i.spf.pass)," pass / ",o(i.spf.fail)," fail"]}),t.jsx("div",{className:"mt-2",children:t.jsx(W,{variant:i.spf.adoptionRate>=80?"default":"secondary",className:"text-xs",children:i.spf.adoptionRate>=80?"Good adoption":"Needs improvement"})})]})]}),t.jsxs(L,{children:[t.jsxs(z,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[t.jsx(F,{className:"text-sm font-medium",children:"Spam Detection"}),t.jsx(_,{className:"h-4 w-4 text-muted-foreground"})]}),t.jsxs($,{children:[t.jsxs("div",{className:"text-2xl font-bold",children:[i.spam.detectionRate.toFixed(1),"%"]}),t.jsxs("div",{className:"text-xs text-muted-foreground",children:[o(i.spam.detected)," spam of"," ",o(i.spam.detected+i.spam.clean)," total"]}),t.jsx("div",{className:"mt-2",children:t.jsx(W,{variant:i.spam.detectionRate<=10?"default":"destructive",className:"text-xs",children:i.spam.detectionRate<=10?"Low spam":"High spam"})})]})]})]}),t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{className:"text-lg",children:"Email Security Summary"}),t.jsx(H,{children:"Global email authentication and threat detection metrics"})]}),t.jsx($,{children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsxs("div",{children:[t.jsx("h4",{className:"font-semibold mb-3",children:"Authentication Protocols"}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm",children:"DKIM Adoption"}),t.jsxs("span",{className:`text-sm font-medium ${a(i.dkim.adoptionRate)}`,children:[i.dkim.adoptionRate.toFixed(1),"%"]})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm",children:"SPF Adoption"}),t.jsxs("span",{className:`text-sm font-medium ${a(i.spf.adoptionRate)}`,children:[i.spf.adoptionRate.toFixed(1),"%"]})]})]})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"font-semibold mb-3",children:"Threat Detection"}),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm",children:"Malicious Rate"}),t.jsxs("span",{className:`text-sm font-medium ${c(i.malicious.detectionRate)}`,children:[i.malicious.detectionRate.toFixed(1),"%"]})]}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("span",{className:"text-sm",children:"Spam Rate"}),t.jsxs("span",{className:`text-sm font-medium ${c(i.spam.detectionRate)}`,children:[i.spam.detectionRate.toFixed(1),"%"]})]})]})]})]})})]})]})},KT=()=>{const{radarData:e,loading:n,refetch:r}=I();return t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsxs("div",{children:[t.jsxs("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[t.jsx(P,{className:"h-6 w-6"}),"Global Threat Intelligence"]}),t.jsx("p",{className:"text-gray-600 mt-1",children:"Real-time cybersecurity insights powered by Cloudflare Radar"})]}),t.jsxs("div",{className:"flex gap-2",children:[t.jsxs(U,{onClick:()=>{r()},disabled:n,variant:"outline",size:"sm",children:[t.jsx(E,{className:"h-4 w-4 mr-2 "+(n?"animate-spin":"")}),"Refresh"]}),t.jsxs(U,{onClick:()=>{if(!e)return;const t={generated:(new Date).toISOString(),summary:{totalPhishingAttacks:e.phishing.total,totalSpoofingAttempts:e.spoofing.total,dmarcAdoptionRate:e.dmarc.adoptionRate,industryRisks:e.industryRisks,emailSecurity:e.emailSecurity?{maliciousDetectionRate:e.emailSecurity.malicious.detectionRate,dkimAdoptionRate:e.emailSecurity.dkim.adoptionRate,spfAdoptionRate:e.emailSecurity.spf.adoptionRate,spamDetectionRate:e.emailSecurity.spam.detectionRate}:null},recommendations:["Implement DMARC with strict policy to reduce spoofing attempts","Conduct regular phishing awareness training for employees","Monitor industry-specific threat trends for proactive defense","Consider implementing additional email security measures",...e.emailSecurity?[`Improve DKIM adoption (currently ${e.emailSecurity.dkim.adoptionRate.toFixed(1)}%)`,`Enhance SPF validation (currently ${e.emailSecurity.spf.adoptionRate.toFixed(1)}%)`,"Monitor malicious email detection trends for emerging threats"]:[]]},n=new Blob([JSON.stringify(t,null,2)],{type:"application/json"}),r=URL.createObjectURL(n),i=document.createElement("a");i.href=r,i.download=`threat-intelligence-report-${(new Date).toISOString().split("T")[0]}.json`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(r)},disabled:!e,variant:"outline",size:"sm",children:[t.jsx(N,{className:"h-4 w-4 mr-2"}),"Export Report"]})]})]}),t.jsx(XT,{}),t.jsx(GT,{}),t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{children:"Threat Intelligence Insights"}),t.jsx(H,{children:"How this data enhances your security assessments"})]}),t.jsx($,{children:t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[t.jsxs("div",{children:[t.jsx("h4",{className:"font-semibold mb-2",children:"Assessment Enhancement"}),t.jsxs("ul",{className:"text-sm space-y-1 text-gray-600",children:[t.jsx("li",{children:"• Real-time threat context for risk scoring"}),t.jsx("li",{children:"• Industry-specific risk benchmarking"}),t.jsx("li",{children:"• Current attack trend awareness"}),t.jsx("li",{children:"• DMARC compliance comparison"}),t.jsx("li",{children:"• Email authentication protocol analysis"}),t.jsx("li",{children:"• Malicious email detection insights"})]})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"font-semibold mb-2",children:"Client Value"}),t.jsxs("ul",{className:"text-sm space-y-1 text-gray-600",children:[t.jsx("li",{children:"• Demonstrate current threat landscape"}),t.jsx("li",{children:"• Provide industry-relative risk positioning"}),t.jsx("li",{children:"• Support recommendations with live data"}),t.jsx("li",{children:"• Show urgency of security measures"}),t.jsx("li",{children:"• Validate email security implementations"}),t.jsx("li",{children:"• Benchmark authentication protocol adoption"})]})]})]})})]})]})},ZT=()=>{const[n,r]=e.useState([]),[i,o]=e.useState(null),[a,s]=e.useState(!1),[c,u]=e.useState(!0),{toast:f}=B(),d=async()=>{try{const{data:e,error:t}=await D.from("email_queue").select("*").order("created_at",{ascending:!1}).limit(50);if(t)throw t;r(e||[]);const{data:n,error:i}=await D.rpc("get_email_queue_status");i||n&&n.length>0&&o(n[0])}catch(e){f({title:"Error",description:"Failed to fetch email queue data",variant:"destructive"})}finally{u(!1)}};e.useEffect((()=>{d();const e=setInterval(d,3e4);return()=>clearInterval(e)}),[]);const p=(e,n)=>{switch(e){case"pending":return t.jsxs(W,{variant:"secondary",className:"flex items-center gap-1",children:[t.jsx(M,{className:"h-3 w-3"}),"Pending"]});case"sent":return t.jsxs(W,{variant:"default",className:"flex items-center gap-1 bg-green-600",children:[t.jsx(k,{className:"h-3 w-3"}),"Sent"]});case"failed":return t.jsxs(W,{variant:"destructive",className:"flex items-center gap-1",children:[t.jsx(_,{className:"h-3 w-3"}),"Failed (",n,"/3)"]});default:return t.jsx(W,{variant:"outline",children:e})}},h=e=>new Date(e).toLocaleString();return c?t.jsx(L,{children:t.jsxs($,{className:"flex items-center justify-center py-8",children:[t.jsx(E,{className:"h-6 w-6 animate-spin mr-2"}),"Loading email automation data..."]})}):t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:"Email Automation"}),t.jsx("p",{className:"text-muted-foreground",children:"Monitor and manage automated email campaigns"})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsxs(U,{onClick:d,variant:"outline",size:"sm",disabled:c,children:[t.jsx(E,{className:"h-4 w-4 mr-2 "+(c?"animate-spin":"")}),"Refresh"]}),t.jsxs(U,{onClick:async()=>{s(!0);try{const{data:e,error:t}=await D.functions.invoke("process-email-queue",{body:{manual:!0}});if(t)throw t;f({title:"Email Queue Processed",description:`Processed ${e.results?.processed||0} emails. ${e.results?.successful||0} successful, ${e.results?.failed||0} failed.`}),await d()}catch(e){f({title:"Processing Failed",description:e.message||"Failed to process email queue",variant:"destructive"})}finally{s(!1)}},disabled:a,size:"sm",children:[a?t.jsx(E,{className:"h-4 w-4 mr-2 animate-spin"}):t.jsx(O,{className:"h-4 w-4 mr-2"}),"Process Queue"]})]})]}),i&&t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t.jsx(L,{children:t.jsxs($,{className:"flex items-center p-6",children:[t.jsx(l,{className:"h-8 w-8 text-blue-600 mr-3"}),t.jsxs("div",{children:[t.jsx("p",{className:"text-2xl font-bold",children:i.total_queued}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Total Queued"})]})]})}),t.jsx(L,{children:t.jsxs($,{className:"flex items-center p-6",children:[t.jsx(M,{className:"h-8 w-8 text-yellow-600 mr-3"}),t.jsxs("div",{children:[t.jsx("p",{className:"text-2xl font-bold",children:i.pending}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Pending"})]})]})}),t.jsx(L,{children:t.jsxs($,{className:"flex items-center p-6",children:[t.jsx(k,{className:"h-8 w-8 text-green-600 mr-3"}),t.jsxs("div",{children:[t.jsx("p",{className:"text-2xl font-bold",children:i.sent}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Sent"})]})]})}),t.jsx(L,{children:t.jsxs($,{className:"flex items-center p-6",children:[t.jsx(_,{className:"h-8 w-8 text-red-600 mr-3"}),t.jsxs("div",{children:[t.jsx("p",{className:"text-2xl font-bold",children:i.failed}),t.jsx("p",{className:"text-xs text-muted-foreground",children:"Failed"})]})]})})]}),t.jsxs(L,{children:[t.jsxs(z,{children:[t.jsx(F,{children:"Email Queue"}),t.jsx(H,{children:"Recent emails in the automation queue"})]}),t.jsx($,{children:0===n.length?t.jsx("div",{className:"text-center py-8 text-muted-foreground",children:"No emails in queue"}):t.jsx("div",{className:"space-y-4",children:n.map((e=>t.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[t.jsx("h4",{className:"font-medium",children:e.recipient_email}),p(e.status,e.attempts)]}),t.jsxs("p",{className:"text-sm text-muted-foreground",children:[e.company_name," • ",e.email_type," • Scheduled: ",h(e.scheduled_at)]}),e.error_message&&t.jsxs("p",{className:"text-sm text-red-600 mt-1",children:["Error: ",e.error_message]})]}),t.jsxs("div",{className:"flex items-center gap-2",children:["pending"===e.status&&t.jsxs(U,{onClick:()=>(async e=>{try{const{data:t,error:n}=await D.functions.invoke("send-lead-email",{body:{emailQueueId:e}});if(n)throw n;f({title:"Test Email Sent",description:"Email delivery test completed successfully"}),await d()}catch(t){f({title:"Test Failed",description:t.message||"Failed to send test email",variant:"destructive"})}})(e.id),variant:"outline",size:"sm",children:[t.jsx(l,{className:"h-4 w-4 mr-1"}),"Test Send"]}),t.jsx("div",{className:"text-xs text-muted-foreground text-right",children:e.sent_at?t.jsxs("div",{children:["Sent: ",h(e.sent_at)]}):t.jsxs("div",{children:["Attempts: ",e.attempts,"/3"]})})]})]},e.id)))})})]})]})},QT=()=>{const[n,r]=e.useState(!1),{toast:i}=B();return t.jsxs(L,{children:[t.jsx(z,{children:t.jsx(F,{children:"Data Population Utility"})}),t.jsxs($,{className:"space-y-4",children:[t.jsx("p",{className:"text-sm text-muted-foreground",children:"This utility helps you populate the database with sample data for testing purposes."}),t.jsxs("div",{className:"flex gap-4",children:[t.jsx(U,{onClick:async()=>{r(!0);try{const{data:e}=await D.from("assessment_types").select("*").limit(1);if(e&&e.length>0)return void i({title:"Data Already Exists",description:"Sample data has already been populated.",variant:"default"});const t=await(async()=>{const{data:e,error:t}=await D.from("assessment_types").insert({name:"Cybersecurity Risk Assessment",title:"Comprehensive Cybersecurity Risk Assessment",description:"Evaluate your organization's cybersecurity posture and identify potential vulnerabilities.",category:"Security",slug:"cybersecurity-risk-assessment",estimated_time_minutes:15,is_active:!0,order_index:1}).select().single();if(t)throw t;return e.id})(),n=await(async e=>{const t=[{assessment_type_id:e,question_text:"Does your organization have a formal cybersecurity policy?",category:"Policy & Governance",order_index:1,is_active:!0},{assessment_type_id:e,question_text:"Do you use multi-factor authentication for critical systems?",category:"Access Control",order_index:2,is_active:!0},{assessment_type_id:e,question_text:"How frequently do you backup your critical data?",category:"Data Protection",order_index:3,is_active:!0},{assessment_type_id:e,question_text:"Do you conduct regular security awareness training?",category:"Training & Awareness",order_index:4,is_active:!0},{assessment_type_id:e,question_text:"Do you have an incident response plan?",category:"Incident Management",order_index:5,is_active:!0}],{data:n}=await D.from("assessment_questions").select("id").order("id",{ascending:!1}).limit(1);let r=n&&n.length>0?n[0].id+1:1;const i=t.map(((e,t)=>({...e,id:r+t}))),{data:o,error:a}=await D.from("assessment_questions").insert(i).select();if(a)throw a;return o})(t);await(async e=>{const t=[];e.forEach(((e,n)=>{let r=[];switch(n){case 0:r=[{option_text:"Yes, comprehensive and regularly updated",risk_score:1,order_index:1},{option_text:"Yes, but needs updating",risk_score:3,order_index:2},{option_text:"Basic policy exists",risk_score:5,order_index:3},{option_text:"No formal policy",risk_score:8,order_index:4}];break;case 1:r=[{option_text:"Yes, for all critical systems",risk_score:1,order_index:1},{option_text:"Yes, for some systems",risk_score:4,order_index:2},{option_text:"Planning to implement",risk_score:6,order_index:3},{option_text:"No MFA implemented",risk_score:9,order_index:4}];break;case 2:r=[{option_text:"Daily automated backups",risk_score:1,order_index:1},{option_text:"Weekly backups",risk_score:3,order_index:2},{option_text:"Monthly backups",risk_score:5,order_index:3},{option_text:"No regular backups",risk_score:8,order_index:4}];break;case 3:r=[{option_text:"Quarterly training sessions",risk_score:1,order_index:1},{option_text:"Annual training",risk_score:3,order_index:2},{option_text:"Ad-hoc training",risk_score:5,order_index:3},{option_text:"No formal training",risk_score:7,order_index:4}];break;case 4:r=[{option_text:"Comprehensive tested plan",risk_score:1,order_index:1},{option_text:"Plan exists, not tested",risk_score:4,order_index:2},{option_text:"Basic plan in development",risk_score:6,order_index:3},{option_text:"No incident response plan",risk_score:8,order_index:4}]}r.forEach((n=>{t.push({...n,question_id:e.id})}))}));const{data:n,error:r}=await D.from("assessment_question_options").insert(t);if(r)throw r;return n})(n);const r=await(async e=>{const t=[{assessment_type_id:e,company_name:"TechCorp Ltd",industry:"Technology",employee_count:"50-100",contact_name:"John Smith",email:"<EMAIL>",phone:"+64 21 123 4567",status:"completed"},{assessment_type_id:e,company_name:"HealthPlus Medical",industry:"Healthcare",employee_count:"100-500",contact_name:"Sarah Johnson",email:"<EMAIL>",phone:"+64 21 987 6543",status:"completed"},{assessment_type_id:e,company_name:"RetailMax",industry:"Retail",employee_count:"10-50",contact_name:"Mike Brown",email:"<EMAIL>",phone:"+64 21 555 0123",status:"in_progress"}],{data:n,error:r}=await D.from("assessment_submissions").insert(t).select();if(r)throw r;return n})(t);await(async e=>{const t=e.map(((e,t)=>{const n=["HIGH","MEDIUM","LOW"][t%3];return{submission_id:e.id,risk_level:n,risk_percentage:"HIGH"===n?85:"MEDIUM"===n?55:25,lead_priority:"HIGH"===n?1:"MEDIUM"===n?3:5,total_risk_score:"HIGH"===n?34:"MEDIUM"===n?22:10,max_possible_score:40,follow_up_urgency:"HIGH"===n?"Immediate":"MEDIUM"===n?"Within 24 hours":"Within 1 week"}})),{data:n,error:r}=await D.from("lead_scores").insert(t);if(r)throw r;return n})(r),i({title:"Success",description:"Sample data has been populated successfully!",variant:"default"})}catch(e){i({title:"Error",description:"Failed to populate sample data. Check console for details.",variant:"destructive"})}finally{r(!1)}},disabled:n,variant:"default",children:n?"Populating...":"Populate Sample Data"}),t.jsx(U,{onClick:async()=>{r(!0);try{await D.from("lead_scores").delete().neq("id","00000000-0000-0000-0000-000000000000"),await D.from("assessment_question_options").delete().neq("id","00000000-0000-0000-0000-000000000000"),await D.from("assessment_questions").delete().neq("id",0),await D.from("assessment_submissions").delete().neq("id","00000000-0000-0000-0000-000000000000"),await D.from("assessment_types").delete().neq("id","00000000-0000-0000-0000-000000000000"),i({title:"Success",description:"All sample data has been cleared.",variant:"default"})}catch(e){i({title:"Error",description:"Failed to clear sample data. Check console for details.",variant:"destructive"})}finally{r(!1)}},disabled:n,variant:"destructive",children:n?"Clearing...":"Clear All Data"})]}),t.jsxs("div",{className:"text-sm text-muted-foreground",children:[t.jsx("strong",{children:"What this creates:"}),t.jsxs("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[t.jsx("li",{children:"1 Assessment Type (Cybersecurity Risk Assessment)"}),t.jsx("li",{children:"5 Sample Questions with multiple choice options"}),t.jsx("li",{children:"3 Sample Company Submissions"}),t.jsx("li",{children:"Lead Scores for each submission"})]})]})]})]})},JT=()=>t.jsxs("div",{className:"min-h-screen bg-black",children:[t.jsx(V,{title:"Admin Dashboard | Business Intelligence | BlackVeil",description:"Comprehensive business intelligence dashboard with analytics, lead management, and A/B testing for BlackVeil Security.",canonicalUrl:"https://blackveil.co.nz/admin"}),t.jsx(R,{className:"pt-16 pb-8",children:t.jsxs("div",{className:"max-w-7xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-bold mb-8 text-white",children:"Business Intelligence Dashboard"}),t.jsxs(Y,{defaultValue:"leads",className:"space-y-6",children:[t.jsxs(X,{className:"grid w-full grid-cols-6 bg-gray-900 border-gray-700",children:[t.jsxs(G,{value:"leads",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t.jsx(a,{className:"h-4 w-4"}),"Lead Management"]}),t.jsxs(G,{value:"analytics",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t.jsx(w,{className:"h-4 w-4"}),"Analytics"]}),t.jsxs(G,{value:"threats",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t.jsx(S,{className:"h-4 w-4"}),"Threat Intel"]}),t.jsxs(G,{value:"email",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t.jsx(l,{className:"h-4 w-4"}),"Email Automation"]}),t.jsxs(G,{value:"abtesting",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t.jsx(x,{className:"h-4 w-4"}),"A/B Testing"]}),t.jsxs(G,{value:"settings",className:"flex items-center gap-2 text-white data-[state=active]:bg-blue-600",children:[t.jsx(T,{className:"h-4 w-4"}),"Settings"]})]}),t.jsx(K,{value:"leads",className:"space-y-6",children:t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx(Wn,{})}),t.jsx("div",{children:t.jsx(qn,{})})]})}),t.jsx(K,{value:"analytics",className:"space-y-6",children:t.jsx("div",{className:"bg-white rounded-lg p-6",children:t.jsx(VT,{})})}),t.jsx(K,{value:"threats",className:"space-y-6",children:t.jsx("div",{className:"bg-white rounded-lg p-6",children:t.jsx(KT,{})})}),t.jsx(K,{value:"email",className:"space-y-6",children:t.jsx("div",{className:"bg-white rounded-lg p-6",children:t.jsx(ZT,{})})}),t.jsx(K,{value:"abtesting",className:"space-y-6",children:t.jsx("div",{className:"bg-white rounded-lg p-6",children:t.jsx(YT,{})})}),t.jsx(K,{value:"settings",className:"space-y-6",children:t.jsxs("div",{className:"space-y-6",children:[t.jsx(QT,{}),t.jsx("div",{className:"bg-white rounded-lg p-6",children:t.jsxs("div",{className:"text-center py-12",children:[t.jsx(T,{className:"h-12 w-12 mx-auto text-muted-foreground mb-4"}),t.jsx("h3",{className:"text-lg font-semibold mb-2",children:"Additional Settings"}),t.jsx("p",{className:"text-muted-foreground",children:"Additional system configuration and preferences will be available here."})]})})]})})]})]})})]});export{JT as default};
