import{am as e,r as s,j as t,aj as a,L as l,an as r,ac as i,S as n}from"./vendor-DEz8SL3m.js";import{S as o}from"./core-ClMcqRvE.js";import{u as c,C as m,a as d,b as u,P as x}from"./index-DeIkHZyv.js";import{I as h}from"./input-DEr6FcyC.js";import{L as b}from"./label-BOElYI-h.js";import{u as p,a as j,A as g,b as v}from"./use-assessment-by-slug-oWrKlYTH.js";import"./ui-BD6Ux9Dl.js";import"./security-D6XyL6Yo.js";const N=()=>{const{slug:n}=e(),{assessmentType:o,questions:x,loading:N,error:y}=p(n||""),{createSubmission:f,isSubmitting:w,submissionId:k}=j(o?.id||""),{toast:S}=c(),[C,q]=s.useState("info"),[A,F]=s.useState({companyName:"",industry:"",employeeCount:"",contactName:"",email:"",phone:""}),T=()=>{q("results")},E=(e,s)=>{F((t=>({...t,[e]:s})))};return N?t.jsx("div",{className:"max-w-2xl mx-auto",children:t.jsx(m,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsx(d,{className:"p-8 text-center",children:t.jsxs("div",{className:"animate-pulse",children:[t.jsx("div",{className:"h-8 bg-green-muted/20 rounded w-3/4 mx-auto mb-4"}),t.jsx("div",{className:"h-4 bg-green-muted/20 rounded w-full mb-2"}),t.jsx("div",{className:"h-4 bg-green-muted/20 rounded w-2/3 mx-auto"})]})})})}):y||!o?t.jsx("div",{className:"max-w-2xl mx-auto",children:t.jsx(m,{className:"cyber-gradient-card border border-red-500/30",children:t.jsxs(d,{className:"p-8 text-center",children:[t.jsx(a,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Assessment Not Available"}),t.jsx("p",{className:"text-red-400 mb-6",children:y||"The requested assessment could not be found."}),t.jsx(u,{asChild:!0,children:t.jsxs(l,{to:"/assessments",children:[t.jsx(r,{className:"w-4 h-4 mr-2"}),"Back to Assessment Library"]})})]})})}):"questions"===C?t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsx(g,{questions:x,onComplete:T,assessmentType:o})}):"results"===C?t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsx(v,{submissionId:k,onStartNew:()=>q("info")})}):t.jsx("div",{className:"max-w-2xl mx-auto",children:t.jsx(m,{className:"cyber-gradient-card border border-green-muted/30",children:t.jsxs(d,{className:"p-8",children:[t.jsxs("div",{className:"text-center mb-8",children:[t.jsx(i,{className:"h-12 w-12 text-green-bright mx-auto mb-4"}),t.jsx("h2",{className:"text-2xl font-bold mb-2",children:o.title}),t.jsx("p",{className:"text-white/70 mb-4",children:o.description}),t.jsxs("div",{className:"inline-flex items-center gap-2 bg-green-dark/30 px-3 py-1 rounded-full text-sm text-green-bright",children:["Estimated time: ",o.estimated_time_minutes," minutes"]})]}),t.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{await f(A);q("questions"),S({title:"Assessment Started",description:`Your ${o?.title} has been started. Let's continue with the questions.`})}catch(s){S({title:"Error",description:"Failed to start assessment. Please try again.",variant:"destructive"})}},className:"space-y-6",children:[t.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[t.jsxs("div",{children:[t.jsx(b,{htmlFor:"companyName",className:"text-white mb-2 block",children:"Company Name *"}),t.jsx(h,{id:"companyName",type:"text",required:!0,value:A.companyName,onChange:e=>E("companyName",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"Your company name"})]}),t.jsxs("div",{children:[t.jsx(b,{htmlFor:"industry",className:"text-white mb-2 block",children:"Industry *"}),t.jsxs("select",{id:"industry",required:!0,value:A.industry,onChange:e=>E("industry",e.target.value),className:"w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white",children:[t.jsx("option",{value:"",children:"Select your industry"}),t.jsx("option",{value:"healthcare",children:"Healthcare"}),t.jsx("option",{value:"finance",children:"Financial Services"}),t.jsx("option",{value:"education",children:"Education"}),t.jsx("option",{value:"government",children:"Government"}),t.jsx("option",{value:"manufacturing",children:"Manufacturing"}),t.jsx("option",{value:"retail",children:"Retail"}),t.jsx("option",{value:"technology",children:"Technology"}),t.jsx("option",{value:"professional-services",children:"Professional Services"}),t.jsx("option",{value:"other",children:"Other"})]})]})]}),t.jsxs("div",{children:[t.jsx(b,{htmlFor:"employeeCount",className:"text-white mb-2 block",children:"Number of Employees *"}),t.jsxs("select",{id:"employeeCount",required:!0,value:A.employeeCount,onChange:e=>E("employeeCount",e.target.value),className:"w-full p-3 bg-black-soft border border-green-muted/30 rounded-md text-white",children:[t.jsx("option",{value:"",children:"Select employee count"}),t.jsx("option",{value:"1-10",children:"1-10 employees"}),t.jsx("option",{value:"11-50",children:"11-50 employees"}),t.jsx("option",{value:"51-200",children:"51-200 employees"}),t.jsx("option",{value:"201-500",children:"201-500 employees"}),t.jsx("option",{value:"500+",children:"500+ employees"})]})]}),t.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[t.jsxs("div",{children:[t.jsx(b,{htmlFor:"contactName",className:"text-white mb-2 block",children:"Your Name *"}),t.jsx(h,{id:"contactName",type:"text",required:!0,value:A.contactName,onChange:e=>E("contactName",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"Your full name"})]}),t.jsxs("div",{children:[t.jsx(b,{htmlFor:"phone",className:"text-white mb-2 block",children:"Phone Number"}),t.jsx(h,{id:"phone",type:"tel",value:A.phone,onChange:e=>E("phone",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"Your phone number"})]})]}),t.jsxs("div",{children:[t.jsx(b,{htmlFor:"email",className:"text-white mb-2 block",children:"Email Address *"}),t.jsx(h,{id:"email",type:"email",required:!0,value:A.email,onChange:e=>E("email",e.target.value),className:"bg-black-soft border-green-muted/30 text-white",placeholder:"<EMAIL>"})]}),t.jsx(u,{type:"submit",disabled:w,className:"w-full bg-green-bright hover:bg-green-muted text-black font-semibold py-3",children:w?"Starting Assessment...":`Start ${o.title}`})]})]})})})},y=()=>{const{slug:s}=e();return s?t.jsxs("div",{className:"min-h-screen bg-black",children:[t.jsx(x,{title:"Security Assessment | BlackVeil",description:"Take a comprehensive cybersecurity assessment to identify vulnerabilities and improve your security posture.",canonicalUrl:`https://blackveil.co.nz/assessment/${s}`}),t.jsx(o,{className:"pt-16 pb-20",children:t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsx(N,{})})})]}):t.jsx("div",{className:"min-h-screen bg-black flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx(n,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),t.jsx("h1",{className:"text-2xl font-bold text-white mb-2",children:"Assessment Not Found"}),t.jsx("p",{className:"text-white/70",children:"The requested assessment could not be found."})]})})};export{y as default};
