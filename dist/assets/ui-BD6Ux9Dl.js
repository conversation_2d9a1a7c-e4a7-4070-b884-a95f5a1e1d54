import{r as e,j as t,R as n,a as r,b as o,c as i}from"./vendor-DEz8SL3m.js";function a(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function s(...t){return e.useCallback(a(...t),t)}var c=e.forwardRef(((n,r)=>{const{children:o,...i}=n,a=e.Children.toArray(o),s=a.find(d);if(s){const n=s.props.children,o=a.map((t=>t===s?e.Children.count(n)>1?e.Children.only(null):e.isValidElement(n)?n.props.children:null:t));return t.jsx(l,{...i,ref:r,children:e.isValidElement(n)?e.cloneElement(n,void 0,o):null})}return t.jsx(l,{...i,ref:r,children:o})}));c.displayName="Slot";var l=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(r);return e.cloneElement(r,{...f(o,r.props),ref:n?a(n,t):t})}return e.Children.count(r)>1?e.Children.only(null):null}));l.displayName="SlotClone";var u=({children:e})=>t.jsx(t.Fragment,{children:e});function d(t){return e.isValidElement(t)&&t.type===u}function f(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function p(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function m(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function h(r){const o=r+"CollectionProvider",[i,a]=function(n,r=[]){let o=[];const i=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return i.scopeName=n,[function(r,i){const a=e.createContext(i),s=o.length;function c(r){const{scope:o,children:i,...c}=r,l=o?.[n][s]||a,u=e.useMemo((()=>c),Object.values(c));return t.jsx(l.Provider,{value:u,children:i})}return o=[...o,i],c.displayName=r+"Provider",[c,function(t,o){const c=o?.[n][s]||a,l=e.useContext(c);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},m(i,...r)]}(o),[l,u]=i(o,{collectionRef:{current:null},itemMap:new Map}),d=e=>{const{scope:r,children:o}=e,i=n.useRef(null),a=n.useRef(new Map).current;return t.jsx(l,{scope:r,itemMap:a,collectionRef:i,children:o})};d.displayName=o;const f=r+"CollectionSlot",p=n.forwardRef(((e,n)=>{const{scope:r,children:o}=e,i=s(n,u(f,r).collectionRef);return t.jsx(c,{ref:i,children:o})}));p.displayName=f;const h=r+"CollectionItemSlot",v="data-radix-collection-item",g=n.forwardRef(((e,r)=>{const{scope:o,children:i,...a}=e,l=n.useRef(null),d=s(r,l),f=u(h,o);return n.useEffect((()=>(f.itemMap.set(l,{ref:l,...a}),()=>{f.itemMap.delete(l)}))),t.jsx(c,{[v]:"",ref:d,children:i})}));return g.displayName=h,[{Provider:d,Slot:p,ItemSlot:g},function(e){const t=u(r+"CollectionConsumer",e);return n.useCallback((()=>{const e=t.collectionRef.current;if(!e)return[];const n=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(t.itemMap.values()).sort(((e,t)=>n.indexOf(e.ref.current)-n.indexOf(t.ref.current)))}),[t.collectionRef,t.itemMap])},a]}function v(n,r=[]){let o=[];const i=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return i.scopeName=n,[function(r,i){const a=e.createContext(i),s=o.length;o=[...o,i];const c=r=>{const{scope:o,children:i,...c}=r,l=o?.[n]?.[s]||a,u=e.useMemo((()=>c),Object.values(c));return t.jsx(l.Provider,{value:u,children:i})};return c.displayName=r+"Provider",[c,function(t,o){const c=o?.[n]?.[s]||a,l=e.useContext(c);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},g(i,...r)]}function g(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var y=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((n,r)=>{const o=e.forwardRef(((e,n)=>{const{asChild:o,...i}=e,a=o?c:r;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),t.jsx(a,{...i,ref:n})}));return o.displayName=`Primitive.${r}`,{...n,[r]:o}}),{});function w(e,t){e&&r.flushSync((()=>e.dispatchEvent(t)))}function b(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>n.current?.(...e)),[])}var x,E="dismissableLayer.update",R="dismissableLayer.pointerDownOutside",C="dismissableLayer.focusOutside",A=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=e.forwardRef(((n,r)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:c,onInteractOutside:l,onDismiss:u,...d}=n,f=e.useContext(A),[m,h]=e.useState(null),v=m?.ownerDocument??globalThis?.document,[,g]=e.useState({}),w=s(r,(e=>h(e))),S=Array.from(f.layers),[N]=[...f.layersWithOutsidePointerEventsDisabled].slice(-1),_=S.indexOf(N),O=m?S.indexOf(m):-1,T=f.layersWithOutsidePointerEventsDisabled.size>0,j=O>=_,M=function(t,n=globalThis?.document){const r=b(t),o=e.useRef(!1),i=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){P(R,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...f.branches].some((e=>e.contains(t)));j&&!n&&(a?.(e),l?.(e),e.defaultPrevented||u?.())}),v),L=function(t,n=globalThis?.document){const r=b(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){P(C,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...f.branches].some((e=>e.contains(t)))||(c?.(e),l?.(e),e.defaultPrevented||u?.())}),v);return function(t,n=globalThis?.document){const r=b(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{O===f.layers.size-1&&(i?.(e),!e.defaultPrevented&&u&&(e.preventDefault(),u()))}),v),e.useEffect((()=>{if(m)return o&&(0===f.layersWithOutsidePointerEventsDisabled.size&&(x=v.body.style.pointerEvents,v.body.style.pointerEvents="none"),f.layersWithOutsidePointerEventsDisabled.add(m)),f.layers.add(m),D(),()=>{o&&1===f.layersWithOutsidePointerEventsDisabled.size&&(v.body.style.pointerEvents=x)}}),[m,v,o,f]),e.useEffect((()=>()=>{m&&(f.layers.delete(m),f.layersWithOutsidePointerEventsDisabled.delete(m),D())}),[m,f]),e.useEffect((()=>{const e=()=>g({});return document.addEventListener(E,e),()=>document.removeEventListener(E,e)}),[]),t.jsx(y.div,{...d,ref:w,style:{pointerEvents:T?j?"auto":"none":void 0,...n.style},onFocusCapture:p(n.onFocusCapture,L.onFocusCapture),onBlurCapture:p(n.onBlurCapture,L.onBlurCapture),onPointerDownCapture:p(n.onPointerDownCapture,M.onPointerDownCapture)})}));S.displayName="DismissableLayer";var N=e.forwardRef(((n,r)=>{const o=e.useContext(A),i=e.useRef(null),a=s(r,i);return e.useEffect((()=>{const e=i.current;if(e)return o.branches.add(e),()=>{o.branches.delete(e)}}),[o.branches]),t.jsx(y.div,{...n,ref:a})}));function D(){const e=new CustomEvent(E);document.dispatchEvent(e)}function P(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?w(o,i):o.dispatchEvent(i)}N.displayName="DismissableLayerBranch";var _=S,O=N,T=Boolean(globalThis?.document)?e.useLayoutEffect:()=>{},j=e.forwardRef(((n,r)=>{const{container:i,...a}=n,[s,c]=e.useState(!1);T((()=>c(!0)),[]);const l=i||s&&globalThis?.document?.body;return l?o.createPortal(t.jsx(y.div,{...a,ref:r}),l):null}));j.displayName="Portal";var M=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef({}),i=e.useRef(t),a=e.useRef("none"),s=t?"mounted":"unmounted",[c,l]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=L(o.current);a.current="mounted"===c?e:"none"}),[c]),T((()=>{const e=o.current,n=i.current;if(n!==t){const r=a.current,o=L(e);if(t)l("MOUNT");else if("none"===o||"none"===e?.display)l("UNMOUNT");else{l(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}i.current=t}}),[t,l]),T((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const a=L(o.current).includes(r.animationName);if(r.target===n&&a&&(l("ANIMATION_END"),!i.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},s=e=>{e.target===n&&(a.current=L(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}l("ANIMATION_END")}),[n,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:e.useCallback((e=>{e&&(o.current=getComputedStyle(e)),r(e)}),[])}}(n),i="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),a=s(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;if(n)return e.ref;if(t=Object.getOwnPropertyDescriptor(e,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n)return e.props.ref;return e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?e.cloneElement(i,{ref:a}):null};function L(e){return e?.animationName||"none"}function I({prop:t,defaultProp:n,onChange:r=()=>{}}){const[o,i]=function({defaultProp:t,onChange:n}){const r=e.useState(t),[o]=r,i=e.useRef(o),a=b(n);return e.useEffect((()=>{i.current!==o&&(a(o),i.current=o)}),[o,i,a]),r}({defaultProp:n,onChange:r}),a=void 0!==t,s=a?t:o,c=b(r);return[s,e.useCallback((e=>{if(a){const n="function"==typeof e?e(t):e;n!==t&&c(n)}else i(e)}),[a,t,i,c])]}M.displayName="Presence";var k=e.forwardRef(((e,n)=>t.jsx(y.span,{...e,ref:n,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));k.displayName="VisuallyHidden";var F=i["useId".toString()]||(()=>{}),W=0;function $(t){const[n,r]=e.useState(F());return T((()=>{r((e=>e??String(W++)))}),[t]),n?`radix-${n}`:""}var B="focusScope.autoFocusOnMount",H="focusScope.autoFocusOnUnmount",V={bubbles:!1,cancelable:!0},z=e.forwardRef(((n,r)=>{const{loop:o=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...l}=n,[u,d]=e.useState(null),f=b(a),p=b(c),m=e.useRef(null),h=s(r,(e=>d(e))),v=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect((()=>{if(i){let e=function(e){if(v.paused||!u)return;const t=e.target;u.contains(t)?m.current=t:X(m.current,{select:!0})},t=function(e){if(v.paused||!u)return;const t=e.relatedTarget;null!==t&&(u.contains(t)||X(m.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&X(u)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return u&&r.observe(u,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[i,u,v.paused]),e.useEffect((()=>{if(u){q.add(v);const t=document.activeElement;if(!u.contains(t)){const n=new CustomEvent(B,V);u.addEventListener(B,f),u.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(X(r,{select:t}),document.activeElement!==n)return}((e=K(u),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&X(u))}return()=>{u.removeEventListener(B,f),setTimeout((()=>{const e=new CustomEvent(H,V);u.addEventListener(H,p),u.dispatchEvent(e),e.defaultPrevented||X(t??document.body,{select:!0}),u.removeEventListener(H,p),q.remove(v)}),0)}}var e}),[u,f,p,v]);const g=e.useCallback((e=>{if(!o&&!i)return;if(v.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[r,i]=function(e){const t=K(e),n=U(t,e),r=U(t.reverse(),e);return[n,r]}(t);r&&i?e.shiftKey||n!==i?e.shiftKey&&n===r&&(e.preventDefault(),o&&X(i,{select:!0})):(e.preventDefault(),o&&X(r,{select:!0})):n===t&&e.preventDefault()}}),[o,i,v.paused]);return t.jsx(y.div,{tabIndex:-1,...l,ref:h,onKeyDown:g})}));function K(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function U(e,t){for(const n of e)if(!Y(n,{upTo:t}))return n}function Y(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function X(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}z.displayName="FocusScope";var q=function(){let e=[];return{add(t){const n=e[0];t!==n&&n?.pause(),e=G(e,t),e.unshift(t)},remove(t){e=G(e,t),e[0]?.resume()}}}();function G(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var Z=0;function J(){e.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Q()),document.body.insertAdjacentElement("beforeend",e[1]??Q()),Z++,()=>{1===Z&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),Z--}}),[])}function Q(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ee=function(){return ee=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},ee.apply(this,arguments)};function te(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var ne="right-scroll-bar-position",re="width-before-scroll-bar";function oe(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var ie="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,ae=new WeakMap;function se(t,n){var r,o,i,a=(r=null,o=function(e){return t.forEach((function(t){return oe(t,e)}))},(i=e.useState((function(){return{value:r,callback:o,facade:{get current(){return i.value},set current(e){var t=i.value;t!==e&&(i.value=e,i.callback(e,t))}}}}))[0]).callback=o,i.facade);return ie((function(){var e=ae.get(a);if(e){var n=new Set(e),r=new Set(t),o=a.current;n.forEach((function(e){r.has(e)||oe(e,null)})),r.forEach((function(e){n.has(e)||oe(e,o)}))}ae.set(a,t)}),[t]),a}function ce(e){return e}var le=function(t){var n=t.sideCar,r=te(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,ee({},r))};le.isSideCarExport=!0;var ue=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=ce);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=ee({async:!0,ssr:!1},e),t}(),de=function(){},fe=e.forwardRef((function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:de,onWheelCapture:de,onTouchMoveCapture:de}),i=o[0],a=o[1],s=t.forwardProps,c=t.children,l=t.className,u=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,m=t.noIsolation,h=t.inert,v=t.allowPinchZoom,g=t.as,y=void 0===g?"div":g,w=t.gapMode,b=te(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=p,E=se([r,n]),R=ee(ee({},b),i);return e.createElement(e.Fragment,null,d&&e.createElement(x,{sideCar:ue,removeScrollBar:u,shards:f,noIsolation:m,inert:h,setCallbacks:a,allowPinchZoom:!!v,lockRef:r,gapMode:w}),s?e.cloneElement(e.Children.only(c),ee(ee({},R),{ref:E})):e.createElement(y,ee({},R,{className:l,ref:E}),c))}));fe.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},fe.classNames={fullWidth:re,zeroRight:ne};function pe(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var me=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=pe())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},he=function(){var t,n=(t=me(),function(n,r){e.useEffect((function(){return t.add(n),function(){t.remove()}}),[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},ve={left:0,top:0,right:0,gap:0},ge=function(e){return parseInt(e||"",10)||0},ye=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return ve;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ge(n),ge(r),ge(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},we=he(),be="data-scroll-locked",xe=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(be,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(ne," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(re," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(ne," .").concat(ne," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(re," .").concat(re," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(be,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},Ee=function(){var e=parseInt(document.body.getAttribute(be)||"0",10);return isFinite(e)?e:0},Re=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,i=void 0===o?"margin":o;e.useEffect((function(){return document.body.setAttribute(be,(Ee()+1).toString()),function(){var e=Ee()-1;e<=0?document.body.removeAttribute(be):document.body.setAttribute(be,e.toString())}}),[]);var a=e.useMemo((function(){return ye(i)}),[i]);return e.createElement(we,{styles:xe(a,!n,i,r?"":"!important")})},Ce=!1;if("undefined"!=typeof window)try{var Ae=Object.defineProperty({},"passive",{get:function(){return Ce=!0,!0}});window.addEventListener("test",Ae,Ae),window.removeEventListener("test",Ae,Ae)}catch(pi){Ce=!1}var Se=!!Ce&&{passive:!1},Ne=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},De=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Pe(e,r)){var o=_e(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Pe=function(e,t){return"v"===e?function(e){return Ne(e,"overflowY")}(t):function(e){return Ne(e,"overflowX")}(t)},_e=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Oe=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Te=function(e){return[e.deltaX,e.deltaY]},je=function(e){return e&&"current"in e?e.current:e},Me=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Le=0,Ie=[];function ke(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Fe=(We=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),i=e.useState(Le++)[0],a=e.useState(he)[0],s=e.useRef(t);e.useEffect((function(){s.current=t}),[t]),e.useEffect((function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(i));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(je),!0).filter(Boolean);return e.forEach((function(e){return e.classList.add("allow-interactivity-".concat(i))})),function(){document.body.classList.remove("block-interactivity-".concat(i)),e.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(i))}))}}}),[t.inert,t.lockRef.current,t.shards]);var c=e.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var n,i=Oe(e),a=r.current,c="deltaX"in e?e.deltaX:a[0]-i[0],l="deltaY"in e?e.deltaY:a[1]-i[1],u=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=De(d,u);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=De(d,u)),!f)return!1;if(!o.current&&"changedTouches"in e&&(c||l)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r,o){var i=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),a=i*r,s=n.target,c=t.contains(s),l=!1,u=a>0,d=0,f=0;do{var p=_e(e,s),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&Pe(e,s)&&(d+=h,f+=m),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return u&&(Math.abs(d)<1||!o)?l=!0:u||!(Math.abs(f)<1)&&o||(l=!0),l}(p,t,e,"h"===p?c:l,!0)}),[]),l=e.useCallback((function(e){var t=e;if(Ie.length&&Ie[Ie.length-1]===a){var r="deltaY"in t?Te(t):Oe(t),o=n.current.filter((function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o}))[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var i=(s.current.shards||[]).map(je).filter(Boolean).filter((function(e){return e.contains(t.target)}));(i.length>0?c(t,i[0]):!s.current.noIsolation)&&t.cancelable&&t.preventDefault()}}}),[]),u=e.useCallback((function(e,t,r,o){var i={name:e,delta:t,target:r,should:o,shadowParent:ke(r)};n.current.push(i),setTimeout((function(){n.current=n.current.filter((function(e){return e!==i}))}),1)}),[]),d=e.useCallback((function(e){r.current=Oe(e),o.current=void 0}),[]),f=e.useCallback((function(e){u(e.type,Te(e),e.target,c(e,t.lockRef.current))}),[]),p=e.useCallback((function(e){u(e.type,Oe(e),e.target,c(e,t.lockRef.current))}),[]);e.useEffect((function(){return Ie.push(a),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,Se),document.addEventListener("touchmove",l,Se),document.addEventListener("touchstart",d,Se),function(){Ie=Ie.filter((function(e){return e!==a})),document.removeEventListener("wheel",l,Se),document.removeEventListener("touchmove",l,Se),document.removeEventListener("touchstart",d,Se)}}),[]);var m=t.removeScrollBar,h=t.inert;return e.createElement(e.Fragment,null,h?e.createElement(a,{styles:Me(i)}):null,m?e.createElement(Re,{gapMode:t.gapMode}):null)},ue.useMedium(We),le);var We,$e=e.forwardRef((function(t,n){return e.createElement(fe,ee({},t,{ref:n,sideCar:Fe}))}));$e.classNames=fe.classNames;var Be=new WeakMap,He=new WeakMap,Ve={},ze=0,Ke=function(e){return e&&(e.host||Ke(e.parentNode))},Ue=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=Ke(t);return n&&e.contains(n)?n:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);Ve[n]||(Ve[n]=new WeakMap);var i=Ve[n],a=[],s=new Set,c=new Set(o),l=function(e){e&&!s.has(e)&&(s.add(e),l(e.parentNode))};o.forEach(l);var u=function(e){e&&!c.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,c=(Be.get(e)||0)+1,l=(i.get(e)||0)+1;Be.set(e,c),i.set(e,l),a.push(e),1===c&&o&&He.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}}))};return u(t),s.clear(),ze++,function(){a.forEach((function(e){var t=Be.get(e)-1,o=i.get(e)-1;Be.set(e,t),i.set(e,o),t||(He.has(e)||e.removeAttribute(r),He.delete(e)),o||e.removeAttribute(n)})),--ze||(Be=new WeakMap,Be=new WeakMap,He=new WeakMap,Ve={})}},Ye=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Ue(r,o,n,"aria-hidden")):function(){return null}},Xe="Dialog",[qe,Ge]=v(Xe),[Ze,Je]=qe(Xe),Qe=n=>{const{__scopeDialog:r,children:o,open:i,defaultOpen:a,onOpenChange:s,modal:c=!0}=n,l=e.useRef(null),u=e.useRef(null),[d=!1,f]=I({prop:i,defaultProp:a,onChange:s});return t.jsx(Ze,{scope:r,triggerRef:l,contentRef:u,contentId:$(),titleId:$(),descriptionId:$(),open:d,onOpenChange:f,onOpenToggle:e.useCallback((()=>f((e=>!e))),[f]),modal:c,children:o})};Qe.displayName=Xe;var et="DialogTrigger";e.forwardRef(((e,n)=>{const{__scopeDialog:r,...o}=e,i=Je(et,r),a=s(n,i.triggerRef);return t.jsx(y.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":wt(i.open),...o,ref:a,onClick:p(e.onClick,i.onOpenToggle)})})).displayName=et;var tt="DialogPortal",[nt,rt]=qe(tt,{forceMount:void 0}),ot=n=>{const{__scopeDialog:r,forceMount:o,children:i,container:a}=n,s=Je(tt,r);return t.jsx(nt,{scope:r,forceMount:o,children:e.Children.map(i,(e=>t.jsx(M,{present:o||s.open,children:t.jsx(j,{asChild:!0,container:a,children:e})})))})};ot.displayName=tt;var it="DialogOverlay",at=e.forwardRef(((e,n)=>{const r=rt(it,e.__scopeDialog),{forceMount:o=r.forceMount,...i}=e,a=Je(it,e.__scopeDialog);return a.modal?t.jsx(M,{present:o||a.open,children:t.jsx(st,{...i,ref:n})}):null}));at.displayName=it;var st=e.forwardRef(((e,n)=>{const{__scopeDialog:r,...o}=e,i=Je(it,r);return t.jsx($e,{as:c,allowPinchZoom:!0,shards:[i.contentRef],children:t.jsx(y.div,{"data-state":wt(i.open),...o,ref:n,style:{pointerEvents:"auto",...o.style}})})})),ct="DialogContent",lt=e.forwardRef(((e,n)=>{const r=rt(ct,e.__scopeDialog),{forceMount:o=r.forceMount,...i}=e,a=Je(ct,e.__scopeDialog);return t.jsx(M,{present:o||a.open,children:a.modal?t.jsx(ut,{...i,ref:n}):t.jsx(dt,{...i,ref:n})})}));lt.displayName=ct;var ut=e.forwardRef(((n,r)=>{const o=Je(ct,n.__scopeDialog),i=e.useRef(null),a=s(r,o.contentRef,i);return e.useEffect((()=>{const e=i.current;if(e)return Ye(e)}),[]),t.jsx(ft,{...n,ref:a,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:p(n.onCloseAutoFocus,(e=>{e.preventDefault(),o.triggerRef.current?.focus()})),onPointerDownOutside:p(n.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:p(n.onFocusOutside,(e=>e.preventDefault()))})})),dt=e.forwardRef(((n,r)=>{const o=Je(ct,n.__scopeDialog),i=e.useRef(!1),a=e.useRef(!1);return t.jsx(ft,{...n,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{n.onCloseAutoFocus?.(e),e.defaultPrevented||(i.current||o.triggerRef.current?.focus(),e.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:e=>{n.onInteractOutside?.(e),e.defaultPrevented||(i.current=!0,"pointerdown"===e.detail.originalEvent.type&&(a.current=!0));const t=e.target,r=o.triggerRef.current?.contains(t);r&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&a.current&&e.preventDefault()}})})),ft=e.forwardRef(((n,r)=>{const{__scopeDialog:o,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:c,...l}=n,u=Je(ct,o),d=e.useRef(null),f=s(r,d);return J(),t.jsxs(t.Fragment,{children:[t.jsx(z,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:c,children:t.jsx(S,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":wt(u.open),...l,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),t.jsxs(t.Fragment,{children:[t.jsx(Rt,{titleId:u.titleId}),t.jsx(Ct,{contentRef:d,descriptionId:u.descriptionId})]})]})})),pt="DialogTitle",mt=e.forwardRef(((e,n)=>{const{__scopeDialog:r,...o}=e,i=Je(pt,r);return t.jsx(y.h2,{id:i.titleId,...o,ref:n})}));mt.displayName=pt;var ht="DialogDescription",vt=e.forwardRef(((e,n)=>{const{__scopeDialog:r,...o}=e,i=Je(ht,r);return t.jsx(y.p,{id:i.descriptionId,...o,ref:n})}));vt.displayName=ht;var gt="DialogClose",yt=e.forwardRef(((e,n)=>{const{__scopeDialog:r,...o}=e,i=Je(gt,r);return t.jsx(y.button,{type:"button",...o,ref:n,onClick:p(e.onClick,(()=>i.onOpenChange(!1)))})}));function wt(e){return e?"open":"closed"}yt.displayName=gt;var bt="DialogTitleWarning",[xt,Et]=function(n,r){const o=e.createContext(r),i=n=>{const{children:r,...i}=n,a=e.useMemo((()=>i),Object.values(i));return t.jsx(o.Provider,{value:a,children:r})};return i.displayName=n+"Provider",[i,function(t){const i=e.useContext(o);if(i)return i;if(void 0!==r)return r;throw new Error(`\`${t}\` must be used within \`${n}\``)}]}(bt,{contentName:ct,titleName:pt,docsSlug:"dialog"}),Rt=({titleId:t})=>{const n=Et(bt),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect((()=>{if(t){document.getElementById(t)}}),[r,t]),null},Ct=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Et("DialogDescriptionWarning").contentName}}.`;return e.useEffect((()=>{const e=t.current?.getAttribute("aria-describedby");if(n&&e){document.getElementById(n)}}),[r,t,n]),null},At=Qe,St=ot,Nt=at,Dt=lt,Pt=mt,_t=vt,Ot=yt,Tt="Collapsible",[jt,Mt]=v(Tt),[Lt,It]=jt(Tt),kt=e.forwardRef(((n,r)=>{const{__scopeCollapsible:o,open:i,defaultOpen:a,disabled:s,onOpenChange:c,...l}=n,[u=!1,d]=I({prop:i,defaultProp:a,onChange:c});return t.jsx(Lt,{scope:o,disabled:s,contentId:$(),open:u,onOpenToggle:e.useCallback((()=>d((e=>!e))),[d]),children:t.jsx(y.div,{"data-state":Vt(u),"data-disabled":s?"":void 0,...l,ref:r})})}));kt.displayName=Tt;var Ft="CollapsibleTrigger",Wt=e.forwardRef(((e,n)=>{const{__scopeCollapsible:r,...o}=e,i=It(Ft,r);return t.jsx(y.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":Vt(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...o,ref:n,onClick:p(e.onClick,i.onOpenToggle)})}));Wt.displayName=Ft;var $t="CollapsibleContent",Bt=e.forwardRef(((e,n)=>{const{forceMount:r,...o}=e,i=It($t,e.__scopeCollapsible);return t.jsx(M,{present:r||i.open,children:({present:e})=>t.jsx(Ht,{...o,ref:n,present:e})})}));Bt.displayName=$t;var Ht=e.forwardRef(((n,r)=>{const{__scopeCollapsible:o,present:i,children:a,...c}=n,l=It($t,o),[u,d]=e.useState(i),f=e.useRef(null),p=s(r,f),m=e.useRef(0),h=m.current,v=e.useRef(0),g=v.current,w=l.open||u,b=e.useRef(w),x=e.useRef();return e.useEffect((()=>{const e=requestAnimationFrame((()=>b.current=!1));return()=>cancelAnimationFrame(e)}),[]),T((()=>{const e=f.current;if(e){x.current=x.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";const t=e.getBoundingClientRect();m.current=t.height,v.current=t.width,b.current||(e.style.transitionDuration=x.current.transitionDuration,e.style.animationName=x.current.animationName),d(i)}}),[l.open,i]),t.jsx(y.div,{"data-state":Vt(l.open),"data-disabled":l.disabled?"":void 0,id:l.contentId,hidden:!w,...c,ref:p,style:{"--radix-collapsible-content-height":h?`${h}px`:void 0,"--radix-collapsible-content-width":g?`${g}px`:void 0,...n.style},children:w&&a})}));function Vt(e){return e?"open":"closed"}var zt=kt,Kt=Wt,Ut=Bt,Yt=e.createContext(void 0);function Xt(t){const n=e.useContext(Yt);return t||n||"ltr"}var qt="Accordion",Gt=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[Zt,Jt,Qt]=h(qt),[en,tn]=v(qt,[Qt,Mt]),nn=Mt(),rn=n.forwardRef(((e,n)=>{const{type:r,...o}=e,i=o,a=o;return t.jsx(Zt.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?t.jsx(un,{...a,ref:n}):t.jsx(ln,{...i,ref:n})})}));rn.displayName=qt;var[on,an]=en(qt),[sn,cn]=en(qt,{collapsible:!1}),ln=n.forwardRef(((e,r)=>{const{value:o,defaultValue:i,onValueChange:a=()=>{},collapsible:s=!1,...c}=e,[l,u]=I({prop:o,defaultProp:i,onChange:a});return t.jsx(on,{scope:e.__scopeAccordion,value:l?[l]:[],onItemOpen:u,onItemClose:n.useCallback((()=>s&&u("")),[s,u]),children:t.jsx(sn,{scope:e.__scopeAccordion,collapsible:s,children:t.jsx(pn,{...c,ref:r})})})})),un=n.forwardRef(((e,r)=>{const{value:o,defaultValue:i,onValueChange:a=()=>{},...s}=e,[c=[],l]=I({prop:o,defaultProp:i,onChange:a}),u=n.useCallback((e=>l(((t=[])=>[...t,e]))),[l]),d=n.useCallback((e=>l(((t=[])=>t.filter((t=>t!==e))))),[l]);return t.jsx(on,{scope:e.__scopeAccordion,value:c,onItemOpen:u,onItemClose:d,children:t.jsx(sn,{scope:e.__scopeAccordion,collapsible:!0,children:t.jsx(pn,{...s,ref:r})})})})),[dn,fn]=en(qt),pn=n.forwardRef(((e,r)=>{const{__scopeAccordion:o,disabled:i,dir:a,orientation:c="vertical",...l}=e,u=s(n.useRef(null),r),d=Jt(o),f="ltr"===Xt(a),m=p(e.onKeyDown,(e=>{if(!Gt.includes(e.key))return;const t=e.target,n=d().filter((e=>!e.ref.current?.disabled)),r=n.findIndex((e=>e.ref.current===t)),o=n.length;if(-1===r)return;e.preventDefault();let i=r;const a=o-1,s=()=>{i=r+1,i>a&&(i=0)},l=()=>{i=r-1,i<0&&(i=a)};switch(e.key){case"Home":i=0;break;case"End":i=a;break;case"ArrowRight":"horizontal"===c&&(f?s():l());break;case"ArrowDown":"vertical"===c&&s();break;case"ArrowLeft":"horizontal"===c&&(f?l():s());break;case"ArrowUp":"vertical"===c&&l()}const u=i%o;n[u].ref.current?.focus()}));return t.jsx(dn,{scope:o,disabled:i,direction:a,orientation:c,children:t.jsx(Zt.Slot,{scope:o,children:t.jsx(y.div,{...l,"data-orientation":c,ref:u,onKeyDown:i?void 0:m})})})})),mn="AccordionItem",[hn,vn]=en(mn),gn=n.forwardRef(((e,n)=>{const{__scopeAccordion:r,value:o,...i}=e,a=fn(mn,r),s=an(mn,r),c=nn(r),l=$(),u=o&&s.value.includes(o)||!1,d=a.disabled||e.disabled;return t.jsx(hn,{scope:r,open:u,disabled:d,triggerId:l,children:t.jsx(zt,{"data-orientation":a.orientation,"data-state":Cn(u),...c,...i,ref:n,disabled:d,open:u,onOpenChange:e=>{e?s.onItemOpen(o):s.onItemClose(o)}})})}));gn.displayName=mn;var yn="AccordionHeader",wn=n.forwardRef(((e,n)=>{const{__scopeAccordion:r,...o}=e,i=fn(qt,r),a=vn(yn,r);return t.jsx(y.h3,{"data-orientation":i.orientation,"data-state":Cn(a.open),"data-disabled":a.disabled?"":void 0,...o,ref:n})}));wn.displayName=yn;var bn="AccordionTrigger",xn=n.forwardRef(((e,n)=>{const{__scopeAccordion:r,...o}=e,i=fn(qt,r),a=vn(bn,r),s=cn(bn,r),c=nn(r);return t.jsx(Zt.ItemSlot,{scope:r,children:t.jsx(Kt,{"aria-disabled":a.open&&!s.collapsible||void 0,"data-orientation":i.orientation,id:a.triggerId,...c,...o,ref:n})})}));xn.displayName=bn;var En="AccordionContent",Rn=n.forwardRef(((e,n)=>{const{__scopeAccordion:r,...o}=e,i=fn(qt,r),a=vn(En,r),s=nn(r);return t.jsx(Ut,{role:"region","aria-labelledby":a.triggerId,"data-orientation":i.orientation,...s,...o,ref:n,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})}));function Cn(e){return e?"open":"closed"}Rn.displayName=En;var An=rn,Sn=gn,Nn=wn,Dn=xn,Pn=Rn;function _n(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var On="rovingFocusGroup.onEntryFocus",Tn={bubbles:!1,cancelable:!0},jn="RovingFocusGroup",[Mn,Ln,In]=h(jn),[kn,Fn]=function(n,r=[]){let o=[];const i=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return i.scopeName=n,[function(r,i){const a=e.createContext(i),s=o.length;function c(r){const{scope:o,children:i,...c}=r,l=o?.[n][s]||a,u=e.useMemo((()=>c),Object.values(c));return t.jsx(l.Provider,{value:u,children:i})}return o=[...o,i],c.displayName=r+"Provider",[c,function(t,o){const c=o?.[n][s]||a,l=e.useContext(c);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},_n(i,...r)]}(jn,[In]),[Wn,$n]=kn(jn),Bn=e.forwardRef(((e,n)=>t.jsx(Mn.Provider,{scope:e.__scopeRovingFocusGroup,children:t.jsx(Mn.Slot,{scope:e.__scopeRovingFocusGroup,children:t.jsx(Hn,{...e,ref:n})})})));Bn.displayName=jn;var Hn=e.forwardRef(((n,r)=>{const{__scopeRovingFocusGroup:o,orientation:i,loop:a=!1,dir:c,currentTabStopId:l,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:d,onEntryFocus:f,preventScrollOnEntryFocus:m=!1,...h}=n,v=e.useRef(null),g=s(r,v),w=Xt(c),[x=null,E]=I({prop:l,defaultProp:u,onChange:d}),[R,C]=e.useState(!1),A=b(f),S=Ln(o),N=e.useRef(!1),[D,P]=e.useState(0);return e.useEffect((()=>{const e=v.current;if(e)return e.addEventListener(On,A),()=>e.removeEventListener(On,A)}),[A]),t.jsx(Wn,{scope:o,orientation:i,dir:w,loop:a,currentTabStopId:x,onItemFocus:e.useCallback((e=>E(e)),[E]),onItemShiftTab:e.useCallback((()=>C(!0)),[]),onFocusableItemAdd:e.useCallback((()=>P((e=>e+1))),[]),onFocusableItemRemove:e.useCallback((()=>P((e=>e-1))),[]),children:t.jsx(y.div,{tabIndex:R||0===D?-1:0,"data-orientation":i,...h,ref:g,style:{outline:"none",...n.style},onMouseDown:p(n.onMouseDown,(()=>{N.current=!0})),onFocus:p(n.onFocus,(e=>{const t=!N.current;if(e.target===e.currentTarget&&t&&!R){const t=new CustomEvent(On,Tn);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=S().filter((e=>e.focusable));Un([e.find((e=>e.active)),e.find((e=>e.id===x)),...e].filter(Boolean).map((e=>e.ref.current)),m)}}N.current=!1})),onBlur:p(n.onBlur,(()=>C(!1)))})})})),Vn="RovingFocusGroupItem",zn=e.forwardRef(((n,r)=>{const{__scopeRovingFocusGroup:o,focusable:i=!0,active:a=!1,tabStopId:s,...c}=n,l=$(),u=s||l,d=$n(Vn,o),f=d.currentTabStopId===u,m=Ln(o),{onFocusableItemAdd:h,onFocusableItemRemove:v}=d;return e.useEffect((()=>{if(i)return h(),()=>v()}),[i,h,v]),t.jsx(Mn.ItemSlot,{scope:o,id:u,focusable:i,active:a,children:t.jsx(y.span,{tabIndex:f?0:-1,"data-orientation":d.orientation,...c,ref:r,onMouseDown:p(n.onMouseDown,(e=>{i?d.onItemFocus(u):e.preventDefault()})),onFocus:p(n.onFocus,(()=>d.onItemFocus(u))),onKeyDown:p(n.onKeyDown,(e=>{if("Tab"===e.key&&e.shiftKey)return void d.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:Kn[r]}(e,d.orientation,d.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=m().filter((e=>e.focusable)).map((e=>e.ref.current));if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const i=o.indexOf(e.currentTarget);o=d.loop?(r=i+1,(n=o).map(((e,t)=>n[(r+t)%n.length]))):o.slice(i+1)}setTimeout((()=>Un(o)))}var n,r}))})})}));zn.displayName=Vn;var Kn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Un(e,t=!1){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus({preventScroll:t}),document.activeElement!==n)return}}var Yn=Bn,Xn=zn,qn="Tabs",[Gn,Zn]=v(qn,[Fn]),Jn=Fn(),[Qn,er]=Gn(qn),tr=e.forwardRef(((e,n)=>{const{__scopeTabs:r,value:o,onValueChange:i,defaultValue:a,orientation:s="horizontal",dir:c,activationMode:l="automatic",...u}=e,d=Xt(c),[f,p]=I({prop:o,onChange:i,defaultProp:a});return t.jsx(Qn,{scope:r,baseId:$(),value:f,onValueChange:p,orientation:s,dir:d,activationMode:l,children:t.jsx(y.div,{dir:d,"data-orientation":s,...u,ref:n})})}));tr.displayName=qn;var nr="TabsList",rr=e.forwardRef(((e,n)=>{const{__scopeTabs:r,loop:o=!0,...i}=e,a=er(nr,r),s=Jn(r);return t.jsx(Yn,{asChild:!0,...s,orientation:a.orientation,dir:a.dir,loop:o,children:t.jsx(y.div,{role:"tablist","aria-orientation":a.orientation,...i,ref:n})})}));rr.displayName=nr;var or="TabsTrigger",ir=e.forwardRef(((e,n)=>{const{__scopeTabs:r,value:o,disabled:i=!1,...a}=e,s=er(or,r),c=Jn(r),l=cr(s.baseId,o),u=lr(s.baseId,o),d=o===s.value;return t.jsx(Xn,{asChild:!0,...c,focusable:!i,active:d,children:t.jsx(y.button,{type:"button",role:"tab","aria-selected":d,"aria-controls":u,"data-state":d?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:l,...a,ref:n,onMouseDown:p(e.onMouseDown,(e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(o)})),onKeyDown:p(e.onKeyDown,(e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(o)})),onFocus:p(e.onFocus,(()=>{const e="manual"!==s.activationMode;d||i||!e||s.onValueChange(o)}))})})}));ir.displayName=or;var ar="TabsContent",sr=e.forwardRef(((n,r)=>{const{__scopeTabs:o,value:i,forceMount:a,children:s,...c}=n,l=er(ar,o),u=cr(l.baseId,i),d=lr(l.baseId,i),f=i===l.value,p=e.useRef(f);return e.useEffect((()=>{const e=requestAnimationFrame((()=>p.current=!1));return()=>cancelAnimationFrame(e)}),[]),t.jsx(M,{present:a||f,children:({present:e})=>t.jsx(y.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":u,hidden:!e,id:d,tabIndex:0,...c,ref:r,style:{...n.style,animationDuration:p.current?"0s":void 0},children:e&&s})})}));function cr(e,t){return`${e}-trigger-${t}`}function lr(e,t){return`${e}-content-${t}`}sr.displayName=ar;var ur=tr,dr=rr,fr=ir,pr=sr;const mr=["top","right","bottom","left"],hr=Math.min,vr=Math.max,gr=Math.round,yr=Math.floor,wr=e=>({x:e,y:e}),br={left:"right",right:"left",bottom:"top",top:"bottom"},xr={start:"end",end:"start"};function Er(e,t,n){return vr(e,hr(t,n))}function Rr(e,t){return"function"==typeof e?e(t):e}function Cr(e){return e.split("-")[0]}function Ar(e){return e.split("-")[1]}function Sr(e){return"x"===e?"y":"x"}function Nr(e){return"y"===e?"height":"width"}function Dr(e){return["top","bottom"].includes(Cr(e))?"y":"x"}function Pr(e){return Sr(Dr(e))}function _r(e){return e.replace(/start|end/g,(e=>xr[e]))}function Or(e){return e.replace(/left|right|bottom|top/g,(e=>br[e]))}function Tr(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function jr(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Mr(e,t,n){let{reference:r,floating:o}=e;const i=Dr(t),a=Pr(t),s=Nr(a),c=Cr(t),l="y"===i,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[s]/2-o[s]/2;let p;switch(c){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Ar(t)){case"start":p[a]-=f*(n&&l?-1:1);break;case"end":p[a]+=f*(n&&l?-1:1)}return p}async function Lr(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:i,rects:a,elements:s,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Rr(t,e),m=Tr(p),h=s[f?"floating"===d?"reference":"floating":d],v=jr(await i.getClippingRect({element:null==(n=await(null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await(null==i.getDocumentElement?void 0:i.getDocumentElement(s.floating)),boundary:l,rootBoundary:u,strategy:c})),g="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await(null==i.getOffsetParent?void 0:i.getOffsetParent(s.floating)),w=await(null==i.isElement?void 0:i.isElement(y))&&await(null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=jr(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:y,strategy:c}):g);return{top:(v.top-b.top+m.top)/w.y,bottom:(b.bottom-v.bottom+m.bottom)/w.y,left:(v.left-b.left+m.left)/w.x,right:(b.right-v.right+m.right)/w.x}}function Ir(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function kr(e){return mr.some((t=>e[t]>=0))}function Fr(){return"undefined"!=typeof window}function Wr(e){return Hr(e)?(e.nodeName||"").toLowerCase():"#document"}function $r(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Br(e){var t;return null==(t=(Hr(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Hr(e){return!!Fr()&&(e instanceof Node||e instanceof $r(e).Node)}function Vr(e){return!!Fr()&&(e instanceof Element||e instanceof $r(e).Element)}function zr(e){return!!Fr()&&(e instanceof HTMLElement||e instanceof $r(e).HTMLElement)}function Kr(e){return!(!Fr()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof $r(e).ShadowRoot)}function Ur(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Jr(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Yr(e){return["table","td","th"].includes(Wr(e))}function Xr(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function qr(e){const t=Gr(),n=Vr(e)?Jr(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Gr(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Zr(e){return["html","body","#document"].includes(Wr(e))}function Jr(e){return $r(e).getComputedStyle(e)}function Qr(e){return Vr(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eo(e){if("html"===Wr(e))return e;const t=e.assignedSlot||e.parentNode||Kr(e)&&e.host||Br(e);return Kr(t)?t.host:t}function to(e){const t=eo(e);return Zr(t)?e.ownerDocument?e.ownerDocument.body:e.body:zr(t)&&Ur(t)?t:to(t)}function no(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=to(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=$r(o);if(i){const e=ro(a);return t.concat(a,a.visualViewport||[],Ur(o)?o:[],e&&n?no(e):[])}return t.concat(o,no(o,[],n))}function ro(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function oo(e){const t=Jr(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=zr(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,s=gr(n)!==i||gr(r)!==a;return s&&(n=i,r=a),{width:n,height:r,$:s}}function io(e){return Vr(e)?e:e.contextElement}function ao(e){const t=io(e);if(!zr(t))return wr(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=oo(t);let a=(i?gr(n.width):n.width)/r,s=(i?gr(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}const so=wr(0);function co(e){const t=$r(e);return Gr()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:so}function lo(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),i=io(e);let a=wr(1);t&&(r?Vr(r)&&(a=ao(r)):a=ao(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==$r(e))&&t}(i,n,r)?co(i):wr(0);let c=(o.left+s.x)/a.x,l=(o.top+s.y)/a.y,u=o.width/a.x,d=o.height/a.y;if(i){const e=$r(i),t=r&&Vr(r)?$r(r):r;let n=e,o=ro(n);for(;o&&r&&t!==n;){const e=ao(o),t=o.getBoundingClientRect(),r=Jr(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,l*=e.y,u*=e.x,d*=e.y,c+=i,l+=a,n=$r(o),o=ro(n)}}return jr({width:u,height:d,x:c,y:l})}function uo(e,t){const n=Qr(e).scrollLeft;return t?t.left+n:lo(Br(e)).left+n}function fo(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=$r(e),r=Br(e),o=n.visualViewport;let i=r.clientWidth,a=r.clientHeight,s=0,c=0;if(o){i=o.width,a=o.height;const e=Gr();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,c=o.offsetTop)}return{width:i,height:a,x:s,y:c}}(e,n);else if("document"===t)r=function(e){const t=Br(e),n=Qr(e),r=e.ownerDocument.body,o=vr(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=vr(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let a=-n.scrollLeft+uo(e);const s=-n.scrollTop;return"rtl"===Jr(r).direction&&(a+=vr(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:s}}(Br(e));else if(Vr(t))r=function(e,t){const n=lo(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=zr(e)?ao(e):wr(1);return{width:e.clientWidth*i.x,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{const n=co(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return jr(r)}function po(e,t){const n=eo(e);return!(n===t||!Vr(n)||Zr(n))&&("fixed"===Jr(n).position||po(n,t))}function mo(e,t,n){const r=zr(t),o=Br(t),i="fixed"===n,a=lo(e,!0,i,t);let s={scrollLeft:0,scrollTop:0};const c=wr(0);if(r||!r&&!i)if(("body"!==Wr(t)||Ur(o))&&(s=Qr(t)),r){const e=lo(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=uo(o));let l=0,u=0;if(o&&!r&&!i){const e=o.getBoundingClientRect();u=e.top+s.scrollTop,l=e.left+s.scrollLeft-uo(o,e)}return{x:a.left+s.scrollLeft-c.x-l,y:a.top+s.scrollTop-c.y-u,width:a.width,height:a.height}}function ho(e){return"static"===Jr(e).position}function vo(e,t){if(!zr(e)||"fixed"===Jr(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Br(e)===n&&(n=n.ownerDocument.body),n}function go(e,t){const n=$r(e);if(Xr(e))return n;if(!zr(e)){let t=eo(e);for(;t&&!Zr(t);){if(Vr(t)&&!ho(t))return t;t=eo(t)}return n}let r=vo(e,t);for(;r&&Yr(r)&&ho(r);)r=vo(r,t);return r&&Zr(r)&&ho(r)&&!qr(r)?n:r||function(e){let t=eo(e);for(;zr(t)&&!Zr(t);){if(qr(t))return t;if(Xr(t))return null;t=eo(t)}return null}(e)||n}const yo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i="fixed"===o,a=Br(r),s=!!t&&Xr(t.floating);if(r===a||s&&i)return n;let c={scrollLeft:0,scrollTop:0},l=wr(1);const u=wr(0),d=zr(r);if((d||!d&&!i)&&(("body"!==Wr(r)||Ur(a))&&(c=Qr(r)),zr(r))){const e=lo(r);l=ao(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x,y:n.y*l.y-c.scrollTop*l.y+u.y}},getDocumentElement:Br,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[..."clippingAncestors"===n?Xr(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=no(e,[],!1).filter((e=>Vr(e)&&"body"!==Wr(e))),o=null;const i="fixed"===Jr(e).position;let a=i?eo(e):e;for(;Vr(a)&&!Zr(a);){const t=Jr(a),n=qr(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||Ur(a)&&!n&&po(e,a))?r=r.filter((e=>e!==a)):o=t,a=eo(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],s=i.reduce(((e,n)=>{const r=fo(t,n,o);return e.top=vr(r.top,e.top),e.right=hr(r.right,e.right),e.bottom=hr(r.bottom,e.bottom),e.left=vr(r.left,e.left),e}),fo(t,a,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:go,getElementRects:async function(e){const t=this.getOffsetParent||go,n=this.getDimensions,r=await n(e.floating);return{reference:mo(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=oo(e);return{width:t,height:n}},getScale:ao,isElement:Vr,isRTL:function(e){return"rtl"===Jr(e).direction}};function wo(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,l=io(e),u=o||i?[...l?no(l):[],...no(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),i&&e.addEventListener("resize",n)}));const d=l&&s?function(e,t){let n,r=null;const o=Br(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function a(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),i();const{left:l,top:u,width:d,height:f}=e.getBoundingClientRect();if(s||t(),!d||!f)return;const p={rootMargin:-yr(u)+"px "+-yr(o.clientWidth-(l+d))+"px "+-yr(o.clientHeight-(u+f))+"px "+-yr(l)+"px",threshold:vr(0,hr(1,c))||1};let m=!0;function h(e){const t=e[0].intersectionRatio;if(t!==c){if(!m)return a();t?a(!1,t):n=setTimeout((()=>{a(!1,1e-7)}),1e3)}m=!1}try{r=new IntersectionObserver(h,{...p,root:o.ownerDocument})}catch(v){r=new IntersectionObserver(h,p)}r.observe(e)}(!0),i}(l,n):null;let f,p=-1,m=null;a&&(m=new ResizeObserver((e=>{let[r]=e;r&&r.target===l&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=m)||e.observe(t)}))),n()})),l&&!c&&m.observe(l),m.observe(t));let h=c?lo(e):null;return c&&function t(){const r=lo(e);!h||r.x===h.x&&r.y===h.y&&r.width===h.width&&r.height===h.height||n();h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),i&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=m)||e.disconnect(),m=null,c&&cancelAnimationFrame(f)}}const bo=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:a,middlewareData:s}=t,c=await async function(e,t){const{placement:n,platform:r,elements:o}=e,i=await(null==r.isRTL?void 0:r.isRTL(o.floating)),a=Cr(n),s=Ar(n),c="y"===Dr(n),l=["left","top"].includes(a)?-1:1,u=i&&c?-1:1,d=Rr(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof m&&(p="end"===s?-1*m:m),c?{x:p*u,y:f*l}:{x:f*l,y:p*u}}(t,e);return a===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:i+c.y,data:{...c,placement:a}}}}},xo=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=Rr(e,t),l={x:n,y:r},u=await Lr(t,c),d=Dr(Cr(o)),f=Sr(d);let p=l[f],m=l[d];if(i){const e="y"===f?"bottom":"right";p=Er(p+u["y"===f?"top":"left"],p,p-u[e])}if(a){const e="y"===d?"bottom":"right";m=Er(m+u["y"===d?"top":"left"],m,m-u[e])}const h=s.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:a}}}}}},Eo=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:a,initialPlacement:s,platform:c,elements:l}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:m="none",flipAlignment:h=!0,...v}=Rr(e,t);if(null!=(n=i.arrow)&&n.alignmentOffset)return{};const g=Cr(o),y=Dr(s),w=Cr(s)===s,b=await(null==c.isRTL?void 0:c.isRTL(l.floating)),x=f||(w||!h?[Or(s)]:function(e){const t=Or(e);return[_r(e),t,_r(t)]}(s)),E="none"!==m;!f&&E&&x.push(...function(e,t,n,r){const o=Ar(e);let i=function(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],a=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:a;default:return[]}}(Cr(e),"start"===n,r);return o&&(i=i.map((e=>e+"-"+o)),t&&(i=i.concat(i.map(_r)))),i}(s,h,m,b));const R=[s,...x],C=await Lr(t,v),A=[];let S=(null==(r=i.flip)?void 0:r.overflows)||[];if(u&&A.push(C[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=Ar(e),o=Pr(e),i=Nr(o);let a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=Or(a)),[a,Or(a)]}(o,a,b);A.push(C[e[0]],C[e[1]])}if(S=[...S,{placement:o,overflows:A}],!A.every((e=>e<=0))){var N,D;const e=((null==(N=i.flip)?void 0:N.index)||0)+1,t=R[e];if(t)return{data:{index:e,overflows:S},reset:{placement:t}};let n=null==(D=S.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:D.placement;if(!n)switch(p){case"bestFit":{var P;const e=null==(P=S.filter((e=>{if(E){const t=Dr(e.placement);return t===y||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:P[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},Ro=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:a,elements:s}=t,{apply:c=()=>{},...l}=Rr(e,t),u=await Lr(t,l),d=Cr(o),f=Ar(o),p="y"===Dr(o),{width:m,height:h}=i.floating;let v,g;"top"===d||"bottom"===d?(v=d,g=f===(await(null==a.isRTL?void 0:a.isRTL(s.floating))?"start":"end")?"left":"right"):(g=d,v="end"===f?"top":"bottom");const y=h-u.top-u.bottom,w=m-u.left-u.right,b=hr(h-u[v],y),x=hr(m-u[g],w),E=!t.middlewareData.shift;let R=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),E&&!f){const e=vr(u.left,0),t=vr(u.right,0),n=vr(u.top,0),r=vr(u.bottom,0);p?C=m-2*(0!==e||0!==t?e+t:vr(u.left,u.right)):R=h-2*(0!==n||0!==r?n+r:vr(u.top,u.bottom))}await c({...t,availableWidth:C,availableHeight:R});const A=await a.getDimensions(s.floating);return m!==A.width||h!==A.height?{reset:{rects:!0}}:{}}}},Co=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Rr(e,t);switch(r){case"referenceHidden":{const e=Ir(await Lr(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:kr(e)}}}case"escaped":{const e=Ir(await Lr(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:kr(e)}}}default:return{}}}}},Ao=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:a,elements:s,middlewareData:c}=t,{element:l,padding:u=0}=Rr(e,t)||{};if(null==l)return{};const d=Tr(u),f={x:n,y:r},p=Pr(o),m=Nr(p),h=await a.getDimensions(l),v="y"===p,g=v?"top":"left",y=v?"bottom":"right",w=v?"clientHeight":"clientWidth",b=i.reference[m]+i.reference[p]-f[p]-i.floating[m],x=f[p]-i.reference[p],E=await(null==a.getOffsetParent?void 0:a.getOffsetParent(l));let R=E?E[w]:0;R&&await(null==a.isElement?void 0:a.isElement(E))||(R=s.floating[w]||i.floating[m]);const C=b/2-x/2,A=R/2-h[m]/2-1,S=hr(d[g],A),N=hr(d[y],A),D=S,P=R-h[m]-N,_=R/2-h[m]/2+C,O=Er(D,_,P),T=!c.arrow&&null!=Ar(o)&&_!==O&&i.reference[m]/2-(_<D?S:N)-h[m]/2<0,j=T?_<D?_-D:_-P:0;return{[p]:f[p]+j,data:{[p]:O,centerOffset:_-O-j,...T&&{alignmentOffset:j}},reset:T}}}),So=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=Rr(e,t),u={x:n,y:r},d=Dr(o),f=Sr(d);let p=u[f],m=u[d];const h=Rr(s,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(l){var g,y;const e="y"===f?"width":"height",t=["top","left"].includes(Cr(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}},No=(e,t,n)=>{const r=new Map,o={platform:yo,...n},i={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,s=i.filter(Boolean),c=await(null==a.isRTL?void 0:a.isRTL(t));let l=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=Mr(l,r,c),f=r,p={},m=0;for(let h=0;h<s.length;h++){const{name:n,fn:i}=s[h],{x:v,y:g,data:y,reset:w}=await i({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:l,platform:a,elements:{reference:e,floating:t}});u=null!=v?v:u,d=null!=g?g:d,p={...p,[n]:{...p[n],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(l=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:u,y:d}=Mr(l,f,c))),h=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:i})};var Do="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function Po(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!Po(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!Po(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function _o(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Oo(e,t){const n=_o(e);return Math.round(t*n)/n}function To(t){const n=e.useRef(t);return Do((()=>{n.current=t})),n}const jo=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Ao({element:n.current,padding:r}).fn(t):{}:n?Ao({element:n,padding:r}).fn(t):{};var o}}),Mo=(e,t)=>({...bo(e),options:[e,t]}),Lo=(e,t)=>({...xo(e),options:[e,t]}),Io=(e,t)=>({...So(e),options:[e,t]}),ko=(e,t)=>({...Eo(e),options:[e,t]}),Fo=(e,t)=>({...Ro(e),options:[e,t]}),Wo=(e,t)=>({...Co(e),options:[e,t]}),$o=(e,t)=>({...jo(e),options:[e,t]});var Bo=e.forwardRef(((e,n)=>{const{children:r,width:o=10,height:i=5,...a}=e;return t.jsx(y.svg,{...a,ref:n,width:o,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:t.jsx("polygon",{points:"0,0 30,0 15,10"})})}));Bo.displayName="Arrow";var Ho=Bo;function Vo(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var zo="Popper",[Ko,Uo]=function(n,r=[]){let o=[];const i=()=>{const t=o.map((t=>e.createContext(t)));return function(r){const o=r?.[n]||t;return e.useMemo((()=>({[`__scope${n}`]:{...r,[n]:o}})),[r,o])}};return i.scopeName=n,[function(r,i){const a=e.createContext(i),s=o.length;function c(r){const{scope:o,children:i,...c}=r,l=o?.[n][s]||a,u=e.useMemo((()=>c),Object.values(c));return t.jsx(l.Provider,{value:u,children:i})}return o=[...o,i],c.displayName=r+"Provider",[c,function(t,o){const c=o?.[n][s]||a,l=e.useContext(c);if(l)return l;if(void 0!==i)return i;throw new Error(`\`${t}\` must be used within \`${r}\``)}]},Vo(i,...r)]}(zo),[Yo,Xo]=Ko(zo),qo=n=>{const{__scopePopper:r,children:o}=n,[i,a]=e.useState(null);return t.jsx(Yo,{scope:r,anchor:i,onAnchorChange:a,children:o})};qo.displayName=zo;var Go="PopperAnchor",Zo=e.forwardRef(((n,r)=>{const{__scopePopper:o,virtualRef:i,...a}=n,c=Xo(Go,o),l=e.useRef(null),u=s(r,l);return e.useEffect((()=>{c.onAnchorChange(i?.current||l.current)})),i?null:t.jsx(y.div,{...a,ref:u})}));Zo.displayName=Go;var Jo="PopperContent",[Qo,ei]=Ko(Jo),ti=e.forwardRef(((n,o)=>{const{__scopePopper:i,side:a="bottom",sideOffset:c=0,align:l="center",alignOffset:u=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:w,...x}=n,E=Xo(Jo,i),[R,C]=e.useState(null),A=s(o,(e=>C(e))),[S,N]=e.useState(null),D=function(t){const[n,r]=e.useState(void 0);return T((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,i;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,i=t.blockSize}else o=t.offsetWidth,i=t.offsetHeight;r({width:o,height:i})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}(S),P=D?.width??0,_=D?.height??0,O=a+("center"!==l?"-"+l:""),j="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},M=Array.isArray(p)?p:[p],L=M.length>0,I={padding:j,boundary:M.filter(ii),altBoundary:L},{refs:k,floatingStyles:F,placement:W,isPositioned:$,middlewareData:B}=function(t){void 0===t&&(t={});const{placement:n="bottom",strategy:o="absolute",middleware:i=[],platform:a,elements:{reference:s,floating:c}={},transform:l=!0,whileElementsMounted:u,open:d}=t,[f,p]=e.useState({x:0,y:0,strategy:o,placement:n,middlewareData:{},isPositioned:!1}),[m,h]=e.useState(i);Po(m,i)||h(i);const[v,g]=e.useState(null),[y,w]=e.useState(null),b=e.useCallback((e=>{e!==C.current&&(C.current=e,g(e))}),[]),x=e.useCallback((e=>{e!==A.current&&(A.current=e,w(e))}),[]),E=s||v,R=c||y,C=e.useRef(null),A=e.useRef(null),S=e.useRef(f),N=null!=u,D=To(u),P=To(a),_=To(d),O=e.useCallback((()=>{if(!C.current||!A.current)return;const e={placement:n,strategy:o,middleware:m};P.current&&(e.platform=P.current),No(C.current,A.current,e).then((e=>{const t={...e,isPositioned:!1!==_.current};T.current&&!Po(S.current,t)&&(S.current=t,r.flushSync((()=>{p(t)})))}))}),[m,n,o,P,_]);Do((()=>{!1===d&&S.current.isPositioned&&(S.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const T=e.useRef(!1);Do((()=>(T.current=!0,()=>{T.current=!1})),[]),Do((()=>{if(E&&(C.current=E),R&&(A.current=R),E&&R){if(D.current)return D.current(E,R,O);O()}}),[E,R,O,D,N]);const j=e.useMemo((()=>({reference:C,floating:A,setReference:b,setFloating:x})),[b,x]),M=e.useMemo((()=>({reference:E,floating:R})),[E,R]),L=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!M.floating)return e;const t=Oo(M.floating,f.x),n=Oo(M.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",..._o(M.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,l,M.floating,f.x,f.y]);return e.useMemo((()=>({...f,update:O,refs:j,elements:M,floatingStyles:L})),[f,O,j,M,L])}({strategy:"fixed",placement:O,whileElementsMounted:(...e)=>wo(...e,{animationFrame:"always"===g}),elements:{reference:E.anchor},middleware:[Mo({mainAxis:c+_,alignmentAxis:u}),f&&Lo({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?Io():void 0,...I}),f&&ko({...I}),Fo({...I,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),S&&$o({element:S,padding:d}),ai({arrowWidth:P,arrowHeight:_}),v&&Wo({strategy:"referenceHidden",...I})]}),[H,V]=si(W),z=b(w);T((()=>{$&&z?.()}),[$,z]);const K=B.arrow?.x,U=B.arrow?.y,Y=0!==B.arrow?.centerOffset,[X,q]=e.useState();return T((()=>{R&&q(window.getComputedStyle(R).zIndex)}),[R]),t.jsx("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:$?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:n.dir,children:t.jsx(Qo,{scope:i,placedSide:H,onArrowChange:N,arrowX:K,arrowY:U,shouldHideArrow:Y,children:t.jsx(y.div,{"data-side":H,"data-align":V,...x,ref:A,style:{...x.style,animation:$?void 0:"none"}})})})}));ti.displayName=Jo;var ni="PopperArrow",ri={top:"bottom",right:"left",bottom:"top",left:"right"},oi=e.forwardRef((function(e,n){const{__scopePopper:r,...o}=e,i=ei(ni,r),a=ri[i.placedSide];return t.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:t.jsx(Ho,{...o,ref:n,style:{...o.style,display:"block"}})})}));function ii(e){return null!==e}oi.displayName=ni;var ai=e=>({name:"transformOrigin",options:e,fn(t){const{placement:n,rects:r,middlewareData:o}=t,i=0!==o.arrow?.centerOffset,a=i?0:e.arrowWidth,s=i?0:e.arrowHeight,[c,l]=si(n),u={start:"0%",center:"50%",end:"100%"}[l],d=(o.arrow?.x??0)+a/2,f=(o.arrow?.y??0)+s/2;let p="",m="";return"bottom"===c?(p=i?u:`${d}px`,m=-s+"px"):"top"===c?(p=i?u:`${d}px`,m=`${r.floating.height+s}px`):"right"===c?(p=-s+"px",m=i?u:`${f}px`):"left"===c&&(p=`${r.floating.width+s}px`,m=i?u:`${f}px`),{data:{x:p,y:m}}}});function si(e){const[t,n="center"]=e.split("-");return[t,n]}var ci=qo,li=Zo,ui=ti,di=oi;function fi(t){const n=e.useRef({value:t,previous:t});return e.useMemo((()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous)),[t])}export{li as A,O as B,Dt as C,_t as D,di as E,z as F,fi as G,Nn as H,Sn as I,Xt as J,ci as K,dr as L,Nt as O,y as P,_ as R,c as S,Pt as T,k as V,v as a,I as b,h as c,M as d,b as e,p as f,j as g,T as h,w as i,Ot as j,St as k,At as l,Dn as m,Pn as n,An as o,fr as p,pr as q,ur as r,Uo as s,Ye as t,s as u,J as v,$e as w,S as x,ui as y,$ as z};
