import{j as e}from"./vendor-DEz8SL3m.js";import{m as r}from"./index-DeIkHZyv.js";const i=({name:i,description:t,url:n,provider:s="BlackVeil",areaServed:a="New Zealand",serviceType:c="Cybersecurity Service"})=>{const o={"@context":"https://schema.org","@type":"Service",name:i,description:t,provider:{"@type":"Organization",name:s,url:"https://blackveil.co.nz"},serviceType:c,areaServed:a,url:n};return e.jsx(r,{children:e.jsx("script",{type:"application/ld+json",children:JSON.stringify(o)})})};export{i as S};
