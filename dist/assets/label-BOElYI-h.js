import{r as e,j as a,s}from"./vendor-DEz8SL3m.js";import{P as o}from"./ui-BD6Ux9Dl.js";import{c as r}from"./core-ClMcqRvE.js";var t=e.forwardRef(((e,s)=>a.jsx(o.label,{...e,ref:s,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}})));t.displayName="Label";var l=t;const d=s("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),n=e.forwardRef((({className:e,...s},o)=>a.jsx(l,{ref:o,className:r(d(),e),...s})));n.displayName=l.displayName;export{n as L};
