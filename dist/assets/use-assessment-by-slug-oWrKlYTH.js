import{r as e,j as s,aj as t,av as r,a1 as a,q as n,S as i,T as l,E as o}from"./vendor-DEz8SL3m.js";import{n as c,u as d,C as m,a as u,b as h,c as x,d as p,o as g}from"./index-DeIkHZyv.js";import{P as b}from"./ui-BD6Ux9Dl.js";import{c as f,s as _}from"./core-ClMcqRvE.js";function w(...s){const t=s[0];if(1===s.length)return t;const r=()=>{const r=s.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(s){const a=r.reduce(((e,{useScope:t,scopeName:r})=>({...e,...t(s)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${t.scopeName}`]:a})),[a])}};return r.scopeName=t.scopeName,r}var v="Progress",[y,j]=function(t,r=[]){let a=[];const n=()=>{const s=a.map((s=>e.createContext(s)));return function(r){const a=r?.[t]||s;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:a}})),[r,a])}};return n.scopeName=t,[function(r,n){const i=e.createContext(n),l=a.length;function o(r){const{scope:a,children:n,...o}=r,c=a?.[t][l]||i,d=e.useMemo((()=>o),Object.values(o));return s.jsx(c.Provider,{value:d,children:n})}return a=[...a,n],o.displayName=r+"Provider",[o,function(s,a){const o=a?.[t][l]||i,c=e.useContext(o);if(c)return c;if(void 0!==n)return n;throw new Error(`\`${s}\` must be used within \`${r}\``)}]},w(n,...r)]}(v),[N,k]=y(v),S=e.forwardRef(((e,t)=>{const{__scopeProgress:r,value:a=null,max:n,getValueLabel:i=q,...l}=e;(n||0===n)&&P(n);const o=P(n)?n:100;null!==a&&E(a,o);const c=E(a,o)?a:null,d=M(c)?i(c,o):void 0;return s.jsx(N,{scope:r,value:c,max:o,children:s.jsx(b.div,{"aria-valuemax":o,"aria-valuemin":0,"aria-valuenow":M(c)?c:void 0,"aria-valuetext":d,role:"progressbar","data-state":I(c,o),"data-value":c??void 0,"data-max":o,...l,ref:t})})}));S.displayName=v;var C="ProgressIndicator",A=e.forwardRef(((e,t)=>{const{__scopeProgress:r,...a}=e,n=k(C,r);return s.jsx(b.div,{"data-state":I(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...a,ref:t})}));function q(e,s){return`${Math.round(e/s*100)}%`}function I(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function M(e){return"number"==typeof e}function P(e){return M(e)&&!isNaN(e)&&e>0}function E(e,s){return M(e)&&!isNaN(e)&&e<=s&&e>=0}A.displayName=C;var D=S,R=A;const $=e.forwardRef((({className:e,value:t,...r},a)=>s.jsx(D,{ref:a,className:f("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...r,children:s.jsx(R,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})));$.displayName=D.displayName;const T=s=>{const[t,r]=e.useState(null),[a,n]=e.useState(null),[i,l]=e.useState(!1),{user:o}=c(),d=e.useCallback((async e=>{l(!0);try{const t={assessment_type_id:s,company_name:e.companyName,industry:e.industry,employee_count:e.employeeCount,contact_name:e.contactName,email:e.email,phone:e.phone||null,status:"in_progress",ip_address:null,user_agent:navigator.userAgent,referrer:document.referrer||null,user_id:o?.id||null},{data:a,error:i}=await _.from("assessment_submissions").insert(t).select("id, session_id").single();if(i)throw i;return r(a.id),n(a.session_id),await _.from("assessment_analytics").insert({submission_id:a.id,step_name:"company_info",step_completed_at:(new Date).toISOString(),device_type:/Mobile|Android|iPhone|iPad/.test(navigator.userAgent)?"mobile":"desktop",browser_name:navigator.userAgent.split(" ")[0],screen_resolution:`${screen.width}x${screen.height}`}),a.id}catch(t){throw t}finally{l(!1)}}),[s,o]),m=e.useCallback((async(e,s,r)=>{if(t)try{const a={submission_id:t,question_id:e,selected_option_id:r||null,risk_score:s},{error:n}=await _.from("assessment_answers").upsert(a,{onConflict:"submission_id,question_id"});if(n)throw n;await _.from("assessment_analytics").insert({submission_id:t,step_name:`question_${e}`,step_completed_at:(new Date).toISOString()})}catch(a){throw a}}),[t]),u=e.useCallback(((e,s)=>{const t=[],r=e.reduce(((e,s)=>e+s),0)/e.length;return r>=4?(t.push("Implement immediate security awareness training"),t.push("Conduct urgent security assessment"),t.push("Deploy endpoint detection and response (EDR)")):r>=3?(t.push("Enhance email security with advanced filtering"),t.push("Implement multi-factor authentication"),t.push("Regular security training and testing")):(t.push("Maintain current security posture"),t.push("Consider advanced threat protection"),t.push("Regular security audits and updates")),s.toLowerCase().includes("healthcare")?(t.push("HIPAA compliance assessment"),t.push("Medical device security review")):s.toLowerCase().includes("finance")?(t.push("Financial regulatory compliance check"),t.push("PCI DSS compliance verification")):s.toLowerCase().includes("education")&&(t.push("FERPA compliance review"),t.push("Student data protection enhancement")),t.slice(0,4)}),[]),h=e.useCallback((async e=>{if(t)try{const{data:s}=await _.from("assessment_submissions").select("industry").eq("id",t).single(),r=e.reduce(((e,s)=>e+s),0),a=5*e.length,n=Math.round(r/a*100);let i,l,o;n<=30?(i="LOW",l="low_priority",o=3):n<=60?(i="MEDIUM",l="within_week",o=6):(i="HIGH",l="immediate",o=9);const c=u(e,s?.industry||""),d={submission_id:t,total_risk_score:r,max_possible_score:a,risk_percentage:n,risk_level:i,lead_priority:o,follow_up_urgency:l,top_recommendations:c},{error:m}=await _.from("lead_scores").insert(d);if(m)throw m;const{error:h}=await _.from("assessment_submissions").update({status:"completed",completed_at:(new Date).toISOString()}).eq("id",t);if(h)throw h;await _.from("assessment_analytics").insert({submission_id:t,step_name:"completed",step_completed_at:(new Date).toISOString()})}catch(s){throw s}}),[t,u]);return{submissionId:t,sessionId:a,isSubmitting:i,createSubmission:d,saveAnswer:m,calculateAndSaveScores:h}},O=({questions:l,onComplete:o,assessmentType:c})=>{const[x,p]=e.useState(0),[g,b]=e.useState([]),[f,_]=e.useState([]),{saveAnswer:w,calculateAndSaveScores:v}=T(c.id),{toast:y}=d();e.useEffect((()=>{}),[l.length,x,g.length,c.title]);const j=()=>{window.location.reload()};if(0===l.length)return s.jsx(m,{className:"cyber-gradient-card border border-red-500/30",children:s.jsxs(u,{className:"p-8 text-center",children:[s.jsx(t,{className:"h-12 w-12 text-red-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Assessment Questions Not Available"}),s.jsxs("p",{className:"text-red-400 mb-4",children:['The questions for "',c.title,'" are not yet available.']}),s.jsxs("div",{className:"space-y-2 text-white/70 text-sm mb-6",children:[s.jsx("p",{children:"This might be due to:"}),s.jsxs("ul",{className:"list-disc list-inside space-y-1",children:[s.jsx("li",{children:"Assessment still being configured"}),s.jsx("li",{children:"Temporary system issues"}),s.jsx("li",{children:"Database connectivity problems"})]})]}),s.jsxs(h,{onClick:j,className:"bg-green-bright hover:bg-green-muted text-black font-semibold",children:[s.jsx(r,{className:"h-4 w-4 mr-2"}),"Try Again"]})]})});const N=(x+1)/l.length*100,k=l[x];return s.jsx(m,{className:"cyber-gradient-card border border-green-muted/30",children:s.jsxs(u,{className:"p-8",children:[s.jsxs("div",{className:"mb-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4",children:[s.jsxs("span",{className:"text-green-bright font-medium",children:["Question ",x+1," of ",l.length]}),s.jsx("span",{className:"text-white/70",children:k.category})]}),s.jsx($,{value:N,className:"mb-6"})]}),s.jsxs("div",{className:"mb-8",children:[s.jsx("h2",{className:"text-xl md:text-2xl font-bold mb-6 text-white leading-relaxed",children:k.question_text}),s.jsx("div",{className:"space-y-4",children:k.options.map((e=>s.jsx(h,{variant:"outline",onClick:()=>(async(e,s)=>{const t=[...g,s],r=[...f,e];b(t),_(r);try{await w(l[x].id,s,e)}catch(a){y({title:"Save Error",description:"Your answer was recorded but not saved. Continuing assessment...",variant:"destructive"})}if(x<l.length-1)p((e=>e+1));else try{await v(t),o(t)}catch(a){o(t)}})(e.id,e.risk_score),className:"w-full text-left p-4 h-auto border-green-muted/30 hover:border-green-bright/50 hover:bg-green-dark/20 transition-all duration-200",children:s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"p-1 rounded-full flex-shrink-0 mt-1 "+(e.risk_score<=2?"bg-green-dark/40":e.risk_score<=3?"bg-yellow-500/20":"bg-red-500/20"),children:e.risk_score<=2?s.jsx(a,{className:"h-4 w-4 text-green-bright"}):e.risk_score<=3?s.jsx(n,{className:"h-4 w-4 text-yellow-400"}):s.jsx(i,{className:"h-4 w-4 text-red-400"})}),s.jsx("span",{className:"text-white",children:e.option_text})]})},e.id)))})]}),s.jsx("div",{className:"text-center text-white/60 text-sm",children:"Click on the option that best describes your current situation"})]})})},L=({submissionId:t,onStartNew:r})=>{const[c,b]=e.useState(null),[f,w]=e.useState(!0),[v,y]=e.useState(!1),{toast:j}=d();e.useEffect((()=>{N()}),[t]);const N=async()=>{try{const{data:e,error:s}=await _.from("lead_scores").select("*").eq("submission_id",t).maybeSingle();if(s)throw s;if(e){const s={risk_level:e.risk_level,risk_percentage:e.risk_percentage,lead_priority:e.lead_priority,follow_up_urgency:e.follow_up_urgency||"",top_recommendations:Array.isArray(e.top_recommendations)?e.top_recommendations:[]};b(s),await k()}else setTimeout((()=>{N()}),2e3)}catch(e){j({title:"Error",description:"Failed to load assessment results. Please try again.",variant:"destructive"})}finally{w(!1)}},k=async()=>{if(!v)try{const{error:e}=await _.functions.invoke("send-assessment-results",{body:{submissionId:t}});if(e)throw e;y(!0)}catch(e){}},S=e=>{switch(e){case"LOW":return"text-green-400";case"MEDIUM":return"text-yellow-400";case"HIGH":return"text-red-400";default:return"text-white"}};if(f)return s.jsx(m,{className:"cyber-gradient-card border border-green-muted/30",children:s.jsxs(u,{className:"p-8 text-center",children:[s.jsx("div",{className:"w-16 h-16 border-4 border-t-green-bright border-r-green-muted/30 border-b-green-muted/30 border-l-green-muted/30 rounded-full animate-spin mx-auto mb-4"}),s.jsx("p",{className:"text-white mb-2",children:"Calculating your security risk assessment..."}),s.jsx("p",{className:"text-white/60 text-sm",children:"This may take a few moments"})]})});if(!c)return s.jsx(m,{className:"cyber-gradient-card border border-red-500/30",children:s.jsxs(u,{className:"p-8 text-center",children:[s.jsx(n,{className:"h-16 w-16 text-red-400 mx-auto mb-4"}),s.jsx("p",{className:"text-white mb-4",children:"Unable to calculate assessment results"}),s.jsx(h,{onClick:r,className:"bg-green-bright hover:bg-green-muted text-black",children:"Start New Assessment"})]})});const C=(e=>{switch(e){case"LOW":return a;case"MEDIUM":return n;default:return i}})(c.risk_level),A=(q=c.risk_percentage,{implementedControls:Math.max(5,Math.floor((100-q)/10)),missingControls:Math.floor(q/15)+2,criticalGaps:q>70?Math.floor(q/25):0});var q;const I=Math.min(85,Math.round(.8*c.risk_percentage));return s.jsxs("div",{className:"space-y-6",children:[s.jsxs(m,{className:"cyber-gradient-card border border-green-muted/30",children:[s.jsxs(x,{className:"text-center",children:[s.jsx("div",{className:"flex items-center justify-center mb-4",children:s.jsx(C,{className:`h-16 w-16 ${S(c.risk_level)}`})}),s.jsx(p,{className:"text-2xl text-white",children:"Assessment Complete"}),s.jsx("div",{className:"flex items-center justify-center gap-2 mt-2",children:s.jsxs(g,{variant:"outline",className:`${S(c.risk_level)} border-current text-lg px-4 py-1`,children:[c.risk_level," RISK"]})})]}),s.jsxs(u,{className:"text-center",children:[s.jsxs("div",{className:"text-4xl font-bold text-white mb-2",children:[c.risk_percentage,"%"]}),s.jsx("p",{className:"text-white/70 mb-6",children:"Overall Security Risk Score"}),v&&s.jsx("div",{className:"bg-green-500/20 border border-green-500/30 rounded-lg p-4 mb-6",children:s.jsx("p",{className:"text-green-400 text-sm",children:"✅ Detailed results have been sent to your email"})})]})]}),s.jsxs(m,{className:"cyber-gradient-card border border-green-muted/30",children:[s.jsx(x,{children:s.jsxs(p,{className:"text-white flex items-center gap-2",children:[s.jsx(l,{className:"h-5 w-5 text-green-bright"}),"Risk Reduction Potential"]})}),s.jsx(u,{children:s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-3xl font-bold text-green-bright mb-2",children:["Up to ",I,"%"]}),s.jsx("p",{className:"text-white/70 text-sm",children:"Potential risk reduction with recommended security improvements"})]})})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsx(m,{className:"cyber-gradient-card border border-green-muted/30",children:s.jsxs(u,{className:"p-4 text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-green-bright mb-1",children:A.implementedControls}),s.jsx("div",{className:"text-white/70 text-sm",children:"Controls in Place"})]})}),s.jsx(m,{className:"cyber-gradient-card border border-yellow-500/30",children:s.jsxs(u,{className:"p-4 text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-yellow-400 mb-1",children:A.missingControls}),s.jsx("div",{className:"text-white/70 text-sm",children:"Areas for Improvement"})]})}),s.jsx(m,{className:"cyber-gradient-card border border-red-500/30",children:s.jsxs(u,{className:"p-4 text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-red-400 mb-1",children:A.criticalGaps}),s.jsx("div",{className:"text-white/70 text-sm",children:"Critical Gaps"})]})})]}),c.top_recommendations&&c.top_recommendations.length>0&&s.jsxs(m,{className:"cyber-gradient-card border border-green-muted/30",children:[s.jsx(x,{children:s.jsx(p,{className:"text-white",children:"Priority Recommendations"})}),s.jsx(u,{children:s.jsx("div",{className:"space-y-3",children:c.top_recommendations.map(((e,t)=>s.jsxs("div",{className:"flex items-start gap-3 p-3 bg-green-dark/20 rounded-lg",children:[s.jsx("div",{className:"bg-green-bright text-black rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mt-0.5",children:t+1}),s.jsx("p",{className:"text-white text-sm",children:e})]},t)))})})]}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[s.jsx(h,{onClick:r,variant:"outline",className:"flex-1 border-green-muted text-green-bright hover:bg-green-dark/20",children:"Take Another Assessment"}),s.jsxs(h,{onClick:()=>window.open("/contact","_blank"),className:"flex-1 bg-green-bright hover:bg-green-muted text-black",children:[s.jsx(o,{className:"w-4 h-4 mr-2"}),"Get Professional Help"]})]})]})},G=s=>{const[t,r]=e.useState(null),[a,n]=e.useState([]),[i,l]=e.useState(!0),[o,c]=e.useState(null);return e.useEffect((()=>{(async()=>{if(s)try{l(!0),c(null);const{data:e,error:t}=await _.from("assessment_types").select("*").eq("slug",s).eq("is_active",!0).single();if(t)throw new Error("Assessment not found");r(e);const{data:a,error:i}=await _.from("assessment_questions").select("*").eq("assessment_type_id",e.id).eq("is_active",!0).order("order_index");if(i)throw i;if(!a||0===a.length)return void c("This assessment is not yet available. Questions are being prepared.");const{data:o,error:d}=await _.from("assessment_question_options").select("*").in("question_id",a.map((e=>e.id))).order("question_id, order_index");if(d)throw d;const m=a.map((e=>{const s=o?.filter((s=>s.question_id===e.id))||[];return{...e,options:s}}));n(m)}catch(e){const s=e instanceof Error?e.message:"Failed to load assessment";c(s)}finally{l(!1)}})()}),[s]),{assessmentType:t,questions:a,loading:i,error:o}};export{O as A,T as a,L as b,G as u};
