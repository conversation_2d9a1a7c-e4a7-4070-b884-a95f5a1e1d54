import{r as e,j as r}from"./vendor-DEz8SL3m.js";import{s as o}from"./security-D6XyL6Yo.js";import{c as t}from"./core-ClMcqRvE.js";const s=e.forwardRef((({className:e,type:s,sanitize:i=!0,onChange:a,...n},l)=>r.jsx("input",{type:s,className:t("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:l,onChange:e=>{if(i&&("text"===s||"search"===s||"email"===s||"url"===s)){const r=e.target.value,t=o(r);r!==t&&(e.target.value=t)}a&&a(e)},...n})));s.displayName="Input";export{s as I};
