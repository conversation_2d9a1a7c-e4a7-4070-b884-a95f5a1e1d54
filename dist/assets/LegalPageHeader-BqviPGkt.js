import{j as e}from"./vendor-DEz8SL3m.js";const s=({title:s,lastUpdated:t})=>e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl md:text-4xl font-bold mb-4 cyber-glow-text",children:s}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[e.jsxs("p",{className:"text-white/70 mb-2 sm:mb-0",children:["Last Updated: ",t]}),e.jsx("div",{className:"cyber-tag",children:"Official Document"})]}),e.jsx("div",{className:"cyber-divider mb-8"})]});export{s as L};
