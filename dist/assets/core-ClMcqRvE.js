const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/browser-C1LZzpRw.js","assets/vendor-DEz8SL3m.js"])))=>i.map(i=>d[i]);
import{r as t,j as e,g as s,d as n,e as r,G as i,S as o,Z as a,U as l,R as c,T as h,A as u,L as d,E as p,C as f,f as m,h as g,i as y,k as v}from"./vendor-DEz8SL3m.js";const b=t.createContext({});function w(e){const s=t.useRef(null);return null===s.current&&(s.current=e()),s.current}const x="undefined"!=typeof window,k=x?t.useLayoutEffect:t.useEffect,_=t.createContext(null),T=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class S extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=e.offsetParent,s=t instanceof HTMLElement&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=s-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function P({children:s,isPresent:n,anchorX:r}){const i=t.useId(),o=t.useRef(null),a=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=t.useContext(T);return t.useInsertionEffect((()=>{const{width:t,height:e,top:s,left:c,right:h}=a.current;if(n||!o.current||!t||!e)return;const u="left"===r?`left: ${c}`:`right: ${h}`;o.current.dataset.motionPopId=i;const d=document.createElement("style");return l&&(d.nonce=l),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`\n          [data-motion-pop-id="${i}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            ${u}px !important;\n            top: ${s}px !important;\n          }\n        `),()=>{document.head.removeChild(d)}}),[n]),e.jsx(S,{isPresent:n,childRef:o,sizeRef:a,children:t.cloneElement(s,{ref:o})})}const j=({children:s,initial:n,isPresent:r,onExitComplete:i,custom:o,presenceAffectsLayout:a,mode:l,anchorX:c})=>{const h=w(E),u=t.useId();let d=!0,p=t.useMemo((()=>(d=!1,{id:u,initial:n,isPresent:r,custom:o,onExitComplete:t=>{h.set(t,!0);for(const e of h.values())if(!e)return;i&&i()},register:t=>(h.set(t,!1),()=>h.delete(t))})),[r,h,i]);return a&&d&&(p={...p}),t.useMemo((()=>{h.forEach(((t,e)=>h.set(e,!1)))}),[r]),t.useEffect((()=>{!r&&!h.size&&i&&i()}),[r]),"popLayout"===l&&(s=e.jsx(P,{isPresent:r,anchorX:c,children:s})),e.jsx(_.Provider,{value:p,children:s})};function E(){return new Map}function A(e=!0){const s=t.useContext(_);if(null===s)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=s,o=t.useId();t.useEffect((()=>{if(e)return i(o)}),[e]);const a=t.useCallback((()=>e&&r&&r(o)),[o,r,e]);return!n&&r?[!1,a]:[!0]}const C=t=>t.key||"";function R(e){const s=[];return t.Children.forEach(e,(e=>{t.isValidElement(e)&&s.push(e)})),s}const O=({children:s,custom:n,initial:r=!0,onExitComplete:i,presenceAffectsLayout:o=!0,mode:a="sync",propagate:l=!1,anchorX:c="left"})=>{const[h,u]=A(l),d=t.useMemo((()=>R(s)),[s]),p=l&&!h?[]:d.map(C),f=t.useRef(!0),m=t.useRef(d),g=w((()=>new Map)),[y,v]=t.useState(d),[x,_]=t.useState(d);k((()=>{f.current=!1,m.current=d;for(let t=0;t<x.length;t++){const e=C(x[t]);p.includes(e)?g.delete(e):!0!==g.get(e)&&g.set(e,!1)}}),[x,p.length,p.join("-")]);const T=[];if(d!==y){let t=[...d];for(let e=0;e<x.length;e++){const s=x[e],n=C(s);p.includes(n)||(t.splice(e,0,s),T.push(s))}return"wait"===a&&T.length&&(t=T),_(R(t)),v(d),null}const{forceRender:S}=t.useContext(b);return e.jsx(e.Fragment,{children:x.map((t=>{const s=C(t),y=!(l&&!h)&&(d===x||p.includes(s));return e.jsx(j,{isPresent:y,initial:!(f.current&&!r)&&void 0,custom:n,presenceAffectsLayout:o,mode:a,onExitComplete:y?void 0:()=>{if(!g.has(s))return;g.set(s,!0);let t=!0;g.forEach((e=>{e||(t=!1)})),t&&(S?.(),_(m.current),l&&u?.(),i&&i())},anchorX:c,children:t},s)}))})};function M(t,e){-1===t.indexOf(e)&&t.push(e)}function L(t,e){const s=t.indexOf(e);s>-1&&t.splice(s,1)}const D=(t,e,s)=>s>e?e:s<t?t:s;const I={},$=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),N=t=>/^0[^.\s]+$/u.test(t);function V(t){let e;return()=>(void 0===e&&(e=t()),e)}const U=t=>t,B=(t,e)=>s=>e(t(s)),F=(...t)=>t.reduce(B),z=(t,e,s)=>{const n=e-t;return 0===n?1:(s-t)/n};class W{constructor(){this.subscriptions=[]}add(t){return M(this.subscriptions,t),()=>L(this.subscriptions,t)}notify(t,e,s){const n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,s);else for(let r=0;r<n;r++){const n=this.subscriptions[r];n&&n(t,e,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const q=t=>1e3*t,H=t=>t/1e3;function G(t,e){return e?t*(1e3/e):0}const K=(t,e,s)=>(((1-3*s+3*e)*t+(3*s-6*e))*t+3*e)*t;function J(t,e,s,n){if(t===e&&s===n)return U;const r=e=>function(t,e,s,n,r){let i,o,a=0;do{o=e+(s-e)/2,i=K(o,n,r)-t,i>0?s=o:e=o}while(Math.abs(i)>1e-7&&++a<12);return o}(e,0,1,t,s);return t=>0===t||1===t?t:K(r(t),e,n)}const Y=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,X=t=>e=>1-t(1-e),Z=J(.33,1.53,.69,.99),Q=X(Z),tt=Y(Q),et=t=>(t*=2)<1?.5*Q(t):.5*(2-Math.pow(2,-10*(t-1))),st=t=>1-Math.sin(Math.acos(t)),nt=X(st),rt=Y(st),it=J(.42,0,1,1),ot=J(0,0,.58,1),at=J(.42,0,.58,1),lt=t=>Array.isArray(t)&&"number"==typeof t[0],ct={linear:U,easeIn:it,easeInOut:at,easeOut:ot,circIn:st,circInOut:rt,circOut:nt,backIn:Q,backInOut:tt,backOut:Z,anticipate:et},ht=t=>{if(lt(t)){t.length;const[e,s,n,r]=t;return J(e,s,n,r)}return"string"==typeof t?ct[t]:t},ut=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],dt={value:null,addProjectionMetrics:null};function pt(t,e){let s=!1,n=!0;const r={delta:0,timestamp:0,isProcessing:!1},i=()=>s=!0,o=ut.reduce(((t,s)=>(t[s]=function(t,e){let s=new Set,n=new Set,r=!1,i=!1;const o=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}const h={schedule:(t,e=!1,i=!1)=>{const a=i&&r?s:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{a=t,r?i=!0:(r=!0,[s,n]=[n,s],s.forEach(c),e&&dt.value&&dt.value.frameloop[e].push(l),l=0,s.clear(),r=!1,i&&(i=!1,h.process(t)))}};return h}(i,e?s:void 0),t)),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:h,update:u,preRender:d,render:p,postRender:f}=o,m=()=>{const i=I.useManualTiming?r.timestamp:performance.now();s=!1,I.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(i-r.timestamp,40),1)),r.timestamp=i,r.isProcessing=!0,a.process(r),l.process(r),c.process(r),h.process(r),u.process(r),d.process(r),p.process(r),f.process(r),r.isProcessing=!1,s&&e&&(n=!1,t(m))};return{schedule:ut.reduce(((e,i)=>{const a=o[i];return e[i]=(e,i=!1,o=!1)=>(s||(s=!0,n=!0,r.isProcessing||t(m)),a.schedule(e,i,o)),e}),{}),cancel:t=>{for(let e=0;e<ut.length;e++)o[ut[e]].cancel(t)},state:r,steps:o}}const{schedule:ft,cancel:mt,state:gt,steps:yt}=pt("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:U,!0);let vt;function bt(){vt=void 0}const wt={now:()=>(void 0===vt&&wt.set(gt.isProcessing||I.useManualTiming?gt.timestamp:performance.now()),vt),set:t=>{vt=t,queueMicrotask(bt)}},xt=t=>e=>"string"==typeof e&&e.startsWith(t),kt=xt("--"),_t=xt("var(--"),Tt=t=>!!_t(t)&&St.test(t.split("/*")[0].trim()),St=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,Pt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},jt={...Pt,transform:t=>D(0,1,t)},Et={...Pt,default:1},At=t=>Math.round(1e5*t)/1e5,Ct=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const Rt=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ot=(t,e)=>s=>Boolean("string"==typeof s&&Rt.test(s)&&s.startsWith(t)||e&&!function(t){return null==t}(s)&&Object.prototype.hasOwnProperty.call(s,e)),Mt=(t,e,s)=>n=>{if("string"!=typeof n)return n;const[r,i,o,a]=n.match(Ct);return{[t]:parseFloat(r),[e]:parseFloat(i),[s]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},Lt={...Pt,transform:t=>Math.round((t=>D(0,255,t))(t))},Dt={test:Ot("rgb","red"),parse:Mt("red","green","blue"),transform:({red:t,green:e,blue:s,alpha:n=1})=>"rgba("+Lt.transform(t)+", "+Lt.transform(e)+", "+Lt.transform(s)+", "+At(jt.transform(n))+")"};const It={test:Ot("#"),parse:function(t){let e="",s="",n="",r="";return t.length>5?(e=t.substring(1,3),s=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),s=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,s+=s,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(s,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:Dt.transform},$t=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Nt=$t("deg"),Vt=$t("%"),Ut=$t("px"),Bt=$t("vh"),Ft=$t("vw"),zt=(()=>({...Vt,parse:t=>Vt.parse(t)/100,transform:t=>Vt.transform(100*t)}))(),Wt={test:Ot("hsl","hue"),parse:Mt("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:s,alpha:n=1})=>"hsla("+Math.round(t)+", "+Vt.transform(At(e))+", "+Vt.transform(At(s))+", "+At(jt.transform(n))+")"},qt={test:t=>Dt.test(t)||It.test(t)||Wt.test(t),parse:t=>Dt.test(t)?Dt.parse(t):Wt.test(t)?Wt.parse(t):It.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?Dt.transform(t):Wt.transform(t)},Ht=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const Gt="number",Kt="color",Jt=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Yt(t){const e=t.toString(),s=[],n={color:[],number:[],var:[]},r=[];let i=0;const o=e.replace(Jt,(t=>(qt.test(t)?(n.color.push(i),r.push(Kt),s.push(qt.parse(t))):t.startsWith("var(")?(n.var.push(i),r.push("var"),s.push(t)):(n.number.push(i),r.push(Gt),s.push(parseFloat(t))),++i,"${}"))).split("${}");return{values:s,split:o,indexes:n,types:r}}function Xt(t){return Yt(t).values}function Zt(t){const{split:e,types:s}=Yt(t),n=e.length;return t=>{let r="";for(let i=0;i<n;i++)if(r+=e[i],void 0!==t[i]){const e=s[i];r+=e===Gt?At(t[i]):e===Kt?qt.transform(t[i]):t[i]}return r}}const Qt=t=>"number"==typeof t?0:t;const te={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Ct)?.length||0)+(t.match(Ht)?.length||0)>0},parse:Xt,createTransformer:Zt,getAnimatableNone:function(t){const e=Xt(t);return Zt(t)(e.map(Qt))}};function ee(t,e,s){return s<0&&(s+=1),s>1&&(s-=1),s<1/6?t+6*(e-t)*s:s<.5?e:s<2/3?t+(e-t)*(2/3-s)*6:t}function se(t,e){return s=>s>0?e:t}const ne=(t,e,s)=>t+(e-t)*s,re=(t,e,s)=>{const n=t*t,r=s*(e*e-n)+n;return r<0?0:Math.sqrt(r)},ie=[It,Dt,Wt];function oe(t){const e=(s=t,ie.find((t=>t.test(s))));var s;if(!Boolean(e))return!1;let n=e.parse(t);return e===Wt&&(n=function({hue:t,saturation:e,lightness:s,alpha:n}){t/=360,s/=100;let r=0,i=0,o=0;if(e/=100){const n=s<.5?s*(1+e):s+e-s*e,a=2*s-n;r=ee(a,n,t+1/3),i=ee(a,n,t),o=ee(a,n,t-1/3)}else r=i=o=s;return{red:Math.round(255*r),green:Math.round(255*i),blue:Math.round(255*o),alpha:n}}(n)),n}const ae=(t,e)=>{const s=oe(t),n=oe(e);if(!s||!n)return se(t,e);const r={...s};return t=>(r.red=re(s.red,n.red,t),r.green=re(s.green,n.green,t),r.blue=re(s.blue,n.blue,t),r.alpha=ne(s.alpha,n.alpha,t),Dt.transform(r))},le=new Set(["none","hidden"]);function ce(t,e){return s=>ne(t,e,s)}function he(t){return"number"==typeof t?ce:"string"==typeof t?Tt(t)?se:qt.test(t)?ae:pe:Array.isArray(t)?ue:"object"==typeof t?qt.test(t)?ae:de:se}function ue(t,e){const s=[...t],n=s.length,r=t.map(((t,s)=>he(t)(t,e[s])));return t=>{for(let e=0;e<n;e++)s[e]=r[e](t);return s}}function de(t,e){const s={...t,...e},n={};for(const r in s)void 0!==t[r]&&void 0!==e[r]&&(n[r]=he(t[r])(t[r],e[r]));return t=>{for(const e in n)s[e]=n[e](t);return s}}const pe=(t,e)=>{const s=te.createTransformer(e),n=Yt(t),r=Yt(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?le.has(t)&&!r.values.length||le.has(e)&&!n.values.length?function(t,e){return le.has(t)?s=>s<=0?t:e:s=>s>=1?e:t}(t,e):F(ue(function(t,e){const s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){const i=e.types[r],o=t.indexes[i][n[i]],a=t.values[o]??0;s[r]=a,n[i]++}return s}(n,r),r.values),s):se(t,e)};function fe(t,e,s){if("number"==typeof t&&"number"==typeof e&&"number"==typeof s)return ne(t,e,s);return he(t)(t,e)}const me=t=>{const e=({timestamp:e})=>t(e);return{start:()=>ft.update(e,!0),stop:()=>mt(e),now:()=>gt.isProcessing?gt.timestamp:wt.now()}},ge=(t,e,s=10)=>{let n="";const r=Math.max(Math.round(e/s),2);for(let i=0;i<r;i++)n+=t(i/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`},ye=2e4;function ve(t){let e=0;let s=t.next(e);for(;!s.done&&e<ye;)e+=50,s=t.next(e);return e>=ye?1/0:e}function be(t,e,s){const n=Math.max(e-5,0);return G(s-t(n),e-n)}const we=100,xe=10,ke=1,_e=0,Te=800,Se=.3,Pe=.3,je={granular:.01,default:2},Ee={granular:.005,default:.5},Ae=.01,Ce=10,Re=.05,Oe=1,Me=.001;function Le({duration:t=Te,bounce:e=Se,velocity:s=_e,mass:n=ke}){let r,i,o=1-e;o=D(Re,Oe,o),t=D(Ae,Ce,H(t)),o<1?(r=e=>{const n=e*o,r=n*t,i=n-s,a=Ie(e,o),l=Math.exp(-r);return Me-i/a*l},i=e=>{const n=e*o*t,i=n*s+s,a=Math.pow(o,2)*Math.pow(e,2)*t,l=Math.exp(-n),c=Ie(Math.pow(e,2),o);return(-r(e)+Me>0?-1:1)*((i-a)*l)/c}):(r=e=>Math.exp(-e*t)*((e-s)*t+1)-.001,i=e=>Math.exp(-e*t)*(t*t*(s-e)));const a=function(t,e,s){let n=s;for(let r=1;r<De;r++)n-=t(n)/e(n);return n}(r,i,5/t);if(t=q(t),isNaN(a))return{stiffness:we,damping:xe,duration:t};{const e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}const De=12;function Ie(t,e){return t*Math.sqrt(1-e*e)}const $e=["duration","bounce"],Ne=["stiffness","damping","mass"];function Ve(t,e){return e.some((e=>void 0!==t[e]))}function Ue(t=Pe,e=Se){const s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:n,restDelta:r}=s;const i=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],a={done:!1,value:i},{stiffness:l,damping:c,mass:h,duration:u,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:_e,stiffness:we,damping:xe,mass:ke,isResolvedFromDuration:!1,...t};if(!Ve(t,Ne)&&Ve(t,$e))if(t.visualDuration){const s=t.visualDuration,n=2*Math.PI/(1.2*s),r=n*n,i=2*D(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:ke,stiffness:r,damping:i}}else{const s=Le(t);e={...e,...s,mass:ke},e.isResolvedFromDuration=!0}return e}({...s,velocity:-H(s.velocity||0)}),f=d||0,m=c/(2*Math.sqrt(l*h)),g=o-i,y=H(Math.sqrt(l/h)),v=Math.abs(g)<5;let b;if(n||(n=v?je.granular:je.default),r||(r=v?Ee.granular:Ee.default),m<1){const t=Ie(y,m);b=e=>{const s=Math.exp(-m*y*e);return o-s*((f+m*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}}else if(1===m)b=t=>o-Math.exp(-y*t)*(g+(f+y*g)*t);else{const t=y*Math.sqrt(m*m-1);b=e=>{const s=Math.exp(-m*y*e),n=Math.min(t*e,300);return o-s*((f+m*y*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}const w={calculatedDuration:p&&u||null,next:t=>{const e=b(t);if(p)a.done=t>=u;else{let s=0===t?f:0;m<1&&(s=0===t?q(f):be(b,t,e));const i=Math.abs(s)<=n,l=Math.abs(o-e)<=r;a.done=i&&l}return a.value=a.done?o:e,a},toString:()=>{const t=Math.min(ve(w),ye),e=ge((e=>w.next(t*e).value),t,30);return t+"ms "+e},toTransition:()=>{}};return w}function Be({keyframes:t,velocity:e=0,power:s=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:l,restDelta:c=.5,restSpeed:h}){const u=t[0],d={done:!1,value:u},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let f=s*e;const m=u+f,g=void 0===o?m:o(m);g!==m&&(f=g-u);const y=t=>-f*Math.exp(-t/n),v=t=>g+y(t),b=t=>{const e=y(t),s=v(t);d.done=Math.abs(e)<=c,d.value=d.done?g:s};let w,x;const k=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(w=t,x=Ue({keyframes:[d.value,p(d.value)],velocity:be(v,t,d.value),damping:r,stiffness:i,restDelta:c,restSpeed:h}))};return k(0),{calculatedDuration:null,next:t=>{let e=!1;return x||void 0!==w||(e=!0,b(t),k(t)),void 0!==w&&t>=w?x.next(t-w):(!e&&b(t),d)}}}function Fe(t,e,{clamp:s=!0,ease:n,mixer:r}={}){const i=t.length;if(e.length,1===i)return()=>e[0];if(2===i&&e[0]===e[1])return()=>e[1];const o=t[0]===t[1];t[0]>t[i-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=function(t,e,s){const n=[],r=s||I.mix||fe,i=t.length-1;for(let o=0;o<i;o++){let s=r(t[o],t[o+1]);if(e){const t=Array.isArray(e)?e[o]||U:e;s=F(t,s)}n.push(s)}return n}(e,n,r),l=a.length,c=s=>{if(o&&s<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(s<t[n+1]);n++);const r=z(t[n],t[n+1],s);return a[n](r)};return s?e=>c(D(t[0],t[i-1],e)):c}function ze(t){const e=[0];return function(t,e){const s=t[t.length-1];for(let n=1;n<=e;n++){const r=z(0,e,n);t.push(ne(s,1,r))}}(e,t.length-1),e}function We({duration:t=300,keyframes:e,times:s,ease:n="easeInOut"}){const r=(t=>Array.isArray(t)&&"number"!=typeof t[0])(n)?n.map(ht):ht(n),i={done:!1,value:e[0]},o=function(t,e){return t.map((t=>t*e))}(s&&s.length===e.length?s:ze(e),t),a=Fe(o,e,{ease:Array.isArray(r)?r:(l=e,c=r,l.map((()=>c||at)).splice(0,l.length-1))});var l,c;return{calculatedDuration:t,next:e=>(i.value=a(e),i.done=e>=t,i)}}Ue.applyToOptions=t=>{const e=function(t,e=100,s){const n=s({...t,keyframes:[0,e]}),r=Math.min(ve(n),ye);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:H(r)}}(t,100,Ue);return t.ease=e.ease,t.duration=q(e.duration),t.type="keyframes",t};const qe=t=>null!==t;function He(t,{repeat:e,repeatType:s="loop"},n,r=1){const i=t.filter(qe),o=r<0||e&&"loop"!==s&&e%2==1?0:i.length-1;return o&&void 0!==n?n:i[o]}const Ge={decay:Be,inertia:Be,tween:We,keyframes:We,spring:Ue};function Ke(t){"string"==typeof t.type&&(t.type=Ge[t.type])}class Je{constructor(){this.count=0,this.updateFinished()}get finished(){return this._finished}updateFinished(){this.count++,this._finished=new Promise((t=>{this.resolve=t}))}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}const Ye=t=>t/100;class Xe extends Je{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:t}=this.options;if(t&&t.updatedAt!==wt.now()&&this.tick(wt.now()),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:e}=this.options;e&&e()},this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){const{options:t}=this;Ke(t);const{type:e=We,repeat:s=0,repeatDelay:n=0,repeatType:r,velocity:i=0}=t;let{keyframes:o}=t;const a=e||We;a!==We&&"number"!=typeof o[0]&&(this.mixKeyframes=F(Ye,fe(o[0],o[1])),o=[0,100]);const l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-i})),null===l.calculatedDuration&&(l.calculatedDuration=ve(l));const{calculatedDuration:c}=l;this.calculatedDuration=c,this.resolvedDuration=c+n,this.totalDuration=this.resolvedDuration*(s+1)-n,this.generator=l}updateTime(t){const e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){const{generator:s,totalDuration:n,mixKeyframes:r,mirroredGenerator:i,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return s.next(0);const{delay:l=0,keyframes:c,repeat:h,repeatType:u,repeatDelay:d,type:p,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);const g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>n;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let v=this.currentTime,b=s;if(h){const t=Math.min(this.currentTime,n)/o;let e=Math.floor(t),s=t%1;!s&&t>=1&&(s=1),1===s&&e--,e=Math.min(e,h+1);Boolean(e%2)&&("reverse"===u?(s=1-s,d&&(s-=d/o)):"mirror"===u&&(b=i)),v=D(0,1,s)*o}const w=y?{done:!1,value:c[0]}:b.next(v);r&&(w.value=r(w.value));let{done:x}=w;y||null===a||(x=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);const k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return k&&p!==Be&&(w.value=He(c,this.options,m,this.speed)),f&&f(w.value),k&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return H(this.calculatedDuration)}get time(){return H(this.currentTime)}set time(t){t=q(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(wt.now());const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=H(this.currentTime))}play(){if(this.isStopped)return;const{driver:t=me,onPlay:e,startTime:s}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const n=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=n):null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime||(this.startTime=s??n),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(wt.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown()}teardown(){this.notifyFinished(),this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),t.observe(this)}}const Ze=t=>180*t/Math.PI,Qe=t=>{const e=Ze(Math.atan2(t[1],t[0]));return es(e)},ts={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:Qe,rotateZ:Qe,skewX:t=>Ze(Math.atan(t[1])),skewY:t=>Ze(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},es=t=>((t%=360)<0&&(t+=360),t),ss=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ns=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),rs={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ss,scaleY:ns,scale:t=>(ss(t)+ns(t))/2,rotateX:t=>es(Ze(Math.atan2(t[6],t[5]))),rotateY:t=>es(Ze(Math.atan2(-t[2],t[0]))),rotateZ:Qe,rotate:Qe,skewX:t=>Ze(Math.atan(t[4])),skewY:t=>Ze(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function is(t){return t.includes("scale")?1:0}function os(t,e){if(!t||"none"===t)return is(e);const s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let n,r;if(s)n=rs,r=s;else{const e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);n=ts,r=e}if(!r)return is(e);const i=n[e],o=r[1].split(",").map(as);return"function"==typeof i?i(o):o[i]}function as(t){return parseFloat(t.trim())}const ls=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],cs=(()=>new Set(ls))(),hs=t=>t===Pt||t===Ut,us=new Set(["x","y","z"]),ds=ls.filter((t=>!us.has(t)));const ps={width:({x:t},{paddingLeft:e="0",paddingRight:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),height:({y:t},{paddingTop:e="0",paddingBottom:s="0"})=>t.max-t.min-parseFloat(e)-parseFloat(s),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>os(e,"x"),y:(t,{transform:e})=>os(e,"y")};ps.translateX=ps.x,ps.translateY=ps.y;const fs=new Set;let ms=!1,gs=!1,ys=!1;function vs(){if(gs){const t=Array.from(fs).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),s=new Map;e.forEach((t=>{const e=function(t){const e=[];return ds.forEach((s=>{const n=t.getValue(s);void 0!==n&&(e.push([s,n.get()]),n.set(s.startsWith("scale")?1:0))})),e}(t);e.length&&(s.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=s.get(t);e&&e.forEach((([e,s])=>{t.getValue(e)?.set(s)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}gs=!1,ms=!1,fs.forEach((t=>t.complete(ys))),fs.clear()}function bs(){fs.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(gs=!0)}))}class ws{constructor(t,e,s,n,r,i=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=s,this.motionValue=n,this.element=r,this.isAsync=i}scheduleResolve(){this.isScheduled=!0,this.isAsync?(fs.add(this),ms||(ms=!0,ft.read(bs),ft.resolveKeyframes(vs))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:s,motionValue:n}=this;if(null===t[0]){const r=n?.get(),i=t[t.length-1];if(void 0!==r)t[0]=r;else if(s&&e){const n=s.readValue(e,i);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=i),n&&void 0===r&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),fs.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,fs.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const xs=V((()=>void 0!==window.ScrollTimeline)),ks={};function _s(t,e){const s=V(t);return()=>ks[e]??s()}const Ts=_s((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0}),"linearEasing"),Ss=([t,e,s,n])=>`cubic-bezier(${t}, ${e}, ${s}, ${n})`,Ps={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Ss([0,.65,.55,1]),circOut:Ss([.55,0,1,.45]),backIn:Ss([.31,.01,.66,-.59]),backOut:Ss([.33,1.53,.69,.99])};function js(t,e){return t?"function"==typeof t?Ts()?ge(t,e):"ease-out":lt(t)?Ss(t):Array.isArray(t)?t.map((t=>js(t,e)||Ps.easeOut)):Ps[t]:void 0}function Es(t,e,s,{delay:n=0,duration:r=300,repeat:i=0,repeatType:o="loop",ease:a="easeOut",times:l}={},c=void 0){const h={[e]:s};l&&(h.offset=l);const u=js(a,r);Array.isArray(u)&&(h.easing=u);const d={delay:n,duration:r,easing:Array.isArray(u)?"linear":u,fill:"both",iterations:i+1,direction:"reverse"===o?"alternate":"normal"};c&&(d.pseudoElement=c);return t.animate(h,d)}function As(t){return"function"==typeof t&&"applyToOptions"in t}class Cs extends Je{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;const{element:e,name:s,keyframes:n,pseudoElement:r,allowFlatten:i=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=Boolean(r),this.allowFlatten=i,this.options=t,t.type;const l=function({type:t,...e}){return As(t)&&Ts()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=Es(e,s,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){const t=He(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,s){(t=>t.startsWith("--"))(e)?t.style.setProperty(e,s):t.style[e]=s}(e,s,t),this.animation.cancel()}a?.(),this.notifyFinished()},this.animation.oncancel=()=>this.notifyFinished()}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const t=this.animation.effect?.getComputedTiming?.().duration||0;return H(Number(t))}get time(){return H(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=q(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&xs()?(this.animation.timeline=t,U):e(this)}}const Rs={anticipate:et,backInOut:tt,circInOut:rt};function Os(t){"string"==typeof t.ease&&t.ease in Rs&&(t.ease=Rs[t.ease])}class Ms extends Cs{constructor(t){Os(t),Ke(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){const{motionValue:e,onUpdate:s,onComplete:n,element:r,...i}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);const o=new Xe({...i,autoplay:!1}),a=q(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}const Ls=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!te.test(t)&&"0"!==t||t.startsWith("url(")));const Ds=new Set(["opacity","clipPath","filter","transform"]),Is=V((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));class $s extends Je{constructor({autoplay:t=!0,delay:e=0,type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:i="loop",keyframes:o,name:a,motionValue:l,element:c,...h}){super(),this.stop=()=>{this._animation?(this._animation.stop(),this.stopTimeline?.()):this.keyframeResolver?.cancel()},this.createdAt=wt.now();const u={autoplay:t,delay:e,type:s,repeat:n,repeatDelay:r,repeatType:i,name:a,motionValue:l,element:c,...h},d=c?.KeyframeResolver||ws;this.keyframeResolver=new d(o,((t,e,s)=>this.onKeyframesResolved(t,e,u,!s)),a,l,c),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,s,n){this.keyframeResolver=void 0;const{name:r,type:i,velocity:o,delay:a,isHandoff:l,onUpdate:c}=s;this.resolvedAt=wt.now(),function(t,e,s,n){const r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;const i=t[t.length-1],o=Ls(r,e),a=Ls(i,e);return!(!o||!a)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let s=0;s<t.length;s++)if(t[s]!==e)return!0}(t)||("spring"===s||As(s))&&n)}(t,r,i,o)||(!I.instantAnimations&&a||c?.(He(t,s,e)),t[0]=t[t.length-1],s.duration=0,s.repeat=0);const h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...s,keyframes:t},u=!l&&function(t){const{motionValue:e,name:s,repeatDelay:n,repeatType:r,damping:i,type:o}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Is()&&s&&Ds.has(s)&&("transform"!==s||!l)&&!a&&!n&&"mirror"!==r&&0!==i&&"inertia"!==o}(h)?new Ms({...h,element:h.motionValue.owner.current}):new Xe(h);u.finished.then((()=>this.notifyFinished())).catch(U),this.pendingTimeline&&(this.stopTimeline=u.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=u}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then((()=>{}))}get animation(){return this._animation||(ys=!0,bs(),vs(),ys=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this.animation.cancel()}}const Ns=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Vs(t,e,s=1){const[n,r]=function(t){const e=Ns.exec(t);if(!e)return[,];const[,s,n,r]=e;return[`--${s??n}`,r]}(t);if(!n)return;const i=window.getComputedStyle(e).getPropertyValue(n);if(i){const t=i.trim();return $(t)?parseFloat(t):t}return Tt(r)?Vs(r,e,s+1):r}function Us(t,e){return t?.[e]??t?.default??t}const Bs=new Set(["width","height","top","left","right","bottom",...ls]),Fs=t=>e=>e.test(t),zs=[Pt,Ut,Vt,Nt,Ft,Bt,{test:t=>"auto"===t,parse:t=>t}],Ws=t=>zs.find(Fs(t));const qs=new Set(["brightness","contrast","saturate","opacity"]);function Hs(t){const[e,s]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[n]=s.match(Ct)||[];if(!n)return t;const r=s.replace(n,"");let i=qs.has(e)?1:0;return n!==s&&(i*=100),e+"("+i+r+")"}const Gs=/\b([a-z-]*)\(.*?\)/gu,Ks={...te,getAnimatableNone:t=>{const e=t.match(Gs);return e?e.map(Hs).join(" "):t}},Js={...Pt,transform:Math.round},Ys={borderWidth:Ut,borderTopWidth:Ut,borderRightWidth:Ut,borderBottomWidth:Ut,borderLeftWidth:Ut,borderRadius:Ut,radius:Ut,borderTopLeftRadius:Ut,borderTopRightRadius:Ut,borderBottomRightRadius:Ut,borderBottomLeftRadius:Ut,width:Ut,maxWidth:Ut,height:Ut,maxHeight:Ut,top:Ut,right:Ut,bottom:Ut,left:Ut,padding:Ut,paddingTop:Ut,paddingRight:Ut,paddingBottom:Ut,paddingLeft:Ut,margin:Ut,marginTop:Ut,marginRight:Ut,marginBottom:Ut,marginLeft:Ut,backgroundPositionX:Ut,backgroundPositionY:Ut,...{rotate:Nt,rotateX:Nt,rotateY:Nt,rotateZ:Nt,scale:Et,scaleX:Et,scaleY:Et,scaleZ:Et,skew:Nt,skewX:Nt,skewY:Nt,distance:Ut,translateX:Ut,translateY:Ut,translateZ:Ut,x:Ut,y:Ut,z:Ut,perspective:Ut,transformPerspective:Ut,opacity:jt,originX:zt,originY:zt,originZ:Ut},zIndex:Js,fillOpacity:jt,strokeOpacity:jt,numOctaves:Js},Xs={...Ys,color:qt,backgroundColor:qt,outlineColor:qt,fill:qt,stroke:qt,borderColor:qt,borderTopColor:qt,borderRightColor:qt,borderBottomColor:qt,borderLeftColor:qt,filter:Ks,WebkitFilter:Ks},Zs=t=>Xs[t];function Qs(t,e){let s=Zs(t);return s!==Ks&&(s=te),s.getAnimatableNone?s.getAnimatableNone(e):void 0}const tn=new Set(["auto","none","0"]);class en extends ws{constructor(t,e,s,n,r){super(t,e,s,n,r,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:s}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let s=t[a];if("string"==typeof s&&(s=s.trim(),Tt(s))){const n=Vs(s,e.current);void 0!==n&&(t[a]=n),a===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!Bs.has(s)||2!==t.length)return;const[n,r]=t,i=Ws(n),o=Ws(r);if(i!==o)if(hs(i)&&hs(o))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,s=[];for(let r=0;r<t.length;r++)(null===t[r]||("number"==typeof(n=t[r])?0===n:null===n||"none"===n||"0"===n||N(n)))&&s.push(r);var n;s.length&&function(t,e,s){let n,r=0;for(;r<t.length&&!n;){const e=t[r];"string"==typeof e&&!tn.has(e)&&Yt(e).values.length&&(n=t[r]),r++}if(n&&s)for(const i of e)t[i]=Qs(s,n)}(t,s,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:s}=this;if(!t||!t.current)return;"height"===s&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ps[s](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const n=e[e.length-1];void 0!==n&&t.getValue(s,n).jump(n,!1)}measureEndState(){const{element:t,name:e,unresolvedKeyframes:s}=this;if(!t||!t.current)return;const n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);const r=s.length-1,i=s[r];s[r]=ps[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==i&&void 0===this.finalKeyframe&&(this.finalKeyframe=i),this.removedTransforms?.length&&this.removedTransforms.forEach((([e,s])=>{t.getValue(e).set(s)})),this.resolveNoneKeyframes()}}function sn(t,e,s){if(t instanceof EventTarget)return[t];if("string"==typeof t){const e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}const{schedule:nn,cancel:rn}=pt(queueMicrotask,!1),on={x:!1,y:!1};function an(){return on.x||on.y}function ln(t,e){const s=sn(t),n=new AbortController;return[s,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function cn(t){return!("touch"===t.pointerType||an())}const hn=(t,e)=>!!e&&(t===e||hn(t,e.parentElement)),un=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,dn=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const pn=new WeakSet;function fn(t){return e=>{"Enter"===e.key&&t(e)}}function mn(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function gn(t){return un(t)&&!an()}function yn(t,e,s={}){const[n,r,i]=ln(t,s),o=t=>{const n=t.currentTarget;if(!gn(t)||pn.has(n))return;pn.add(n);const i=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),gn(t)&&pn.has(n)&&(pn.delete(n),"function"==typeof i&&i(t,{success:e}))},a=t=>{o(t,n===window||n===document||s.useGlobalTarget||hn(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach((t=>{var e;(s.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),t instanceof HTMLElement&&(t.addEventListener("focus",(t=>((t,e)=>{const s=t.currentTarget;if(!s)return;const n=fn((()=>{if(pn.has(s))return;mn(s,"down");const t=fn((()=>{mn(s,"up")}));s.addEventListener("keyup",t,e),s.addEventListener("blur",(()=>mn(s,"cancel")),e)}));s.addEventListener("keydown",n,e),s.addEventListener("blur",(()=>s.removeEventListener("keydown",n)),e)})(t,r))),e=t,dn.has(e.tagName)||-1!==e.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))})),i}function vn(t,e){let s;const n=()=>{const{currentTime:n}=e,r=(null===n?0:n.value)/100;s!==r&&t(r),s=r};return ft.preUpdate(n,!0),()=>mt(n)}const bn={current:void 0};class wn{constructor(t,e={}){this.version="12.9.1",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const s=wt.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change?.notify(this.current),e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=wt.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new W);const s=this.events[t].add(e);return"change"===t?()=>{s(),ft.read((()=>{this.events.change.getSize()||this.stop()}))}:s}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,s){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-s}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return bn.current&&bn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=wt.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return G(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function xn(t,e){return new wn(t,e)}const kn=[...zs,qt,te],_n=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Tn=t.createContext({strict:!1}),Sn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Pn={};for(const $d in Sn)Pn[$d]={isEnabled:t=>Sn[$d].some((e=>!!t[e]))};const jn=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function En(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||jn.has(t)}let An=t=>!En(t);try{(Cn=require("@emotion/is-prop-valid").default)&&(An=t=>t.startsWith("on")?!En(t):Cn(t))}catch{}var Cn;function Rn(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(s,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}const On=t.createContext({});function Mn(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function Ln(t){return"string"==typeof t||Array.isArray(t)}const Dn=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],In=["initial",...Dn];function $n(t){return Mn(t.animate)||In.some((e=>Ln(t[e])))}function Nn(t){return Boolean($n(t)||t.variants)}function Vn(e){const{initial:s,animate:n}=function(t,e){if($n(t)){const{initial:e,animate:s}=t;return{initial:!1===e||Ln(e)?e:void 0,animate:Ln(s)?s:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(On));return t.useMemo((()=>({initial:s,animate:n})),[Un(s),Un(n)])}function Un(t){return Array.isArray(t)?t.join(" "):t}const Bn=Symbol.for("motionComponentSymbol");function Fn(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function zn(e,s,n){return t.useCallback((t=>{t&&e.onMount&&e.onMount(t),s&&(t?s.mount(t):s.unmount()),n&&("function"==typeof n?n(t):Fn(n)&&(n.current=t))}),[s])}const Wn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),qn="data-"+Wn("framerAppearId"),Hn=t.createContext({});function Gn(e,s,n,r,i){const{visualElement:o}=t.useContext(On),a=t.useContext(Tn),l=t.useContext(_),c=t.useContext(T).reducedMotion,h=t.useRef(null);r=r||a.renderer,!h.current&&r&&(h.current=r(e,{visualState:s,parent:o,props:n,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:c}));const u=h.current,d=t.useContext(Hn);!u||u.projection||!i||"html"!==u.type&&"svg"!==u.type||function(t,e,s,n){const{layoutId:r,layout:i,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:h}=e;t.projection=new s(t.latestValues,e["data-framer-portal-id"]?void 0:Kn(t.parent)),t.projection.setOptions({layoutId:r,layout:i,alwaysMeasureLayout:Boolean(o)||a&&Fn(a),visualElement:t,animationType:"string"==typeof i?i:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:c})}(h.current,n,i,d);const p=t.useRef(!1);t.useInsertionEffect((()=>{u&&p.current&&u.update(n,l)}));const f=n[qn],m=t.useRef(Boolean(f)&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return k((()=>{u&&(p.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),nn.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())})),t.useEffect((()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask((()=>{window.MotionHandoffMarkAsComplete?.(f)})),m.current=!1))})),u}function Kn(t){if(t)return!1!==t.options.allowProjection?t.projection:Kn(t.parent)}function Jn({preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:i,Component:o}){function a(s,a){let l;const c={...t.useContext(T),...s,layoutId:Yn(s)},{isStatic:h}=c,u=Vn(s),d=i(s,h);if(!h&&x){t.useContext(Tn).strict;const e=function(t){const{drag:e,layout:s}=Pn;if(!e&&!s)return{};const n={...e,...s};return{MeasureLayout:e?.isEnabled(t)||s?.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);l=e.MeasureLayout,u.visualElement=Gn(o,d,c,n,e.ProjectionNode)}return e.jsxs(On.Provider,{value:u,children:[l&&u.visualElement?e.jsx(l,{visualElement:u.visualElement,...c}):null,r(o,s,zn(d,u.visualElement,a),d,h,u.visualElement)]})}s&&function(t){for(const e in t)Pn[e]={...Pn[e],...t[e]}}(s),a.displayName=`motion.${"string"==typeof o?o:`create(${o.displayName??o.name??""})`}`;const l=t.forwardRef(a);return l[Bn]=o,l}function Yn({layoutId:e}){const s=t.useContext(b).id;return s&&void 0!==e?s+"-"+e:e}const Xn={};function Zn(t,{layout:e,layoutId:s}){return cs.has(t)||t.startsWith("origin")||(e||void 0!==s)&&(!!Xn[t]||"opacity"===t)}const Qn=t=>Boolean(t&&t.getVelocity),tr={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},er=ls.length;function sr(t,e,s){const{style:n,vars:r,transformOrigin:i}=t;let o=!1,a=!1;for(const l in e){const t=e[l];if(cs.has(l))o=!0;else if(kt(l))r[l]=t;else{const e=_n(t,Ys[l]);l.startsWith("origin")?(a=!0,i[l]=e):n[l]=e}}if(e.transform||(o||s?n.transform=function(t,e,s){let n="",r=!0;for(let i=0;i<er;i++){const o=ls[i],a=t[o];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a),!l||s){const t=_n(a,Ys[o]);l||(r=!1,n+=`${tr[o]||o}(${t}) `),s&&(e[o]=t)}}return n=n.trim(),s?n=s(e,r?"":n):r&&(n="none"),n}(e,t.transform,s):n.transform&&(n.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:s=0}=i;n.transformOrigin=`${t} ${e} ${s}`}}const nr=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rr(t,e,s){for(const n in e)Qn(e[n])||Zn(n,s)||(t[n]=e[n])}function ir(e,s){const n={};return rr(n,e.style||{},e),Object.assign(n,function({transformTemplate:e},s){return t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return sr(t,s,e),Object.assign({},t.vars,t.style)}),[s])}(e,s)),n}function or(t,e){const s={},n=ir(t,e);return t.drag&&!1!==t.dragListener&&(s.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(s.tabIndex=0),s.style=n,s}const ar=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function lr(t){return"string"==typeof t&&!t.includes("-")&&!!(ar.indexOf(t)>-1||/[A-Z]/u.test(t))}const cr={offset:"stroke-dashoffset",array:"stroke-dasharray"},hr={offset:"strokeDashoffset",array:"strokeDasharray"};function ur(t,{attrX:e,attrY:s,attrScale:n,pathLength:r,pathSpacing:i=1,pathOffset:o=0,...a},l,c){if(sr(t,a,c),l)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:h,style:u}=t;h.transform&&(u.transform=h.transform,delete h.transform),(u.transform||h.transformOrigin)&&(u.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),u.transform&&(u.transformBox="fill-box",delete h.transformBox),void 0!==e&&(h.x=e),void 0!==s&&(h.y=s),void 0!==n&&(h.scale=n),void 0!==r&&function(t,e,s=1,n=0,r=!0){t.pathLength=1;const i=r?cr:hr;t[i.offset]=Ut.transform(-n);const o=Ut.transform(e),a=Ut.transform(s);t[i.array]=`${o} ${a}`}(h,r,i,o,!1)}const dr=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),pr=t=>"string"==typeof t&&"svg"===t.toLowerCase();function fr(e,s,n,r){const i=t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return ur(t,s,pr(r),e.transformTemplate),{...t.attrs,style:{...t.style}}}),[s]);if(e.style){const t={};rr(t,e.style,e),i.style={...t,...i.style}}return i}function mr(e=!1){return(s,n,r,{latestValues:i},o)=>{const a=(lr(s)?fr:or)(n,i,o,s),l=function(t,e,s){const n={};for(const r in t)"values"===r&&"object"==typeof t.values||(An(r)||!0===s&&En(r)||!e&&!En(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(n,"string"==typeof s,e),c=s!==t.Fragment?{...l,...a,ref:r}:{},{children:h}=n,u=t.useMemo((()=>Qn(h)?h.get():h),[h]);return t.createElement(s,{...c,children:u})}}function gr(t){const e=[{},{}];return t?.values.forEach(((t,s)=>{e[0][s]=t.get(),e[1][s]=t.getVelocity()})),e}function yr(t,e,s,n){if("function"==typeof e){const[r,i]=gr(n);e=e(void 0!==s?s:t.custom,r,i)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[r,i]=gr(n);e=e(void 0!==s?s:t.custom,r,i)}return e}function vr(t){return Qn(t)?t.get():t}const br=e=>(s,n)=>{const r=t.useContext(On),i=t.useContext(_),o=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},s,n,r){return{latestValues:wr(s,n,r,t),renderState:e()}}(e,s,r,i);return n?o():w(o)};function wr(t,e,s,n){const r={},i=n(t,{});for(const d in i)r[d]=vr(i[d]);let{initial:o,animate:a}=t;const l=$n(t),c=Nn(t);e&&c&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!s&&!1===s.initial;h=h||!1===o;const u=h?a:o;if(u&&"boolean"!=typeof u&&!Mn(u)){const e=Array.isArray(u)?u:[u];for(let s=0;s<e.length;s++){const n=yr(t,e[s]);if(n){const{transitionEnd:t,transition:e,...s}=n;for(const n in s){let t=s[n];if(Array.isArray(t)){t=t[h?t.length-1:0]}null!==t&&(r[n]=t)}for(const n in t)r[n]=t[n]}}}return r}function xr(t,e,s){const{style:n}=t,r={};for(const i in n)(Qn(n[i])||e.style&&Qn(e.style[i])||Zn(i,t)||void 0!==s?.getValue(i)?.liveStyle)&&(r[i]=n[i]);return r}const kr={useVisualState:br({scrapeMotionValuesFromProps:xr,createRenderState:nr})};function _r(t,e,s){const n=xr(t,e,s);for(const r in t)if(Qn(t[r])||Qn(e[r])){n[-1!==ls.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=t[r]}return n}const Tr={useVisualState:br({scrapeMotionValuesFromProps:_r,createRenderState:dr})};function Sr(t,e){return function(s,{forwardMotionProps:n}={forwardMotionProps:!1}){return Jn({...lr(s)?Tr:kr,preloadedFeatures:t,useRender:mr(n),createVisualElement:e,Component:s})}}function Pr(t,e,s){const n=t.getProps();return yr(n,e,void 0!==s?s:n.custom,t)}const jr=t=>Array.isArray(t);function Er(t,e,s){t.hasValue(e)?t.getValue(e).set(s):t.addValue(e,xn(s))}function Ar(t,e){const s=t.getValue("willChange");if(n=s,Boolean(Qn(n)&&n.add))return s.add(e);if(!s&&I.WillChange){const s=new I.WillChange("auto");t.addValue("willChange",s),s.add(e)}var n}function Cr(t){return t.props[qn]}const Rr=t=>null!==t;const Or={type:"spring",stiffness:500,damping:25,restSpeed:10},Mr={type:"keyframes",duration:.8},Lr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Dr=(t,{keyframes:e})=>e.length>2?Mr:cs.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Or:Lr;const Ir=(t,e,s,n={},r,i)=>o=>{const a=Us(n,t)||{},l=a.delay||n.delay||0;let{elapsed:c=0}=n;c-=q(l);const h={keyframes:Array.isArray(s)?s:[null,s],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:i?void 0:r};(function({when:t,delay:e,delayChildren:s,staggerChildren:n,staggerDirection:r,repeat:i,repeatType:o,repeatDelay:a,from:l,elapsed:c,...h}){return!!Object.keys(h).length})(a)||Object.assign(h,Dr(t,h)),h.duration&&(h.duration=q(h.duration)),h.repeatDelay&&(h.repeatDelay=q(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let u=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(u=!0)),(I.instantAnimations||I.skipAnimations)&&(u=!0,h.duration=0,h.delay=0),h.allowFlatten=!a.type&&!a.ease,u&&!i&&void 0!==e.get()){const t=function(t,{repeat:e,repeatType:s="loop"},n){const r=t.filter(Rr),i=e&&"loop"!==s&&e%2==1?0:r.length-1;return i&&void 0!==n?n:r[i]}(h.keyframes,a);if(void 0!==t)return void ft.update((()=>{h.onUpdate(t),h.onComplete()}))}return new $s(h)};function $r({protectedKeys:t,needsAnimating:e},s){const n=t.hasOwnProperty(s)&&!0!==e[s];return e[s]=!1,n}function Nr(t,e,{delay:s=0,transitionOverride:n,type:r}={}){let{transition:i=t.getDefaultTransition(),transitionEnd:o,...a}=e;n&&(i=n);const l=[],c=r&&t.animationState&&t.animationState.getState()[r];for(const h in a){const e=t.getValue(h,t.latestValues[h]??null),n=a[h];if(void 0===n||c&&$r(c,h))continue;const r={delay:s,...Us(i||{},h)},o=e.get();if(void 0!==o&&!e.isAnimating&&!Array.isArray(n)&&n===o&&!r.velocity)continue;let u=!1;if(window.MotionHandoffAnimation){const e=Cr(t);if(e){const t=window.MotionHandoffAnimation(e,h,ft);null!==t&&(r.startTime=t,u=!0)}}Ar(t,h),e.start(Ir(h,e,n,t.shouldReduceMotion&&Bs.has(h)?{type:!1}:r,t,u));const d=e.animation;d&&l.push(d)}return o&&Promise.all(l).then((()=>{ft.update((()=>{o&&function(t,e){const s=Pr(t,e);let{transitionEnd:n={},transition:r={},...i}=s||{};i={...i,...n};for(const a in i)Er(t,a,(o=i[a],jr(o)?o[o.length-1]||0:o));var o}(t,o)}))})),l}function Vr(t,e,s={}){const n=Pr(t,e,"exit"===s.type?t.presenceContext?.custom:void 0);let{transition:r=t.getDefaultTransition()||{}}=n||{};s.transitionOverride&&(r=s.transitionOverride);const i=n?()=>Promise.all(Nr(t,n,s)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{const{delayChildren:i=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,s=0,n=0,r=1,i){const o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(Ur).forEach(((t,n)=>{t.notify("AnimationStart",e),o.push(Vr(t,e,{...i,delay:s+l(n)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(o)}(t,e,i+n,o,a,s)}:()=>Promise.resolve(),{when:a}=r;if(a){const[t,e]="beforeChildren"===a?[i,o]:[o,i];return t().then((()=>e()))}return Promise.all([i(),o(s.delay)])}function Ur(t,e){return t.sortNodePosition(e)}function Br(t,e){if(!Array.isArray(e))return!1;const s=e.length;if(s!==t.length)return!1;for(let n=0;n<s;n++)if(e[n]!==t[n])return!1;return!0}const Fr=In.length;function zr(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&zr(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let s=0;s<Fr;s++){const n=In[s],r=t.props[n];(Ln(r)||!1===r)&&(e[n]=r)}return e}const Wr=[...Dn].reverse(),qr=Dn.length;function Hr(t){return e=>Promise.all(e.map((({animation:e,options:s})=>function(t,e,s={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e)){const r=e.map((e=>Vr(t,e,s)));n=Promise.all(r)}else if("string"==typeof e)n=Vr(t,e,s);else{const r="function"==typeof e?Pr(t,e,s.custom):e;n=Promise.all(Nr(t,r,s))}return n.then((()=>{t.notify("AnimationComplete",e)}))}(t,e,s))))}function Gr(t){let e=Hr(t),s=Yr(),n=!0;const r=e=>(s,n)=>{const r=Pr(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){const{transition:t,transitionEnd:e,...n}=r;s={...s,...n,...e}}return s};function i(i){const{props:o}=t,a=zr(t.parent)||{},l=[],c=new Set;let h={},u=1/0;for(let e=0;e<qr;e++){const d=Wr[e],p=s[d],f=void 0!==o[d]?o[d]:a[d],m=Ln(f),g=d===i?p.isActive:null;!1===g&&(u=e);let y=f===a[d]&&f!==o[d]&&m;if(y&&n&&t.manuallyAnimateOnMount&&(y=!1),p.protectedKeys={...h},!p.isActive&&null===g||!f&&!p.prevProp||Mn(f)||"boolean"==typeof f)continue;const v=Kr(p.prevProp,f);let b=v||d===i&&p.isActive&&!y&&m||e>u&&m,w=!1;const x=Array.isArray(f)?f:[f];let k=x.reduce(r(d),{});!1===g&&(k={});const{prevResolvedValues:_={}}=p,T={..._,...k},S=e=>{b=!0,c.has(e)&&(w=!0,c.delete(e)),p.needsAnimating[e]=!0;const s=t.getValue(e);s&&(s.liveStyle=!1)};for(const t in T){const e=k[t],s=_[t];if(h.hasOwnProperty(t))continue;let n=!1;n=jr(e)&&jr(s)?!Br(e,s):e!==s,n?null!=e?S(t):c.add(t):void 0!==e&&c.has(t)?S(t):p.protectedKeys[t]=!0}p.prevProp=f,p.prevResolvedValues=k,p.isActive&&(h={...h,...k}),n&&t.blockInitialAnimation&&(b=!1);b&&(!(y&&v)||w)&&l.push(...x.map((t=>({animation:t,options:{type:d}}))))}if(c.size){const e={};if("boolean"!=typeof o.initial){const s=Pr(t,Array.isArray(o.initial)?o.initial[0]:o.initial);s&&s.transition&&(e.transition=s.transition)}c.forEach((s=>{const n=t.getBaseTarget(s),r=t.getValue(s);r&&(r.liveStyle=!0),e[s]=n??null})),l.push({animation:e})}let d=Boolean(l.length);return!n||!1!==o.initial&&o.initial!==o.animate||t.manuallyAnimateOnMount||(d=!1),n=!1,d?e(l):Promise.resolve()}return{animateChanges:i,setActive:function(e,n){if(s[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach((t=>t.animationState?.setActive(e,n))),s[e].isActive=n;const r=i(e);for(const t in s)s[t].protectedKeys={};return r},setAnimateFunction:function(s){e=s(t)},getState:()=>s,reset:()=>{s=Yr(),n=!0}}}function Kr(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Br(e,t)}function Jr(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Yr(){return{animate:Jr(!0),whileInView:Jr(),whileHover:Jr(),whileTap:Jr(),whileDrag:Jr(),whileFocus:Jr(),exit:Jr()}}class Xr{constructor(t){this.isMounted=!1,this.node=t}update(){}}let Zr=0;const Qr={animation:{Feature:class extends Xr{constructor(t){super(t),t.animationState||(t.animationState=Gr(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Mn(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}},exit:{Feature:class extends Xr{constructor(){super(...arguments),this.id=Zr++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===s)return;const n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then((()=>{e(this.id)}))}mount(){const{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}}};function ti(t,e,s,n={passive:!0}){return t.addEventListener(e,s,n),()=>t.removeEventListener(e,s)}function ei(t){return{point:{x:t.pageX,y:t.pageY}}}function si(t,e,s,n){return ti(t,e,(t=>e=>un(e)&&t(e,ei(e)))(s),n)}function ni({top:t,left:e,right:s,bottom:n}){return{x:{min:e,max:s},y:{min:t,max:n}}}function ri(t){return t.max-t.min}function ii(t,e,s,n=.5){t.origin=n,t.originPoint=ne(e.min,e.max,t.origin),t.scale=ri(s)/ri(e),t.translate=ne(s.min,s.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function oi(t,e,s,n){ii(t.x,e.x,s.x,n?n.originX:void 0),ii(t.y,e.y,s.y,n?n.originY:void 0)}function ai(t,e,s){t.min=s.min+e.min,t.max=t.min+ri(e)}function li(t,e,s){t.min=e.min-s.min,t.max=t.min+ri(e)}function ci(t,e,s){li(t.x,e.x,s.x),li(t.y,e.y,s.y)}const hi=()=>({x:{min:0,max:0},y:{min:0,max:0}});function ui(t){return[t("x"),t("y")]}function di(t){return void 0===t||1===t}function pi({scale:t,scaleX:e,scaleY:s}){return!di(t)||!di(e)||!di(s)}function fi(t){return pi(t)||mi(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function mi(t){return gi(t.x)||gi(t.y)}function gi(t){return t&&"0%"!==t}function yi(t,e,s){return s+e*(t-s)}function vi(t,e,s,n,r){return void 0!==r&&(t=yi(t,r,n)),yi(t,s,n)+e}function bi(t,e=0,s=1,n,r){t.min=vi(t.min,e,s,n,r),t.max=vi(t.max,e,s,n,r)}function wi(t,{x:e,y:s}){bi(t.x,e.translate,e.scale,e.originPoint),bi(t.y,s.translate,s.scale,s.originPoint)}const xi=.999999999999,ki=1.0000000000001;function _i(t,e){t.min=t.min+e,t.max=t.max+e}function Ti(t,e,s,n,r=.5){bi(t,e,s,ne(t.min,t.max,r),n)}function Si(t,e){Ti(t.x,e.x,e.scaleX,e.scale,e.originX),Ti(t.y,e.y,e.scaleY,e.scale,e.originY)}function Pi(t,e){return ni(function(t,e){if(!e)return t;const s=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:s.y,left:s.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}const ji=({current:t})=>t?t.ownerDocument.defaultView:null,Ei=(t,e)=>Math.abs(t-e);class Ai{constructor(t,e,{transformPagePoint:s,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Oi(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,s=function(t,e){const s=Ei(t.x,e.x),n=Ei(t.y,e.y);return Math.sqrt(s**2+n**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!s)return;const{point:n}=t,{timestamp:r}=gt;this.history.push({...n,timestamp:r});const{onStart:i,onMove:o}=this.handlers;e||(i&&i(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=Ci(e,this.transformPagePoint),ft.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:s,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const i=Oi("pointercancel"===t.type?this.lastMoveEventInfo:Ci(e,this.transformPagePoint),this.history);this.startEvent&&s&&s(t,i),n&&n(t,i)},!un(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=s,this.contextWindow=n||window;const i=Ci(ei(t),this.transformPagePoint),{point:o}=i,{timestamp:a}=gt;this.history=[{...o,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Oi(i,this.history)),this.removeListeners=F(si(this.contextWindow,"pointermove",this.handlePointerMove),si(this.contextWindow,"pointerup",this.handlePointerUp),si(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),mt(this.updatePoint)}}function Ci(t,e){return e?{point:e(t.point)}:t}function Ri(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Oi({point:t},e){return{point:t,delta:Ri(t,Li(e)),offset:Ri(t,Mi(e)),velocity:Di(e,.1)}}function Mi(t){return t[0]}function Li(t){return t[t.length-1]}function Di(t,e){if(t.length<2)return{x:0,y:0};let s=t.length-1,n=null;const r=Li(t);for(;s>=0&&(n=t[s],!(r.timestamp-n.timestamp>q(e)));)s--;if(!n)return{x:0,y:0};const i=H(r.timestamp-n.timestamp);if(0===i)return{x:0,y:0};const o={x:(r.x-n.x)/i,y:(r.y-n.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ii(t,e,s){return{min:void 0!==e?t.min+e:void 0,max:void 0!==s?t.max+s-(t.max-t.min):void 0}}function $i(t,e){let s=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([s,n]=[n,s]),{min:s,max:n}}const Ni=.35;function Vi(t,e,s){return{min:Ui(t,e),max:Ui(t,s)}}function Ui(t,e){return"number"==typeof t?t:t[e]||0}const Bi=new WeakMap;class Fi{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&!1===s.isPresent)return;const{dragSnapToOrigin:n}=this.getProps();this.panSession=new Ai(t,{onSessionStart:t=>{const{dragSnapToOrigin:s}=this.getProps();s?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ei(t).point)},onStart:(t,e)=>{const{drag:s,dragPropagation:n,onDragStart:r}=this.getProps();if(s&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=s)||"y"===i?on[i]?null:(on[i]=!0,()=>{on[i]=!1}):on.x||on.y?null:(on.x=on.y=!0,()=>{on.x=on.y=!1}),!this.openDragLock))return;var i;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ui((t=>{let e=this.getAxisMotionValue(t).get()||0;if(Vt.test(e)){const{projection:s}=this.visualElement;if(s&&s.layout){const n=s.layout.layoutBox[t];if(n){e=ri(n)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),r&&ft.postRender((()=>r(t,e))),Ar(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:s,dragDirectionLock:n,onDirectionLock:r,onDrag:i}=this.getProps();if(!s&&!this.openDragLock)return;const{offset:o}=e;if(n&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let s=null;Math.abs(t.y)>e?s="y":Math.abs(t.x)>e&&(s="x");return s}(o),void(null!==this.currentDirection&&r&&r(this.currentDirection));this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),i&&i(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ui((t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:ji(this.visualElement)})}stop(t,e){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:n}=e;this.startAnimation(n);const{onDragEnd:r}=this.getProps();r&&ft.postRender((()=>r(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,s){const{drag:n}=this.getProps();if(!s||!zi(t,n,this.currentDirection))return;const r=this.getAxisMotionValue(t);let i=this.originPoint[t]+s[t];this.constraints&&this.constraints[t]&&(i=function(t,{min:e,max:s},n){return void 0!==e&&t<e?t=n?ne(e,t,n.min):Math.max(t,e):void 0!==s&&t>s&&(t=n?ne(s,t,n.max):Math.min(t,s)),t}(i,this.constraints[t],this.elastic[t])),r.set(i)}resolveConstraints(){const{dragConstraints:t,dragElastic:e}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&Fn(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!s)&&function(t,{top:e,left:s,bottom:n,right:r}){return{x:Ii(t.x,s,r),y:Ii(t.y,e,n)}}(s.layoutBox,t),this.elastic=function(t=Ni){return!1===t?t=0:!0===t&&(t=Ni),{x:Vi(t,"left","right"),y:Vi(t,"top","bottom")}}(e),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&ui((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const s={};return void 0!==e.min&&(s.min=e.min-t.min),void 0!==e.max&&(s.max=e.max-t.min),s}(s.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!Fn(t))return!1;const s=t.current,{projection:n}=this.visualElement;if(!n||!n.layout)return!1;const r=function(t,e,s){const n=Pi(t,s),{scroll:r}=e;return r&&(_i(n.x,r.offset.x),_i(n.y,r.offset.y)),n}(s,n.root,this.visualElement.getTransformPagePoint());let i=function(t,e){return{x:$i(t.x,e.x),y:$i(t.y,e.y)}}(n.layout.layoutBox,r);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(i));this.hasMutatedConstraints=!!t,t&&(i=ni(t))}return i}startAnimation(t){const{drag:e,dragMomentum:s,dragElastic:n,dragTransition:r,dragSnapToOrigin:i,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{},l=ui((o=>{if(!zi(o,e,this.currentDirection))return;let l=a&&a[o]||{};i&&(l={min:0,max:0});const c=n?200:1e6,h=n?40:1e7,u={type:"inertia",velocity:s?t[o]:0,bounceStiffness:c,bounceDamping:h,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)}));return Promise.all(l).then(o)}startAxisValueAnimation(t,e){const s=this.getAxisMotionValue(t);return Ar(this.visualElement,t),s.start(Ir(t,s,0,e,this.visualElement,!1))}stopAnimation(){ui((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){ui((t=>this.getAxisMotionValue(t).animation?.pause()))}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,s=this.visualElement.getProps(),n=s[e];return n||this.visualElement.getValue(t,(s.initial?s.initial[t]:void 0)||0)}snapToCursor(t){ui((e=>{const{drag:s}=this.getProps();if(!zi(e,s,this.currentDirection))return;const{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){const{min:s,max:i}=n.layout.layoutBox[e];r.set(t[e]-ne(s,i,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:s}=this.visualElement;if(!Fn(e)||!s||!this.constraints)return;this.stopAnimation();const n={x:0,y:0};ui((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const s=e.get();n[t]=function(t,e){let s=.5;const n=ri(t),r=ri(e);return r>n?s=z(e.min,e.max-n,t.min):n>r&&(s=z(t.min,t.max-r,e.min)),D(0,1,s)}({min:s,max:s},this.constraints[t])}}));const{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),ui((e=>{if(!zi(e,t,null))return;const s=this.getAxisMotionValue(e),{min:r,max:i}=this.constraints[e];s.set(ne(r,i,n[e]))}))}addListeners(){if(!this.visualElement.current)return;Bi.set(this.visualElement,this);const t=si(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:s=!0}=this.getProps();e&&s&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();Fn(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,n=s.addEventListener("measure",e);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),ft.read(e);const r=ti(window,"resize",(()=>this.scalePositionWithinConstraints())),i=s.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ui((e=>{const s=this.getAxisMotionValue(e);s&&(this.originPoint[e]+=t[e].translate,s.set(s.get()+t[e].translate))})),this.visualElement.render())}));return()=>{r(),t(),n(),i&&i()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:s=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:i=Ni,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:s,dragPropagation:n,dragConstraints:r,dragElastic:i,dragMomentum:o}}}function zi(t,e,s){return!(!0!==e&&e!==t||null!==s&&s!==t)}const Wi=t=>(e,s)=>{t&&ft.postRender((()=>t(e,s)))};const qi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Hi(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const Gi={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Ut.test(t))return t;t=parseFloat(t)}return`${Hi(t,e.target.x)}% ${Hi(t,e.target.y)}%`}},Ki={correct:(t,{treeScale:e,projectionDelta:s})=>{const n=t,r=te.parse(t);if(r.length>5)return n;const i=te.createTransformer(t),o="number"!=typeof r[0]?1:0,a=s.x.scale*e.x,l=s.y.scale*e.y;r[0+o]/=a,r[1+o]/=l;const c=ne(a,l,.5);return"number"==typeof r[2+o]&&(r[2+o]/=c),"number"==typeof r[3+o]&&(r[3+o]/=c),i(r)}};class Ji extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:s,layoutId:n}=this.props,{projection:r}=t;!function(t){for(const e in t)Xn[e]=t[e],kt(e)&&(Xn[e].isCSSVariable=!0)}(Xi),r&&(e.group&&e.group.add(r),s&&s.register&&n&&s.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",(()=>{this.safeToRemove()})),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),qi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:s,drag:n,isPresent:r}=this.props,i=s.projection;return i?(i.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?i.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?i.promote():i.relegate()||ft.postRender((()=>{const t=i.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),nn.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:s}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),s&&s.deregister&&s.deregister(n))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Yi(s){const[n,r]=A(),i=t.useContext(b);return e.jsx(Ji,{...s,layoutGroup:i,switchLayoutGroup:t.useContext(Hn),isPresent:n,safeToRemove:r})}const Xi={borderRadius:{...Gi,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:Gi,borderTopRightRadius:Gi,borderBottomLeftRadius:Gi,borderBottomRightRadius:Gi,boxShadow:Ki};const Zi=(t,e)=>t.depth-e.depth;class Qi{constructor(){this.children=[],this.isDirty=!1}add(t){M(this.children,t),this.isDirty=!0}remove(t){L(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Zi),this.isDirty=!1,this.children.forEach(t)}}const to=["TopLeft","TopRight","BottomLeft","BottomRight"],eo=to.length,so=t=>"string"==typeof t?parseFloat(t):t,no=t=>"number"==typeof t||Ut.test(t);function ro(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const io=ao(0,.5,nt),oo=ao(.5,.95,U);function ao(t,e,s){return n=>n<t?0:n>e?1:s(z(t,e,n))}function lo(t,e){t.min=e.min,t.max=e.max}function co(t,e){lo(t.x,e.x),lo(t.y,e.y)}function ho(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function uo(t,e,s,n,r){return t=yi(t-=e,1/s,n),void 0!==r&&(t=yi(t,1/r,n)),t}function po(t,e,[s,n,r],i,o){!function(t,e=0,s=1,n=.5,r,i=t,o=t){Vt.test(e)&&(e=parseFloat(e),e=ne(o.min,o.max,e/100)-o.min);if("number"!=typeof e)return;let a=ne(i.min,i.max,n);t===i&&(a-=e),t.min=uo(t.min,e,s,a,r),t.max=uo(t.max,e,s,a,r)}(t,e[s],e[n],e[r],e.scale,i,o)}const fo=["x","scaleX","originX"],mo=["y","scaleY","originY"];function go(t,e,s,n){po(t.x,e,fo,s?s.x:void 0,n?n.x:void 0),po(t.y,e,mo,s?s.y:void 0,n?n.y:void 0)}function yo(t){return 0===t.translate&&1===t.scale}function vo(t){return yo(t.x)&&yo(t.y)}function bo(t,e){return t.min===e.min&&t.max===e.max}function wo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function xo(t,e){return wo(t.x,e.x)&&wo(t.y,e.y)}function ko(t){return ri(t.x)/ri(t.y)}function _o(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class To{constructor(){this.members=[]}add(t){M(this.members,t),t.scheduleRender()}remove(t){if(L(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let s;for(let n=e;n>=0;n--){const t=this.members[n];if(!1!==t.isPresent){s=t;break}}return!!s&&(this.promote(s),!0)}promote(t,e){const s=this.lead;if(t!==s&&(this.prevLead=s,this.lead=t,t.show(),s)){s.instance&&s.scheduleRender(),t.scheduleRender(),t.resumeFrom=s,e&&(t.resumeFrom.preserveOpacity=!0),s.snapshot&&(t.snapshot=s.snapshot,t.snapshot.latestValues=s.animationValues||s.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:n}=t.options;!1===n&&s.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:s}=t;e.onExitComplete&&e.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const So=["","X","Y","Z"],Po={visibility:"hidden"};let jo=0;function Eo(t,e,s,n){const{latestValues:r}=e;r[t]&&(s[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function Ao(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const s=Cr(e);if(window.MotionHasOptimisedAnimation(s,"transform")){const{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(s,"transform",ft,!(e||n))}const{parent:n}=t;n&&!n.hasCheckedOptimisedAppear&&Ao(n)}function Co({attachResizeListener:t,defaultParent:e,measureScroll:s,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},s=e?.()){this.id=jo++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Mo),this.nodes.forEach(Uo),this.nodes.forEach(Bo),this.nodes.forEach(Lo)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=s?s.root||s:this,this.path=s?[...s.path,s]:[],this.parent=s,this.depth=s?s.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Qi)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new W),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const s=this.eventHandlers.get(t);s&&s.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,s=this.root.hasTreeAnimated){if(this.instance)return;var n;this.isSVG=(n=e)instanceof SVGElement&&"svg"!==n.tagName,this.instance=e;const{layoutId:r,layout:i,visualElement:o}=this.options;if(o&&!o.current&&o.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),s&&(i||r)&&(this.isLayoutDirty=!0),t){let s;const n=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,s&&s(),s=function(t,e){const s=wt.now(),n=({timestamp:r})=>{const i=r-s;i>=e&&(mt(n),t(i-e))};return ft.setup(n,!0),()=>mt(n)}(n,250),qi.hasAnimatedSinceResize&&(qi.hasAnimatedSinceResize=!1,this.nodes.forEach(Vo))}))}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&o&&(r||i)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:s,layout:n})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const r=this.options.transition||o.getDefaultTransition()||Go,{onLayoutAnimationStart:i,onLayoutAnimationComplete:a}=o.getProps(),l=!this.targetLayout||!xo(this.targetLayout,n),c=!e&&s;if(this.options.layoutRoot||this.resumeFrom||c||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,c);const e={...Us(r,"layout"),onPlay:i,onComplete:a};(o.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||Vo(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,mt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Fo),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Ao(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let r=0;r<this.path.length;r++){const t=this.path[r];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:s}=this.options;if(void 0===e&&!s)return;const n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Io);this.isUpdating||this.nodes.forEach($o),this.isUpdating=!1,this.nodes.forEach(No),this.nodes.forEach(Ro),this.nodes.forEach(Oo),this.clearAllSnapshots();const t=wt.now();gt.delta=D(0,1e3/60,t-gt.timestamp),gt.timestamp=t,gt.isProcessing=!0,yt.update.process(gt),yt.preRender.process(gt),yt.render.process(gt),gt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nn.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Do),this.sharedNodes.forEach(zo)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,ft.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){ft.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ri(this.snapshot.measuredBox.x)||ri(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let s=0;s<this.path.length;s++){this.path[s].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:s(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!vo(this.projectionDelta),s=this.getTransformTemplate(),n=s?s(this.latestValues,""):void 0,i=n!==this.prevTransformTemplateValue;t&&(e||fi(this.latestValues)||i)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let s=this.removeElementScroll(e);var n;return t&&(s=this.removeTransform(s)),Yo((n=s).x),Yo(n.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:t}=this.options;if(!t)return{x:{min:0,max:0},y:{min:0,max:0}};const e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Zo))){const{scroll:t}=this.root;t&&(_i(e.x,t.offset.x),_i(e.y,t.offset.y))}return e}removeElementScroll(t){const e={x:{min:0,max:0},y:{min:0,max:0}};if(co(e,t),this.scroll?.wasRoot)return e;for(let s=0;s<this.path.length;s++){const n=this.path[s],{scroll:r,options:i}=n;n!==this.root&&r&&i.layoutScroll&&(r.wasRoot&&co(e,t),_i(e.x,r.offset.x),_i(e.y,r.offset.y))}return e}applyTransform(t,e=!1){const s={x:{min:0,max:0},y:{min:0,max:0}};co(s,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&Si(s,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),fi(t.latestValues)&&Si(s,t.latestValues)}return fi(this.latestValues)&&Si(s,this.latestValues),s}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};co(e,t);for(let s=0;s<this.path.length;s++){const t=this.path[s];if(!t.instance)continue;if(!fi(t.latestValues))continue;pi(t.latestValues)&&t.updateSnapshot();const n={x:{min:0,max:0},y:{min:0,max:0}};co(n,t.measurePageBox()),go(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,n)}return fi(this.latestValues)&&go(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==gt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){const e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);const s=Boolean(this.resumingFrom)||this!==e;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=gt.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ci(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),co(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}var i,o,a;if(this.relativeTarget||this.targetDelta)if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),i=this.target,o=this.relativeTarget,a=this.relativeParent.target,ai(i.x,o.x,a.x),ai(i.y,o.y,a.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):co(this.target,this.layout.layoutBox),wi(this.target,this.targetDelta)):co(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},ci(this.relativeTargetOrigin,this.target,t.target),co(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(this.parent&&!pi(this.parent.latestValues)&&!mi(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const t=this.getLead(),e=Boolean(this.resumingFrom)||this!==t;let s=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(s=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===gt.timestamp&&(s=!1),s)return;const{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!n&&!r)return;co(this.layoutCorrected,this.layout.layoutBox);const i=this.treeScale.x,o=this.treeScale.y;!function(t,e,s,n=!1){const r=s.length;if(!r)return;let i,o;e.x=e.y=1;for(let a=0;a<r;a++){i=s[a],o=i.projectionDelta;const{visualElement:r}=i.options;r&&r.props.style&&"contents"===r.props.style.display||(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&Si(t,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(e.x*=o.x.scale,e.y*=o.y.scale,wi(t,o)),n&&fi(i.latestValues)&&Si(t,i.latestValues))}e.x<ki&&e.x>xi&&(e.x=1),e.y<ki&&e.y>xi&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,e),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox,t.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:a}=t;a?(this.projectionDelta&&this.prevProjectionDelta?(ho(this.prevProjectionDelta.x,this.projectionDelta.x),ho(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),oi(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===i&&this.treeScale.y===o&&_o(this.projectionDelta.x,this.prevProjectionDelta.x)&&_o(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a))):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},i={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const o={x:{min:0,max:0},y:{min:0,max:0}},a=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),c=!l||l.members.length<=1,h=Boolean(a&&!c&&!0===this.options.crossfade&&!this.path.some(Ho));let u;this.animationProgress=0,this.mixTargetDelta=e=>{const s=e/1e3;var l,d,p,f,m,g;Wo(i.x,t.x,s),Wo(i.y,t.y,s),this.setTargetDelta(i),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(ci(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=o,g=s,qo(p.x,f.x,m.x,g),qo(p.y,f.y,m.y,g),u&&(l=this.relativeTarget,d=u,bo(l.x,d.x)&&bo(l.y,d.y))&&(this.isProjectionDirty=!1),u||(u={x:{min:0,max:0},y:{min:0,max:0}}),co(u,this.relativeTarget)),a&&(this.animationValues=r,function(t,e,s,n,r,i){r?(t.opacity=ne(0,s.opacity??1,io(n)),t.opacityExit=ne(e.opacity??1,0,oo(n))):i&&(t.opacity=ne(e.opacity??1,s.opacity??1,n));for(let o=0;o<eo;o++){const r=`border${to[o]}Radius`;let i=ro(e,r),a=ro(s,r);void 0===i&&void 0===a||(i||(i=0),a||(a=0),0===i||0===a||no(i)===no(a)?(t[r]=Math.max(ne(so(i),so(a),n),0),(Vt.test(a)||Vt.test(i))&&(t[r]+="%")):t[r]=a)}(e.rotate||s.rotate)&&(t.rotate=ne(e.rotate||0,s.rotate||0,n))}(r,n,this.latestValues,s,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(mt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=ft.update((()=>{qi.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,s){const n=Qn(t)?t:xn(t);return n.start(Ir("",n,e,s)),n.animation}(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:s,layout:n,latestValues:r}=t;if(e&&s&&n){if(this!==t&&this.layout&&n&&Xo(this.options.animationType,this.layout.layoutBox,n.layoutBox)){s=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=ri(this.layout.layoutBox.x);s.x.min=t.target.x.min,s.x.max=s.x.min+e;const n=ri(this.layout.layoutBox.y);s.y.min=t.target.y.min,s.y.max=s.y.min+n}co(e,s),Si(e,r),oi(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new To);this.sharedNodes.get(t).add(e);const s=e.options.initialPromotionConfig;e.promote({transition:s?s.transition:void 0,preserveFollowOpacity:s&&s.shouldPreserveFollowOpacity?s.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){const{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){const{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:s}={}){const n=this.getStack();n&&n.promote(this,s),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:s}=t;if((s.z||s.rotate||s.rotateX||s.rotateY||s.rotateZ||s.skewX||s.skewY)&&(e=!0),!e)return;const n={};s.z&&Eo("z",t,n,this.animationValues);for(let r=0;r<So.length;r++)Eo(`rotate${So[r]}`,t,n,this.animationValues),Eo(`skew${So[r]}`,t,n,this.animationValues);t.render();for(const r in n)t.setStaticValue(r,n[r]),this.animationValues&&(this.animationValues[r]=n[r]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return Po;const e={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=vr(t?.pointerEvents)||"",e.transform=s?s(this.latestValues,""):"none",e;const n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=vr(t?.pointerEvents)||""),this.hasProjected&&!fi(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,s){let n="";const r=t.x.translate/e.x,i=t.y.translate/e.y,o=s?.z||0;if((r||i||o)&&(n=`translate3d(${r}px, ${i}px, ${o}px) `),1===e.x&&1===e.y||(n+=`scale(${1/e.x}, ${1/e.y}) `),s){const{transformPerspective:t,rotate:e,rotateX:r,rotateY:i,skewX:o,skewY:a}=s;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),i&&(n+=`rotateY(${i}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(e.transform=s(r,e.transform));const{x:i,y:o}=this.projectionDelta;e.transformOrigin=`${100*i.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const a in Xn){if(void 0===r[a])continue;const{correct:t,applyTo:s,isCSSVariable:i}=Xn[a],o="none"===e.transform?r[a]:t(r[a],n);if(s){const t=s.length;for(let n=0;n<t;n++)e[s[n]]=o}else i?this.options.visualElement.renderState.vars[a]=o:e[a]=o}return this.options.layoutId&&(e.pointerEvents=n===this?vr(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>t.currentAnimation?.stop())),this.root.nodes.forEach(Io),this.root.sharedNodes.clear()}}}function Ro(t){t.updateLayout()}function Oo(t){const e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:n}=t.layout,{animationType:r}=t.options,i=e.source!==t.layout.source;"size"===r?ui((t=>{const n=i?e.measuredBox[t]:e.layoutBox[t],r=ri(n);n.min=s[t].min,n.max=n.min+r})):Xo(r,e.layoutBox,s)&&ui((n=>{const r=i?e.measuredBox[n]:e.layoutBox[n],o=ri(s[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)}));const o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};oi(o,s,e.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};i?oi(a,t.applyTransform(n,!0),e.measuredBox):oi(a,s,e.layoutBox);const l=!vo(o);let c=!1;if(!t.resumeFrom){const n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){const{snapshot:r,layout:i}=n;if(r&&i){const o={x:{min:0,max:0},y:{min:0,max:0}};ci(o,e.layoutBox,r.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};ci(a,s,i.layoutBox),xo(o,a)||(c=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:c})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function Mo(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Lo(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Do(t){t.clearSnapshot()}function Io(t){t.clearMeasurements()}function $o(t){t.isLayoutDirty=!1}function No(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function Vo(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function Uo(t){t.resolveTargetDelta()}function Bo(t){t.calcProjection()}function Fo(t){t.resetSkewAndRotation()}function zo(t){t.removeLeadSnapshot()}function Wo(t,e,s){t.translate=ne(e.translate,0,s),t.scale=ne(e.scale,1,s),t.origin=e.origin,t.originPoint=e.originPoint}function qo(t,e,s,n){t.min=ne(e.min,s.min,n),t.max=ne(e.max,s.max,n)}function Ho(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const Go={duration:.45,ease:[.4,0,.1,1]},Ko=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),Jo=Ko("applewebkit/")&&!Ko("chrome/")?Math.round:U;function Yo(t){t.min=Jo(t.min),t.max=Jo(t.max)}function Xo(t,e,s){return"position"===t||"preserve-aspect"===t&&(n=ko(e),r=ko(s),i=.2,!(Math.abs(n-r)<=i));var n,r,i}function Zo(t){return t!==t.root&&t.scroll?.wasRoot}const Qo=Co({attachResizeListener:(t,e)=>ti(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ta={current:void 0},ea=Co({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ta.current){const t=new Qo({});t.mount(window),t.setOptions({layoutScroll:!0}),ta.current=t}return ta.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),sa={pan:{Feature:class extends Xr{constructor(){super(...arguments),this.removePointerDownListener=U}onPointerDown(t){this.session=new Ai(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ji(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:s,onPanEnd:n}=this.node.getProps();return{onSessionStart:Wi(t),onStart:Wi(e),onMove:s,onEnd:(t,e)=>{delete this.session,n&&ft.postRender((()=>n(t,e)))}}}mount(){this.removePointerDownListener=si(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Xr{constructor(t){super(t),this.removeGroupControls=U,this.removeListeners=U,this.controls=new Fi(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||U}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:ea,MeasureLayout:Yi}};function na(t,e,s){const{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===s);const r=n["onHover"+s];r&&ft.postRender((()=>r(e,ei(e))))}function ra(t,e,s){const{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===s);const r=n["onTap"+("End"===s?"":s)];r&&ft.postRender((()=>r(e,ei(e))))}const ia=new WeakMap,oa=new WeakMap,aa=t=>{const e=ia.get(t.target);e&&e(t)},la=t=>{t.forEach(aa)};function ca(t,e,s){const n=function({root:t,...e}){const s=t||document;oa.has(s)||oa.set(s,{});const n=oa.get(s),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(la,{root:t,...e})),n[r]}(e);return ia.set(t,s),n.observe(t),()=>{ia.delete(t),n.unobserve(t)}}const ha={some:0,all:1};const ua={inView:{Feature:class extends Xr{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:s,amount:n="some",once:r}=t,i={root:e?e.current:void 0,rootMargin:s,threshold:"number"==typeof n?n:ha[n]};return ca(this.node.current,i,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,r&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:s,onViewportLeave:n}=this.node.getProps(),i=e?s:n;i&&i(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return s=>t[s]!==e[s]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Xr{mount(){const{current:t}=this.node;t&&(this.unmount=yn(t,((t,e)=>(ra(this.node,e,"Start"),(t,{success:e})=>ra(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends Xr{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=F(ti(this.node.current,"focus",(()=>this.onFocus())),ti(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends Xr{mount(){const{current:t}=this.node;t&&(this.unmount=function(t,e,s={}){const[n,r,i]=ln(t,s),o=t=>{if(!cn(t))return;const{target:s}=t,n=e(s,t);if("function"!=typeof n||!s)return;const i=t=>{cn(t)&&(n(t),s.removeEventListener("pointerleave",i))};s.addEventListener("pointerleave",i,r)};return n.forEach((t=>{t.addEventListener("pointerenter",o,r)})),i}(t,((t,e)=>(na(this.node,e,"Start"),t=>na(this.node,t,"End")))))}unmount(){}}}},da={layout:{ProjectionNode:ea,MeasureLayout:Yi}},pa={current:null},fa={current:!1};const ma=new WeakMap;const ga=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ya{scrapeMotionValuesFromProps(t,e,s){return{}}constructor({parent:t,props:e,presenceContext:s,reducedMotionConfig:n,blockInitialAnimation:r,visualState:i},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ws,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=wt.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,ft.render(this.render,!1,!0))};const{latestValues:a,renderState:l}=i;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=s,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=Boolean(r),this.isControllingVariants=$n(e),this.isVariantNode=Nn(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const u in h){const t=h[u];void 0!==a[u]&&Qn(t)&&t.set(a[u],!1)}}mount(t){this.current=t,ma.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),fa.current||function(){if(fa.current=!0,x)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>pa.current=t.matches;t.addListener(e),e()}else pa.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||pa.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),mt(this.notifyUpdate),mt(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const s=cs.has(t);s&&this.onBindTransform&&this.onBindTransform();const n=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&ft.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)})),r=e.on("renderRequest",this.scheduleRender);let i;window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{n(),r(),i&&i(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in Pn){const e=Pn[t];if(!e)continue;const{isEnabled:s,Feature:n}=e;if(!this.features[t]&&n&&s(this.props)&&(this.features[t]=new n(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let s=0;s<ga.length;s++){const e=ga[s];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const n=t["on"+e];n&&(this.propEventSubscriptions[e]=this.on(e,n))}this.prevMotionValues=function(t,e,s){for(const n in e){const r=e[n],i=s[n];if(Qn(r))t.addValue(n,r);else if(Qn(i))t.addValue(n,xn(r,{owner:t}));else if(i!==r)if(t.hasValue(n)){const e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{const e=t.getStaticValue(n);t.addValue(n,xn(void 0!==e?e:r,{owner:t}))}}for(const n in s)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const s=this.values.get(t);e!==s&&(s&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let s=this.values.get(t);return void 0===s&&void 0!==e&&(s=xn(null===e?void 0:e,{owner:this}),this.addValue(t,s)),s}readValue(t,e){let s=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var n;return null!=s&&("string"==typeof s&&($(s)||N(s))?s=parseFloat(s):(n=s,!kn.find(Fs(n))&&te.test(e)&&(s=Qs(t,e))),this.setBaseTarget(t,Qn(s)?s.get():s)),Qn(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){const{initial:e}=this.props;let s;if("string"==typeof e||"object"==typeof e){const n=yr(this.props,e,this.presenceContext?.custom);n&&(s=n[t])}if(e&&void 0!==s)return s;const n=this.getBaseTargetFromProps(this.props,t);return void 0===n||Qn(n)?void 0!==this.initialValues[t]&&void 0===s?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new W),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class va extends ya{constructor(){super(...arguments),this.KeyframeResolver=en}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:s}){delete e[t],delete s[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Qn(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}function ba(t,{style:e,vars:s},n,r){Object.assign(t.style,e,r&&r.getProjectionStyles(n));for(const i in s)t.style.setProperty(i,s[i])}class wa extends va{constructor(){super(...arguments),this.type="html",this.renderInstance=ba}readValueFromInstance(t,e){if(cs.has(e))return((t,e)=>{const{transform:s="none"}=getComputedStyle(t);return os(s,e)})(t,e);{const n=(s=t,window.getComputedStyle(s)),r=(kt(e)?n.getPropertyValue(e):n[e])||0;return"string"==typeof r?r.trim():r}var s}measureInstanceViewportBox(t,{transformPagePoint:e}){return Pi(t,e)}build(t,e,s){sr(t,e,s.transformTemplate)}scrapeMotionValuesFromProps(t,e,s){return xr(t,e,s)}}const xa=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ka extends va{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=hi}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(cs.has(e)){const t=Zs(e);return t&&t.default||0}return e=xa.has(e)?e:Wn(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,s){return _r(t,e,s)}build(t,e,s){ur(t,e,this.isSVGTag,s.transformTemplate)}renderInstance(t,e,s,n){!function(t,e,s,n){ba(t,e,void 0,n);for(const r in e.attrs)t.setAttribute(xa.has(r)?r:Wn(r),e.attrs[r])}(t,e,0,n)}mount(t){this.isSVGTag=pr(t.tagName),super.mount(t)}}const _a=Rn(Sr({...Qr,...ua,...sa,...da},((e,s)=>lr(e)?new ka(s):new wa(s,{allowProjection:e!==t.Fragment})))),Ta=new WeakMap;let Sa;function Pa({target:t,contentRect:e,borderBoxSize:s}){Ta.get(t)?.forEach((n=>{n({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:s}=e[0];return{width:t,height:s}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,s)}})}))}function ja(t){t.forEach(Pa)}function Ea(t,e){Sa||"undefined"!=typeof ResizeObserver&&(Sa=new ResizeObserver(ja));const s=sn(t);return s.forEach((t=>{let s=Ta.get(t);s||(s=new Set,Ta.set(t,s)),s.add(e),Sa?.observe(t)})),()=>{s.forEach((t=>{const s=Ta.get(t);s?.delete(e),s?.size||Sa?.unobserve(t)}))}}const Aa=new Set;let Ca;function Ra(t){return Aa.add(t),Ca||(Ca=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};Aa.forEach((t=>t(e)))},window.addEventListener("resize",Ca)),()=>{Aa.delete(t),!Aa.size&&Ca&&(Ca=void 0)}}const Oa={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function Ma(t,e,s,n){const r=s[e],{length:i,position:o}=Oa[e],a=r.current,l=s.time;r.current=t[`scroll${o}`],r.scrollLength=t[`scroll${i}`]-t[`client${i}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=z(0,r.scrollLength,r.current);const c=n-l;r.velocity=c>50?0:G(r.current-a,c)}const La={start:0,center:.5,end:1};function Da(t,e,s=0){let n=0;if(t in La&&(t=La[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?n=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?n=e/100*document.documentElement.clientWidth:t.endsWith("vh")?n=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(n=e*t),s+n}const Ia=[0,0];function $a(t,e,s,n){let r=Array.isArray(t)?t:Ia,i=0,o=0;return"number"==typeof t?r=[t,t]:"string"==typeof t&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,La[t]?t:"0"]),i=Da(r[0],s,n),o=Da(r[1],e),i-o}const Na={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},Va={x:0,y:0};function Ua(t,e,s){const{offset:n=Na.All}=s,{target:r=t,axis:i="y"}=s,o="y"===i?"height":"width",a=r!==t?function(t,e){const s={x:0,y:0};let n=t;for(;n&&n!==e;)if(n instanceof HTMLElement)s.x+=n.offsetLeft,s.y+=n.offsetTop,n=n.offsetParent;else if("svg"===n.tagName){const t=n.getBoundingClientRect();n=n.parentElement;const e=n.getBoundingClientRect();s.x+=t.left-e.left,s.y+=t.top-e.top}else{if(!(n instanceof SVGGraphicsElement))break;{const{x:t,y:e}=n.getBBox();s.x+=t,s.y+=e;let r=null,i=n.parentNode;for(;!r;)"svg"===i.tagName&&(r=i),i=n.parentNode;n=r}}return s}(r,t):Va,l=r===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(r),c={width:t.clientWidth,height:t.clientHeight};e[i].offset.length=0;let h=!e[i].interpolate;const u=n.length;for(let d=0;d<u;d++){const t=$a(n[d],c[o],l[o],a[i]);h||t===e[i].interpolatorOffsets[d]||(h=!0),e[i].offset[d]=t}h&&(e[i].interpolate=Fe(e[i].offset,ze(n),{clamp:!1}),e[i].interpolatorOffsets=[...e[i].offset]),e[i].progress=D(0,1,e[i].interpolate(e[i].current))}function Ba(t,e,s,n={}){return{measure:()=>function(t,e=t,s){if(s.x.targetOffset=0,s.y.targetOffset=0,e!==t){let n=e;for(;n&&n!==t;)s.x.targetOffset+=n.offsetLeft,s.y.targetOffset+=n.offsetTop,n=n.offsetParent}s.x.targetLength=e===t?e.scrollWidth:e.clientWidth,s.y.targetLength=e===t?e.scrollHeight:e.clientHeight,s.x.containerLength=t.clientWidth,s.y.containerLength=t.clientHeight}(t,n.target,s),update:e=>{!function(t,e,s){Ma(t,"x",e,s),Ma(t,"y",e,s),e.time=s}(t,s,e),(n.offset||n.target)&&Ua(t,s,n)},notify:()=>e(s)}}const Fa=new WeakMap,za=new WeakMap,Wa=new WeakMap,qa=t=>t===document.documentElement?window:t;function Ha(t,{container:e=document.documentElement,...s}={}){let n=Wa.get(e);n||(n=new Set,Wa.set(e,n));const r=Ba(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},s);if(n.add(r),!Fa.has(e)){const t=()=>{for(const t of n)t.measure()},s=()=>{for(const t of n)t.update(gt.timestamp)},r=()=>{for(const t of n)t.notify()},a=()=>{ft.read(t,!1,!0),ft.read(s,!1,!0),ft.preUpdate(r,!1,!0)};Fa.set(e,a);const l=qa(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&za.set(e,(o=a,"function"==typeof(i=e)?Ra(i):Ea(i,o))),l.addEventListener("scroll",a,{passive:!0})}var i,o;const a=Fa.get(e);return ft.read(a,!1,!0),()=>{mt(a);const t=Wa.get(e);if(!t)return;if(t.delete(r),t.size)return;const s=Fa.get(e);Fa.delete(e),s&&(qa(e).removeEventListener("scroll",s),za.get(e)?.(),window.removeEventListener("resize",s))}}const Ga=new Map;function Ka({source:t,container:e,...s}){const{axis:n}=s;t&&(e=t);const r=Ga.get(e)??new Map;Ga.set(e,r);const i=s.target??"self",o=r.get(i)??{},a=n+(s.offset??[]).join(",");return o[a]||(o[a]=!s.target&&xs()?new ScrollTimeline({source:e,axis:n}):function(t){const e={value:0},s=Ha((s=>{e.value=100*s[t.axis].progress}),t);return{currentTime:e,cancel:s}}({container:e,...s})),o[a]}function Ja(t,{axis:e="y",container:s=document.documentElement,...n}={}){s===document.documentElement&&("y"===e&&s.scrollHeight===s.clientHeight||"x"===e&&s.scrollWidth===s.clientWidth)&&(s=document.body);const r={axis:e,container:s,...n};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)?Ha((s=>{t(s[e.axis].progress,s)}),e):vn(t,Ka(e))}(t,r):function(t,e){const s=Ka(e);return t.attachTimeline({timeline:e.target?void 0:s,observe:t=>(t.pause(),vn((e=>{t.time=t.duration*e}),s))})}(t,r)}function Ya(t,e){Boolean(!e||e.current)}const Xa=()=>({scrollX:xn(0),scrollY:xn(0),scrollXProgress:xn(0),scrollYProgress:xn(0)});function Za({container:e,target:s,layoutEffect:n=!0,...r}={}){const i=w(Xa);return(n?k:t.useEffect)((()=>(Ya(0,s),Ya(0,e),Ja(((t,{x:e,y:s})=>{i.scrollX.set(e.current),i.scrollXProgress.set(e.progress),i.scrollY.set(s.current),i.scrollYProgress.set(s.progress)}),{...r,container:e?.current||void 0,target:s?.current||void 0}))),[e,s,JSON.stringify(r.offset)]),i}function Qa(e,s){const n=function(e){const s=w((()=>xn(e))),{isStatic:n}=t.useContext(T);if(n){const[,n]=t.useState(e);t.useEffect((()=>s.on("change",n)),[])}return s}(s()),r=()=>n.set(s());return r(),k((()=>{const t=()=>ft.preRender(r,!1,!0),s=e.map((e=>e.on("change",t)));return()=>{s.forEach((t=>t())),mt(r)}})),n}function tl(t,e,s,n){if("function"==typeof t)return function(t){bn.current=[],t();const e=Qa(bn.current,t);return bn.current=void 0,e}(t);const r="function"==typeof e?e:function(...t){const e=!Array.isArray(t[0]),s=e?0:-1,n=t[0+s],r=Fe(t[1+s],t[2+s],t[3+s]);return e?r(n):r}(e,s,n);return Array.isArray(t)?el(t,r):el([t],(([t])=>r(t)))}function el(t,e){const s=w((()=>[]));return Qa(t,(()=>{s.length=0;const n=t.length;for(let e=0;e<n;e++)s[e]=t[e].get();return e(s)}))}const sl={},nl=function(t,e,s){let n=Promise.resolve();if(e&&e.length>0){document.getElementsByTagName("link");const t=document.querySelector("meta[property=csp-nonce]"),s=t?.nonce||t?.getAttribute("nonce");n=Promise.allSettled(e.map((t=>{if((t=function(t){return"/"+t}(t))in sl)return;sl[t]=!0;const e=t.endsWith(".css"),n=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${n}`))return;const r=document.createElement("link");return r.rel=e?"stylesheet":"modulepreload",e||(r.as="script"),r.crossOrigin="",r.href=t,s&&r.setAttribute("nonce",s),document.head.appendChild(r),e?new Promise(((e,s)=>{r.addEventListener("load",e),r.addEventListener("error",(()=>s(new Error(`Unable to preload CSS for ${t}`))))})):void 0})))}function r(t){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=t,window.dispatchEvent(e),!e.defaultPrevented)throw t}return n.then((e=>{for(const t of e||[])"rejected"===t.status&&r(t.reason);return t().catch(r)}))};class rl extends Error{constructor(t,e="FunctionsError",s){super(t),this.name=e,this.context=s}}class il extends rl{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class ol extends rl{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class al extends rl{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var ll;!function(t){t.Any="any",t.ApNortheast1="ap-northeast-1",t.ApNortheast2="ap-northeast-2",t.ApSouth1="ap-south-1",t.ApSoutheast1="ap-southeast-1",t.ApSoutheast2="ap-southeast-2",t.CaCentral1="ca-central-1",t.EuCentral1="eu-central-1",t.EuWest1="eu-west-1",t.EuWest2="eu-west-2",t.EuWest3="eu-west-3",t.SaEast1="sa-east-1",t.UsEast1="us-east-1",t.UsWest1="us-west-1",t.UsWest2="us-west-2"}(ll||(ll={}));var cl=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};class hl{constructor(t,{headers:e={},customFetch:s,region:n=ll.Any}={}){this.url=t,this.headers=e,this.region=n,this.fetch=(t=>{let e;return e=t||("undefined"==typeof fetch?(...t)=>nl((async()=>{const{default:t}=await Promise.resolve().then((()=>_l));return{default:t}}),void 0).then((({default:e})=>e(...t))):fetch),(...t)=>e(...t)})(s)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,e={}){var s;return cl(this,void 0,void 0,(function*(){try{const{headers:n,method:r,body:i}=e;let o,a={},{region:l}=e;l||(l=this.region),l&&"any"!==l&&(a["x-region"]=l),i&&(n&&!Object.prototype.hasOwnProperty.call(n,"Content-Type")||!n)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",o=i):"string"==typeof i?(a["Content-Type"]="text/plain",o=i):"undefined"!=typeof FormData&&i instanceof FormData?o=i:(a["Content-Type"]="application/json",o=JSON.stringify(i)));const c=yield this.fetch(`${this.url}/${t}`,{method:r||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),n),body:o}).catch((t=>{throw new il(t)})),h=c.headers.get("x-relay-error");if(h&&"true"===h)throw new ol(c);if(!c.ok)throw new al(c);let u,d=(null!==(s=c.headers.get("Content-Type"))&&void 0!==s?s:"text/plain").split(";")[0].trim();return u="application/json"===d?yield c.json():"application/octet-stream"===d?yield c.blob():"text/event-stream"===d?c:"multipart/form-data"===d?yield c.formData():yield c.text(),{data:u,error:null}}catch(n){return{data:null,error:n}}}))}}var ul={},dl={},pl={},fl={},ml={},gl={},yl=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const vl=yl.fetch,bl=yl.fetch.bind(yl),wl=yl.Headers,xl=yl.Request,kl=yl.Response,_l=Object.freeze(Object.defineProperty({__proto__:null,Headers:wl,Request:xl,Response:kl,default:bl,fetch:vl},Symbol.toStringTag,{value:"Module"})),Tl=s(_l);var Sl={};Object.defineProperty(Sl,"__esModule",{value:!0});let Pl=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};Sl.default=Pl;var jl=n&&n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(gl,"__esModule",{value:!0});const El=jl(Tl),Al=jl(Sl);gl.default=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:"undefined"==typeof fetch?this.fetch=El.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,e){return this.headers=Object.assign({},this.headers),this.headers[t]=e,this}then(t,e){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((async t=>{var e,s,n;let r=null,i=null,o=null,a=t.status,l=t.statusText;if(t.ok){if("HEAD"!==this.method){const e=await t.text();""===e||(i="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?e:JSON.parse(e))}const n=null===(e=this.headers.Prefer)||void 0===e?void 0:e.match(/count=(exact|planned|estimated)/),c=null===(s=t.headers.get("content-range"))||void 0===s?void 0:s.split("/");n&&c&&c.length>1&&(o=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(i)&&(i.length>1?(r={code:"PGRST116",details:`Results contain ${i.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},i=null,o=null,a=406,l="Not Acceptable"):i=1===i.length?i[0]:null)}else{const e=await t.text();try{r=JSON.parse(e),Array.isArray(r)&&404===t.status&&(i=[],r=null,a=200,l="OK")}catch(c){404===t.status&&""===e?(a=204,l="No Content"):r={message:e}}if(r&&this.isMaybeSingle&&(null===(n=null==r?void 0:r.details)||void 0===n?void 0:n.includes("0 rows"))&&(r=null,a=200,l="OK"),r&&this.shouldThrowOnError)throw new Al.default(r)}return{error:r,data:i,count:o,status:a,statusText:l}}));return this.shouldThrowOnError||(s=s.catch((t=>{var e,s,n;return{error:{message:`${null!==(e=null==t?void 0:t.name)&&void 0!==e?e:"FetchError"}: ${null==t?void 0:t.message}`,details:`${null!==(s=null==t?void 0:t.stack)&&void 0!==s?s:""}`,hint:"",code:`${null!==(n=null==t?void 0:t.code)&&void 0!==n?n:""}`},data:null,count:null,status:0,statusText:""}}))),s.then(t,e)}returns(){return this}overrideTypes(){return this}};var Cl=n&&n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ml,"__esModule",{value:!0});const Rl=Cl(gl);let Ol=class extends Rl.default{select(t){let e=!1;const s=(null!=t?t:"*").split("").map((t=>/\s/.test(t)&&!e?"":('"'===t&&(e=!e),t))).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:e=!0,nullsFirst:s,foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.order`:"order",o=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${o?`${o},`:""}${t}.${e?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:e,referencedTable:s=e}={}){const n=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(n,`${t}`),this}range(t,e,{foreignTable:s,referencedTable:n=s}={}){const r=void 0===n?"offset":`${n}.offset`,i=void 0===n?"limit":`${n}.limit`;return this.url.searchParams.set(r,`${t}`),this.url.searchParams.set(i,""+(e-t+1)),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:e=!1,settings:s=!1,buffers:n=!1,wal:r=!1,format:i="text"}={}){var o;const a=[t?"analyze":null,e?"verbose":null,s?"settings":null,n?"buffers":null,r?"wal":null].filter(Boolean).join("|"),l=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${l}"; options=${a};`,this}rollback(){var t;return(null!==(t=this.headers.Prefer)&&void 0!==t?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};ml.default=Ol;var Ml=n&&n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(fl,"__esModule",{value:!0});const Ll=Ml(ml);let Dl=class extends Ll.default{eq(t,e){return this.url.searchParams.append(t,`eq.${e}`),this}neq(t,e){return this.url.searchParams.append(t,`neq.${e}`),this}gt(t,e){return this.url.searchParams.append(t,`gt.${e}`),this}gte(t,e){return this.url.searchParams.append(t,`gte.${e}`),this}lt(t,e){return this.url.searchParams.append(t,`lt.${e}`),this}lte(t,e){return this.url.searchParams.append(t,`lte.${e}`),this}like(t,e){return this.url.searchParams.append(t,`like.${e}`),this}likeAllOf(t,e){return this.url.searchParams.append(t,`like(all).{${e.join(",")}}`),this}likeAnyOf(t,e){return this.url.searchParams.append(t,`like(any).{${e.join(",")}}`),this}ilike(t,e){return this.url.searchParams.append(t,`ilike.${e}`),this}ilikeAllOf(t,e){return this.url.searchParams.append(t,`ilike(all).{${e.join(",")}}`),this}ilikeAnyOf(t,e){return this.url.searchParams.append(t,`ilike(any).{${e.join(",")}}`),this}is(t,e){return this.url.searchParams.append(t,`is.${e}`),this}in(t,e){const s=Array.from(new Set(e)).map((t=>"string"==typeof t&&new RegExp("[,()]").test(t)?`"${t}"`:`${t}`)).join(",");return this.url.searchParams.append(t,`in.(${s})`),this}contains(t,e){return"string"==typeof e?this.url.searchParams.append(t,`cs.${e}`):Array.isArray(e)?this.url.searchParams.append(t,`cs.{${e.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(e)}`),this}containedBy(t,e){return"string"==typeof e?this.url.searchParams.append(t,`cd.${e}`):Array.isArray(e)?this.url.searchParams.append(t,`cd.{${e.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(e)}`),this}rangeGt(t,e){return this.url.searchParams.append(t,`sr.${e}`),this}rangeGte(t,e){return this.url.searchParams.append(t,`nxl.${e}`),this}rangeLt(t,e){return this.url.searchParams.append(t,`sl.${e}`),this}rangeLte(t,e){return this.url.searchParams.append(t,`nxr.${e}`),this}rangeAdjacent(t,e){return this.url.searchParams.append(t,`adj.${e}`),this}overlaps(t,e){return"string"==typeof e?this.url.searchParams.append(t,`ov.${e}`):this.url.searchParams.append(t,`ov.{${e.join(",")}}`),this}textSearch(t,e,{config:s,type:n}={}){let r="";"plain"===n?r="pl":"phrase"===n?r="ph":"websearch"===n&&(r="w");const i=void 0===s?"":`(${s})`;return this.url.searchParams.append(t,`${r}fts${i}.${e}`),this}match(t){return Object.entries(t).forEach((([t,e])=>{this.url.searchParams.append(t,`eq.${e}`)})),this}not(t,e,s){return this.url.searchParams.append(t,`not.${e}.${s}`),this}or(t,{foreignTable:e,referencedTable:s=e}={}){const n=s?`${s}.or`:"or";return this.url.searchParams.append(n,`(${t})`),this}filter(t,e,s){return this.url.searchParams.append(t,`${e}.${s}`),this}};fl.default=Dl;var Il=n&&n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(pl,"__esModule",{value:!0});const $l=Il(fl);pl.default=class{constructor(t,{headers:e={},schema:s,fetch:n}){this.url=t,this.headers=e,this.schema=s,this.fetch=n}select(t,{head:e=!1,count:s}={}){const n=e?"HEAD":"GET";let r=!1;const i=(null!=t?t:"*").split("").map((t=>/\s/.test(t)&&!r?"":('"'===t&&(r=!r),t))).join("");return this.url.searchParams.set("select",i),s&&(this.headers.Prefer=`count=${s}`),new $l.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:e,defaultToNull:s=!0}={}){const n=[];if(this.headers.Prefer&&n.push(this.headers.Prefer),e&&n.push(`count=${e}`),s||n.push("missing=default"),this.headers.Prefer=n.join(","),Array.isArray(t)){const e=t.reduce(((t,e)=>t.concat(Object.keys(e))),[]);if(e.length>0){const t=[...new Set(e)].map((t=>`"${t}"`));this.url.searchParams.set("columns",t.join(","))}}return new $l.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:e,ignoreDuplicates:s=!1,count:n,defaultToNull:r=!0}={}){const i=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==e&&this.url.searchParams.set("on_conflict",e),this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(t)){const e=t.reduce(((t,e)=>t.concat(Object.keys(e))),[]);if(e.length>0){const t=[...new Set(e)].map((t=>`"${t}"`));this.url.searchParams.set("columns",t.join(","))}}return new $l.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:e}={}){const s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),e&&s.push(`count=${e}`),this.headers.Prefer=s.join(","),new $l.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const e=[];return t&&e.push(`count=${t}`),this.headers.Prefer&&e.unshift(this.headers.Prefer),this.headers.Prefer=e.join(","),new $l.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var Nl={},Vl={};Object.defineProperty(Vl,"__esModule",{value:!0}),Vl.version=void 0,Vl.version="0.0.0-automated",Object.defineProperty(Nl,"__esModule",{value:!0}),Nl.DEFAULT_HEADERS=void 0;const Ul=Vl;Nl.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Ul.version}`};var Bl=n&&n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(dl,"__esModule",{value:!0});const Fl=Bl(pl),zl=Bl(fl),Wl=Nl;dl.default=class t{constructor(t,{headers:e={},schema:s,fetch:n}={}){this.url=t,this.headers=Object.assign(Object.assign({},Wl.DEFAULT_HEADERS),e),this.schemaName=s,this.fetch=n}from(t){const e=new URL(`${this.url}/${t}`);return new Fl.default(e,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new t(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(t,e={},{head:s=!1,get:n=!1,count:r}={}){let i;const o=new URL(`${this.url}/rpc/${t}`);let a;s||n?(i=s?"HEAD":"GET",Object.entries(e).filter((([t,e])=>void 0!==e)).map((([t,e])=>[t,Array.isArray(e)?`{${e.join(",")}}`:`${e}`])).forEach((([t,e])=>{o.searchParams.append(t,e)}))):(i="POST",a=e);const l=Object.assign({},this.headers);return r&&(l.Prefer=`count=${r}`),new zl.default({method:i,url:o,headers:l,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}};var ql=n&&n.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(ul,"__esModule",{value:!0}),ul.PostgrestError=ul.PostgrestBuilder=ul.PostgrestTransformBuilder=ul.PostgrestFilterBuilder=ul.PostgrestQueryBuilder=ul.PostgrestClient=void 0;const Hl=ql(dl);ul.PostgrestClient=Hl.default;const Gl=ql(pl);ul.PostgrestQueryBuilder=Gl.default;const Kl=ql(fl);ul.PostgrestFilterBuilder=Kl.default;const Jl=ql(ml);ul.PostgrestTransformBuilder=Jl.default;const Yl=ql(gl);ul.PostgrestBuilder=Yl.default;const Xl=ql(Sl);ul.PostgrestError=Xl.default;var Zl=ul.default={PostgrestClient:Hl.default,PostgrestQueryBuilder:Gl.default,PostgrestFilterBuilder:Kl.default,PostgrestTransformBuilder:Jl.default,PostgrestBuilder:Yl.default,PostgrestError:Xl.default};const{PostgrestClient:Ql,PostgrestQueryBuilder:tc,PostgrestFilterBuilder:ec,PostgrestTransformBuilder:sc,PostgrestBuilder:nc,PostgrestError:rc}=Zl,ic={"X-Client-Info":"realtime-js/2.11.2"};var oc,ac,lc,cc,hc,uc;!function(t){t[t.connecting=0]="connecting",t[t.open=1]="open",t[t.closing=2]="closing",t[t.closed=3]="closed"}(oc||(oc={})),function(t){t.closed="closed",t.errored="errored",t.joined="joined",t.joining="joining",t.leaving="leaving"}(ac||(ac={})),function(t){t.close="phx_close",t.error="phx_error",t.join="phx_join",t.reply="phx_reply",t.leave="phx_leave",t.access_token="access_token"}(lc||(lc={})),function(t){t.websocket="websocket"}(cc||(cc={})),function(t){t.Connecting="connecting",t.Open="open",t.Closing="closing",t.Closed="closed"}(hc||(hc={}));class dc{constructor(){this.HEADER_LENGTH=1}decode(t,e){return t.constructor===ArrayBuffer?e(this._binaryDecode(t)):e("string"==typeof t?JSON.parse(t):{})}_binaryDecode(t){const e=new DataView(t),s=new TextDecoder;return this._decodeBroadcast(t,e,s)}_decodeBroadcast(t,e,s){const n=e.getUint8(1),r=e.getUint8(2);let i=this.HEADER_LENGTH+2;const o=s.decode(t.slice(i,i+n));i+=n;const a=s.decode(t.slice(i,i+r));i+=r;return{ref:null,topic:o,event:a,payload:JSON.parse(s.decode(t.slice(i,t.byteLength)))}}}class pc{constructor(t,e){this.callback=t,this.timerCalc=e,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=e}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.tries=this.tries+1,this.callback()}),this.timerCalc(this.tries+1))}}!function(t){t.abstime="abstime",t.bool="bool",t.date="date",t.daterange="daterange",t.float4="float4",t.float8="float8",t.int2="int2",t.int4="int4",t.int4range="int4range",t.int8="int8",t.int8range="int8range",t.json="json",t.jsonb="jsonb",t.money="money",t.numeric="numeric",t.oid="oid",t.reltime="reltime",t.text="text",t.time="time",t.timestamp="timestamp",t.timestamptz="timestamptz",t.timetz="timetz",t.tsrange="tsrange",t.tstzrange="tstzrange"}(uc||(uc={}));const fc=(t,e,s={})=>{var n;const r=null!==(n=s.skipTypes)&&void 0!==n?n:[];return Object.keys(e).reduce(((s,n)=>(s[n]=mc(n,t,e,r),s)),{})},mc=(t,e,s,n)=>{const r=e.find((e=>e.name===t)),i=null==r?void 0:r.type,o=s[t];return i&&!n.includes(i)?gc(i,o):yc(o)},gc=(t,e)=>{if("_"===t.charAt(0)){const s=t.slice(1,t.length);return xc(e,s)}switch(t){case uc.bool:return vc(e);case uc.float4:case uc.float8:case uc.int2:case uc.int4:case uc.int8:case uc.numeric:case uc.oid:return bc(e);case uc.json:case uc.jsonb:return wc(e);case uc.timestamp:return kc(e);case uc.abstime:case uc.date:case uc.daterange:case uc.int4range:case uc.int8range:case uc.money:case uc.reltime:case uc.text:case uc.time:case uc.timestamptz:case uc.timetz:case uc.tsrange:case uc.tstzrange:default:return yc(e)}},yc=t=>t,vc=t=>{switch(t){case"t":return!0;case"f":return!1;default:return t}},bc=t=>{if("string"==typeof t){const e=parseFloat(t);if(!Number.isNaN(e))return e}return t},wc=t=>{if("string"==typeof t)try{return JSON.parse(t)}catch(e){return t}return t},xc=(t,e)=>{if("string"!=typeof t)return t;const s=t.length-1,n=t[s];if("{"===t[0]&&"}"===n){let n;const i=t.slice(1,s);try{n=JSON.parse("["+i+"]")}catch(r){n=i?i.split(","):[]}return n.map((t=>gc(e,t)))}return t},kc=t=>"string"==typeof t?t.replace(" ","T"):t,_c=t=>{let e=t;return e=e.replace(/^ws/i,"http"),e=e.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),e.replace(/\/+$/,"")};class Tc{constructor(t,e,s={},n=1e4){this.channel=t,this.event=e,this.payload=s,this.timeout=n,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,e){var s;return this._hasReceived(t)&&e(null===(s=this.receivedResp)||void 0===s?void 0:s.response),this.recHooks.push({status:t,callback:e}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},(t=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=t,this._matchReceive(t)})),this.timeoutTimer=setTimeout((()=>{this.trigger("timeout",{})}),this.timeout)}trigger(t,e){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:e})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:e}){this.recHooks.filter((e=>e.status===t)).forEach((t=>t.callback(e)))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var Sc,Pc,jc,Ec;!function(t){t.SYNC="sync",t.JOIN="join",t.LEAVE="leave"}(Sc||(Sc={}));class Ac{constructor(t,e){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(null==e?void 0:e.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},(t=>{const{onJoin:e,onLeave:s,onSync:n}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Ac.syncState(this.state,t,e,s),this.pendingDiffs.forEach((t=>{this.state=Ac.syncDiff(this.state,t,e,s)})),this.pendingDiffs=[],n()})),this.channel._on(s.diff,{},(t=>{const{onJoin:e,onLeave:s,onSync:n}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(t):(this.state=Ac.syncDiff(this.state,t,e,s),n())})),this.onJoin(((t,e,s)=>{this.channel._trigger("presence",{event:"join",key:t,currentPresences:e,newPresences:s})})),this.onLeave(((t,e,s)=>{this.channel._trigger("presence",{event:"leave",key:t,currentPresences:e,leftPresences:s})})),this.onSync((()=>{this.channel._trigger("presence",{event:"sync"})}))}static syncState(t,e,s,n){const r=this.cloneDeep(t),i=this.transformState(e),o={},a={};return this.map(r,((t,e)=>{i[t]||(a[t]=e)})),this.map(i,((t,e)=>{const s=r[t];if(s){const n=e.map((t=>t.presence_ref)),r=s.map((t=>t.presence_ref)),i=e.filter((t=>r.indexOf(t.presence_ref)<0)),l=s.filter((t=>n.indexOf(t.presence_ref)<0));i.length>0&&(o[t]=i),l.length>0&&(a[t]=l)}else o[t]=e})),this.syncDiff(r,{joins:o,leaves:a},s,n)}static syncDiff(t,e,s,n){const{joins:r,leaves:i}={joins:this.transformState(e.joins),leaves:this.transformState(e.leaves)};return s||(s=()=>{}),n||(n=()=>{}),this.map(r,((e,n)=>{var r;const i=null!==(r=t[e])&&void 0!==r?r:[];if(t[e]=this.cloneDeep(n),i.length>0){const s=t[e].map((t=>t.presence_ref)),n=i.filter((t=>s.indexOf(t.presence_ref)<0));t[e].unshift(...n)}s(e,i,n)})),this.map(i,((e,s)=>{let r=t[e];if(!r)return;const i=s.map((t=>t.presence_ref));r=r.filter((t=>i.indexOf(t.presence_ref)<0)),t[e]=r,n(e,r,s),0===r.length&&delete t[e]})),t}static map(t,e){return Object.getOwnPropertyNames(t).map((s=>e(s,t[s])))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce(((e,s)=>{const n=t[s];return e[s]="metas"in n?n.metas.map((t=>(t.presence_ref=t.phx_ref,delete t.phx_ref,delete t.phx_ref_prev,t))):n,e}),{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(t){t.ALL="*",t.INSERT="INSERT",t.UPDATE="UPDATE",t.DELETE="DELETE"}(Pc||(Pc={})),function(t){t.BROADCAST="broadcast",t.PRESENCE="presence",t.POSTGRES_CHANGES="postgres_changes",t.SYSTEM="system"}(jc||(jc={})),function(t){t.SUBSCRIBED="SUBSCRIBED",t.TIMED_OUT="TIMED_OUT",t.CLOSED="CLOSED",t.CHANNEL_ERROR="CHANNEL_ERROR"}(Ec||(Ec={}));class Cc{constructor(t,e={config:{}},s){this.topic=t,this.params=e,this.socket=s,this.bindings={},this.state=ac.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},e.config),this.timeout=this.socket.timeout,this.joinPush=new Tc(this,lc.join,this.params,this.timeout),this.rejoinTimer=new pc((()=>this._rejoinUntilConnected()),this.socket.reconnectAfterMs),this.joinPush.receive("ok",(()=>{this.state=ac.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach((t=>t.send())),this.pushBuffer=[]})),this._onClose((()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=ac.closed,this.socket._remove(this)})),this._onError((t=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,t),this.state=ac.errored,this.rejoinTimer.scheduleTimeout())})),this.joinPush.receive("timeout",(()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=ac.errored,this.rejoinTimer.scheduleTimeout())})),this._on(lc.reply,{},((t,e)=>{this._trigger(this._replyEventName(e),t)})),this.presence=new Ac(this),this.broadcastEndpointURL=_c(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,e=this.timeout){var s,n;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:r,presence:i,private:o}}=this.params;this._onError((e=>null==t?void 0:t(Ec.CHANNEL_ERROR,e))),this._onClose((()=>null==t?void 0:t(Ec.CLOSED)));const a={},l={broadcast:r,presence:i,postgres_changes:null!==(n=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map((t=>t.filter)))&&void 0!==n?n:[],private:o};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},a)),this.joinedOnce=!0,this._rejoin(e),this.joinPush.receive("ok",(async({postgres_changes:e})=>{var s;if(this.socket.setAuth(),void 0!==e){const n=this.bindings.postgres_changes,r=null!==(s=null==n?void 0:n.length)&&void 0!==s?s:0,i=[];for(let s=0;s<r;s++){const r=n[s],{filter:{event:o,schema:a,table:l,filter:c}}=r,h=e&&e[s];if(!h||h.event!==o||h.schema!==a||h.table!==l||h.filter!==c)return this.unsubscribe(),void(null==t||t(Ec.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));i.push(Object.assign(Object.assign({},r),{id:h.id}))}return this.bindings.postgres_changes=i,void(t&&t(Ec.SUBSCRIBED))}null==t||t(Ec.SUBSCRIBED)})).receive("error",(e=>{null==t||t(Ec.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(e).join(", ")||"error")))})).receive("timeout",(()=>{null==t||t(Ec.TIMED_OUT)}))}return this}presenceState(){return this.presence.state}async track(t,e={}){return await this.send({type:"presence",event:"track",payload:t},e.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,e,s){return this._on(t,e,s)}async send(t,e={}){var s,n;if(this._canPush()||"broadcast"!==t.type)return new Promise((s=>{var n,r,i;const o=this._push(t.type,t,e.timeout||this.timeout);"broadcast"!==t.type||(null===(i=null===(r=null===(n=this.params)||void 0===n?void 0:n.config)||void 0===r?void 0:r.broadcast)||void 0===i?void 0:i.ack)||s("ok"),o.receive("ok",(()=>s("ok"))),o.receive("error",(()=>s("error"))),o.receive("timeout",(()=>s("timed out")))}));{const{event:i,payload:o}=t,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:o,private:this.private}]})};try{const t=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(s=e.timeout)&&void 0!==s?s:this.timeout);return await(null===(n=t.body)||void 0===n?void 0:n.cancel()),t.ok?"ok":"error"}catch(r){return"AbortError"===r.name?"timed out":"error"}}}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=ac.leaving;const e=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(lc.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise((s=>{const n=new Tc(this,lc.leave,{},t);n.receive("ok",(()=>{e(),s("ok")})).receive("timeout",(()=>{e(),s("timed out")})).receive("error",(()=>{s("error")})),n.send(),this._canPush()||n.trigger("ok",{})}))}async _fetchWithTimeout(t,e,s){const n=new AbortController,r=setTimeout((()=>n.abort()),s),i=await this.socket.fetch(t,Object.assign(Object.assign({},e),{signal:n.signal}));return clearTimeout(r),i}_push(t,e,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let n=new Tc(this,t,e,s);return this._canPush()?n.send():(n.startTimeout(),this.pushBuffer.push(n)),n}_onMessage(t,e,s){return e}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,e,s){var n,r;const i=t.toLocaleLowerCase(),{close:o,error:a,leave:l,join:c}=lc;if(s&&[o,a,l,c].indexOf(i)>=0&&s!==this._joinRef())return;let h=this._onMessage(i,e,s);if(e&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(n=this.bindings.postgres_changes)||void 0===n||n.filter((t=>{var e,s,n;return"*"===(null===(e=t.filter)||void 0===e?void 0:e.event)||(null===(n=null===(s=t.filter)||void 0===s?void 0:s.event)||void 0===n?void 0:n.toLocaleLowerCase())===i})).map((t=>t.callback(h,s))):null===(r=this.bindings[i])||void 0===r||r.filter((t=>{var s,n,r,o,a,l;if(["broadcast","presence","postgres_changes"].includes(i)){if("id"in t){const i=t.id,o=null===(s=t.filter)||void 0===s?void 0:s.event;return i&&(null===(n=e.ids)||void 0===n?void 0:n.includes(i))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(r=e.data)||void 0===r?void 0:r.type.toLocaleLowerCase()))}{const s=null===(a=null===(o=null==t?void 0:t.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===s||s===(null===(l=null==e?void 0:e.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return t.type.toLocaleLowerCase()===i})).map((t=>{if("object"==typeof h&&"ids"in h){const t=h.data,{schema:e,table:s,commit_timestamp:n,type:r,errors:i}=t,o={schema:e,table:s,commit_timestamp:n,eventType:r,new:{},old:{},errors:i};h=Object.assign(Object.assign({},o),this._getPayloadRecords(t))}t.callback(h,s)}))}_isClosed(){return this.state===ac.closed}_isJoined(){return this.state===ac.joined}_isJoining(){return this.state===ac.joining}_isLeaving(){return this.state===ac.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,e,s){const n=t.toLocaleLowerCase(),r={type:n,filter:e,callback:s};return this.bindings[n]?this.bindings[n].push(r):this.bindings[n]=[r],this}_off(t,e){const s=t.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter((t=>{var n;return!((null===(n=t.type)||void 0===n?void 0:n.toLocaleLowerCase())===s&&Cc.isEqual(t.filter,e))})),this}static isEqual(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(const s in t)if(t[s]!==e[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(lc.close,{},t)}_onError(t){this._on(lc.error,{},(e=>t(e)))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=ac.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const e={new:{},old:{}};return"INSERT"!==t.type&&"UPDATE"!==t.type||(e.new=fc(t.columns,t.record)),"UPDATE"!==t.type&&"DELETE"!==t.type||(e.old=fc(t.columns,t.old_record)),e}}const Rc=()=>{},Oc="undefined"!=typeof WebSocket;class Mc{constructor(t,e){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=ic,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=Rc,this.conn=null,this.sendBuffer=[],this.serializer=new dc,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=t=>{let e;return e=t||("undefined"==typeof fetch?(...t)=>nl((async()=>{const{default:t}=await Promise.resolve().then((()=>_l));return{default:t}}),void 0).then((({default:e})=>e(...t))):fetch),(...t)=>e(...t)},this.endPoint=`${t}/${cc.websocket}`,this.httpEndpoint=_c(t),(null==e?void 0:e.transport)?this.transport=e.transport:this.transport=null,(null==e?void 0:e.params)&&(this.params=e.params),(null==e?void 0:e.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),e.headers)),(null==e?void 0:e.timeout)&&(this.timeout=e.timeout),(null==e?void 0:e.logger)&&(this.logger=e.logger),(null==e?void 0:e.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=e.heartbeatIntervalMs);const n=null===(s=null==e?void 0:e.params)||void 0===s?void 0:s.apikey;if(n&&(this.accessTokenValue=n,this.apiKey=n),this.reconnectAfterMs=(null==e?void 0:e.reconnectAfterMs)?e.reconnectAfterMs:t=>[1e3,2e3,5e3,1e4][t-1]||1e4,this.encode=(null==e?void 0:e.encode)?e.encode:(t,e)=>e(JSON.stringify(t)),this.decode=(null==e?void 0:e.decode)?e.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new pc((async()=>{this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(null==e?void 0:e.fetch),null==e?void 0:e.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==e?void 0:e.worker)||!1,this.workerUrl=null==e?void 0:e.workerUrl}this.accessToken=(null==e?void 0:e.accessToken)||null}connect(){if(!this.conn)if(this.transport)this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});else{if(Oc)return this.conn=new WebSocket(this.endpointURL()),void this.setupConnection();this.conn=new Lc(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),nl((async()=>{const{default:t}=await import("./browser-C1LZzpRw.js").then((t=>t.b));return{default:t}}),__vite__mapDeps([0,1])).then((({default:t})=>{this.conn=new t(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()}))}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(t,e){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,null!=e?e:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(t){const e=await t.unsubscribe();return 0===this.channels.length&&this.disconnect(),e}async removeAllChannels(){const t=await Promise.all(this.channels.map((t=>t.unsubscribe())));return this.disconnect(),t}log(t,e,s){this.logger(t,e,s)}connectionState(){switch(this.conn&&this.conn.readyState){case oc.connecting:return hc.Connecting;case oc.open:return hc.Open;case oc.closing:return hc.Closing;default:return hc.Closed}}isConnected(){return this.connectionState()===hc.Open}channel(t,e={config:{}}){const s=new Cc(`realtime:${t}`,e,this);return this.channels.push(s),s}push(t){const{topic:e,event:s,payload:n,ref:r}=t,i=()=>{this.encode(t,(t=>{var e;null===(e=this.conn)||void 0===e||e.send(t)}))};this.log("push",`${e} ${s} (${r})`,n),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(t=null){let e=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(e){let t=null;try{t=JSON.parse(atob(e.split(".")[1]))}catch(s){}if(t&&t.exp){if(!(Math.floor(Date.now()/1e3)-t.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${t.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${t.exp}`)}this.accessTokenValue=e,this.channels.forEach((t=>{e&&t.updateJoinPayload({access_token:e}),t.joinedOnce&&t._isJoined()&&t._push(lc.access_token,{access_token:e})}))}}async sendHeartbeat(){var t;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),void(null===(t=this.conn)||void 0===t||t.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach((t=>t())),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let e=this.channels.find((e=>e.topic===t&&(e._isJoined()||e._isJoining())));e&&(this.log("transport",`leaving duplicate topic "${t}"`),e.unsubscribe())}_remove(t){this.channels=this.channels.filter((e=>e._joinRef()!==t._joinRef()))}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,(t=>{let{topic:e,event:s,payload:n,ref:r}=t;r&&r===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${n.status||""} ${e} ${s} ${r&&"("+r+")"||""}`,n),this.channels.filter((t=>t._isMember(e))).forEach((t=>t._trigger(s,n,r))),this.stateChangeCallbacks.message.forEach((e=>e(t)))}))}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=t=>{this.log("worker","worker error",t.message),this.workerRef.terminate()},this.workerRef.onmessage=t=>{"keepAlive"===t.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>this.sendHeartbeat()),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach((t=>t()))}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach((e=>e(t)))}_onConnError(t){this.log("transport",t.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach((e=>e(t)))}_triggerChanError(){this.channels.forEach((t=>t._trigger(lc.error)))}_appendParams(t,e){if(0===Object.keys(e).length)return t;const s=t.match(/\?/)?"&":"?";return`${t}${s}${new URLSearchParams(e)}`}_workerObjectUrl(t){let e;if(t)e=t;else{const t=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});e=URL.createObjectURL(t)}return e}}class Lc{constructor(t,e,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=oc.connecting,this.send=()=>{},this.url=null,this.url=t,this.close=s.close}}class Dc extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function Ic(t){return"object"==typeof t&&null!==t&&"__isStorageError"in t}class $c extends Dc{constructor(t,e){super(t),this.name="StorageApiError",this.status=e}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class Nc extends Dc{constructor(t,e){super(t),this.name="StorageUnknownError",this.originalError=e}}var Vc=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};const Uc=t=>{let e;return e=t||("undefined"==typeof fetch?(...t)=>nl((async()=>{const{default:t}=await Promise.resolve().then((()=>_l));return{default:t}}),void 0).then((({default:e})=>e(...t))):fetch),(...t)=>e(...t)},Bc=t=>{if(Array.isArray(t))return t.map((t=>Bc(t)));if("function"==typeof t||t!==Object(t))return t;const e={};return Object.entries(t).forEach((([t,s])=>{const n=t.replace(/([-_][a-z])/gi,(t=>t.toUpperCase().replace(/[-_]/g,"")));e[n]=Bc(s)})),e};var Fc=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};const zc=t=>t.msg||t.message||t.error_description||t.error||JSON.stringify(t),Wc=(t,e,s)=>Fc(void 0,void 0,void 0,(function*(){const n=yield Vc(void 0,void 0,void 0,(function*(){return"undefined"==typeof Response?(yield nl((()=>Promise.resolve().then((()=>_l))),void 0)).Response:Response}));t instanceof n&&!(null==s?void 0:s.noResolveJson)?t.json().then((s=>{e(new $c(zc(s),t.status||500))})).catch((t=>{e(new Nc(zc(t),t))})):e(new Nc(zc(t),t))}));function qc(t,e,s,n,r,i){return Fc(this,void 0,void 0,(function*(){return new Promise(((o,a)=>{t(s,((t,e,s,n)=>{const r={method:t,headers:(null==e?void 0:e.headers)||{}};return"GET"===t?r:(r.headers=Object.assign({"Content-Type":"application/json"},null==e?void 0:e.headers),n&&(r.body=JSON.stringify(n)),Object.assign(Object.assign({},r),s))})(e,n,r,i)).then((t=>{if(!t.ok)throw t;return(null==n?void 0:n.noResolveJson)?t:t.json()})).then((t=>o(t))).catch((t=>Wc(t,a,n)))}))}))}function Hc(t,e,s,n){return Fc(this,void 0,void 0,(function*(){return qc(t,"GET",e,s,n)}))}function Gc(t,e,s,n,r){return Fc(this,void 0,void 0,(function*(){return qc(t,"POST",e,n,r,s)}))}function Kc(t,e,s,n,r){return Fc(this,void 0,void 0,(function*(){return qc(t,"DELETE",e,n,r,s)}))}var Jc=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};const Yc={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Xc={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class Zc{constructor(t,e={},s,n){this.url=t,this.headers=e,this.bucketId=s,this.fetch=Uc(n)}uploadOrUpdate(t,e,s,n){return Jc(this,void 0,void 0,(function*(){try{let r;const i=Object.assign(Object.assign({},Xc),n);let o=Object.assign(Object.assign({},this.headers),"POST"===t&&{"x-upsert":String(i.upsert)});const a=i.metadata;"undefined"!=typeof Blob&&s instanceof Blob?(r=new FormData,r.append("cacheControl",i.cacheControl),a&&r.append("metadata",this.encodeMetadata(a)),r.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(r=s,r.append("cacheControl",i.cacheControl),a&&r.append("metadata",this.encodeMetadata(a))):(r=s,o["cache-control"]=`max-age=${i.cacheControl}`,o["content-type"]=i.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==n?void 0:n.headers)&&(o=Object.assign(Object.assign({},o),n.headers));const l=this._removeEmptyFolders(e),c=this._getFinalPath(l),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:r,headers:o},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{})),u=yield h.json();if(h.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(r){if(Ic(r))return{data:null,error:r};throw r}}))}upload(t,e,s){return Jc(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",t,e,s)}))}uploadToSignedUrl(t,e,s,n){return Jc(this,void 0,void 0,(function*(){const r=this._removeEmptyFolders(t),i=this._getFinalPath(r),o=new URL(this.url+`/object/upload/sign/${i}`);o.searchParams.set("token",e);try{let t;const e=Object.assign({upsert:Xc.upsert},n),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(e.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?(t=new FormData,t.append("cacheControl",e.cacheControl),t.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(t=s,t.append("cacheControl",e.cacheControl)):(t=s,i["cache-control"]=`max-age=${e.cacheControl}`,i["content-type"]=e.contentType);const a=yield this.fetch(o.toString(),{method:"PUT",body:t,headers:i}),l=yield a.json();if(a.ok)return{data:{path:r,fullPath:l.Key},error:null};return{data:null,error:l}}catch(a){if(Ic(a))return{data:null,error:a};throw a}}))}createSignedUploadUrl(t,e){return Jc(this,void 0,void 0,(function*(){try{let s=this._getFinalPath(t);const n=Object.assign({},this.headers);(null==e?void 0:e.upsert)&&(n["x-upsert"]="true");const r=yield Gc(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:n}),i=new URL(this.url+r.url),o=i.searchParams.get("token");if(!o)throw new Dc("No token returned by API");return{data:{signedUrl:i.toString(),path:t,token:o},error:null}}catch(s){if(Ic(s))return{data:null,error:s};throw s}}))}update(t,e,s){return Jc(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",t,e,s)}))}move(t,e,s){return Jc(this,void 0,void 0,(function*(){try{return{data:yield Gc(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:e,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(n){if(Ic(n))return{data:null,error:n};throw n}}))}copy(t,e,s){return Jc(this,void 0,void 0,(function*(){try{return{data:{path:(yield Gc(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:e,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(n){if(Ic(n))return{data:null,error:n};throw n}}))}createSignedUrl(t,e,s){return Jc(this,void 0,void 0,(function*(){try{let n=this._getFinalPath(t),r=yield Gc(this.fetch,`${this.url}/object/sign/${n}`,Object.assign({expiresIn:e},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers});const i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return r={signedUrl:encodeURI(`${this.url}${r.signedURL}${i}`)},{data:r,error:null}}catch(n){if(Ic(n))return{data:null,error:n};throw n}}))}createSignedUrls(t,e,s){return Jc(this,void 0,void 0,(function*(){try{const n=yield Gc(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:e,paths:t},{headers:this.headers}),r=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:n.map((t=>Object.assign(Object.assign({},t),{signedUrl:t.signedURL?encodeURI(`${this.url}${t.signedURL}${r}`):null}))),error:null}}catch(n){if(Ic(n))return{data:null,error:n};throw n}}))}download(t,e){return Jc(this,void 0,void 0,(function*(){const s=void 0!==(null==e?void 0:e.transform)?"render/image/authenticated":"object",n=this.transformOptsToQueryString((null==e?void 0:e.transform)||{}),r=n?`?${n}`:"";try{const e=this._getFinalPath(t),n=yield Hc(this.fetch,`${this.url}/${s}/${e}${r}`,{headers:this.headers,noResolveJson:!0});return{data:yield n.blob(),error:null}}catch(i){if(Ic(i))return{data:null,error:i};throw i}}))}info(t){return Jc(this,void 0,void 0,(function*(){const e=this._getFinalPath(t);try{const t=yield Hc(this.fetch,`${this.url}/object/info/${e}`,{headers:this.headers});return{data:Bc(t),error:null}}catch(s){if(Ic(s))return{data:null,error:s};throw s}}))}exists(t){return Jc(this,void 0,void 0,(function*(){const e=this._getFinalPath(t);try{return yield function(t,e,s,n){return Fc(this,void 0,void 0,(function*(){return qc(t,"HEAD",e,Object.assign(Object.assign({},s),{noResolveJson:!0}),n)}))}(this.fetch,`${this.url}/object/${e}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(Ic(s)&&s instanceof Nc){const t=s.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:s}}throw s}}))}getPublicUrl(t,e){const s=this._getFinalPath(t),n=[],r=(null==e?void 0:e.download)?`download=${!0===e.download?"":e.download}`:"";""!==r&&n.push(r);const i=void 0!==(null==e?void 0:e.transform)?"render/image":"object",o=this.transformOptsToQueryString((null==e?void 0:e.transform)||{});""!==o&&n.push(o);let a=n.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${s}${a}`)}}}remove(t){return Jc(this,void 0,void 0,(function*(){try{return{data:yield Kc(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(e){if(Ic(e))return{data:null,error:e};throw e}}))}list(t,e,s){return Jc(this,void 0,void 0,(function*(){try{const n=Object.assign(Object.assign(Object.assign({},Yc),e),{prefix:t||""});return{data:yield Gc(this.fetch,`${this.url}/object/list/${this.bucketId}`,n,{headers:this.headers},s),error:null}}catch(n){if(Ic(n))return{data:null,error:n};throw n}}))}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return"undefined"!=typeof Buffer?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const e=[];return t.width&&e.push(`width=${t.width}`),t.height&&e.push(`height=${t.height}`),t.resize&&e.push(`resize=${t.resize}`),t.format&&e.push(`format=${t.format}`),t.quality&&e.push(`quality=${t.quality}`),e.join("&")}}const Qc={"X-Client-Info":"storage-js/2.7.1"};var th=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};class eh{constructor(t,e={},s){this.url=t,this.headers=Object.assign(Object.assign({},Qc),e),this.fetch=Uc(s)}listBuckets(){return th(this,void 0,void 0,(function*(){try{return{data:yield Hc(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(Ic(t))return{data:null,error:t};throw t}}))}getBucket(t){return th(this,void 0,void 0,(function*(){try{return{data:yield Hc(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(e){if(Ic(e))return{data:null,error:e};throw e}}))}createBucket(t,e={public:!1}){return th(this,void 0,void 0,(function*(){try{return{data:yield Gc(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(Ic(s))return{data:null,error:s};throw s}}))}updateBucket(t,e){return th(this,void 0,void 0,(function*(){try{const s=yield function(t,e,s,n,r){return Fc(this,void 0,void 0,(function*(){return qc(t,"PUT",e,n,r,s)}))}(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:e.public,file_size_limit:e.fileSizeLimit,allowed_mime_types:e.allowedMimeTypes},{headers:this.headers});return{data:s,error:null}}catch(s){if(Ic(s))return{data:null,error:s};throw s}}))}emptyBucket(t){return th(this,void 0,void 0,(function*(){try{return{data:yield Gc(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(Ic(e))return{data:null,error:e};throw e}}))}deleteBucket(t){return th(this,void 0,void 0,(function*(){try{return{data:yield Kc(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(e){if(Ic(e))return{data:null,error:e};throw e}}))}}class sh extends eh{constructor(t,e={},s){super(t,e,s)}from(t){return new Zc(this.url,this.headers,t,this.fetch)}}let nh="";nh="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const rh={headers:{"X-Client-Info":`supabase-js-${nh}/2.49.8`}},ih={schema:"public"},oh={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ah={};var lh=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};const ch=t=>{let e;return e=t||("undefined"==typeof fetch?bl:fetch),(...t)=>e(...t)},hh=(t,e,s)=>{const n=ch(s),r="undefined"==typeof Headers?wl:Headers;return(s,i)=>lh(void 0,void 0,void 0,(function*(){var o;const a=null!==(o=yield e())&&void 0!==o?o:t;let l=new r(null==i?void 0:i.headers);return l.has("apikey")||l.set("apikey",t),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),n(s,Object.assign(Object.assign({},i),{headers:l}))}))};var uh=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};const dh="2.69.1",ph=3e4,fh=9e4,mh={"X-Client-Info":`gotrue-js/${dh}`},gh="X-Supabase-Api-Version",yh={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},vh=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class bh extends Error{constructor(t,e,s){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=e,this.code=s}}function wh(t){return"object"==typeof t&&null!==t&&"__isAuthError"in t}class xh extends bh{constructor(t,e,s){super(t,e,s),this.name="AuthApiError",this.status=e,this.code=s}}class kh extends bh{constructor(t,e){super(t),this.name="AuthUnknownError",this.originalError=e}}class _h extends bh{constructor(t,e,s,n){super(t,s,n),this.name=e,this.status=s}}class Th extends _h{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Sh extends _h{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ph extends _h{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class jh extends _h{constructor(t,e=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=e}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Eh extends _h{constructor(t,e=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=e}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Ah extends _h{constructor(t,e){super(t,"AuthRetryableFetchError",e,void 0)}}function Ch(t){return wh(t)&&"AuthRetryableFetchError"===t.name}class Rh extends _h{constructor(t,e,s){super(t,"AuthWeakPasswordError",e,"weak_password"),this.reasons=s}}class Oh extends _h{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const Mh="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Lh=" \t\n\r=".split(""),Dh=(()=>{const t=new Array(128);for(let e=0;e<t.length;e+=1)t[e]=-1;for(let e=0;e<Lh.length;e+=1)t[Lh[e].charCodeAt(0)]=-2;for(let e=0;e<Mh.length;e+=1)t[Mh[e].charCodeAt(0)]=e;return t})();function Ih(t,e,s){const n=Dh[t];if(!(n>-1)){if(-2===n)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(t)}"`)}for(e.queue=e.queue<<6|n,e.queuedBits+=6;e.queuedBits>=8;)s(e.queue>>e.queuedBits-8&255),e.queuedBits-=8}function $h(t){const e=[],s=t=>{e.push(String.fromCodePoint(t))},n={utf8seq:0,codepoint:0},r={queue:0,queuedBits:0},i=t=>{!function(t,e,s){if(0===e.utf8seq){if(t<=127)return void s(t);for(let s=1;s<6;s+=1)if(!(t>>7-s&1)){e.utf8seq=s;break}if(2===e.utf8seq)e.codepoint=31&t;else if(3===e.utf8seq)e.codepoint=15&t;else{if(4!==e.utf8seq)throw new Error("Invalid UTF-8 sequence");e.codepoint=7&t}e.utf8seq-=1}else if(e.utf8seq>0){if(t<=127)throw new Error("Invalid UTF-8 sequence");e.codepoint=e.codepoint<<6|63&t,e.utf8seq-=1,0===e.utf8seq&&s(e.codepoint)}}(t,n,s)};for(let o=0;o<t.length;o+=1)Ih(t.charCodeAt(o),r,i);return e.join("")}function Nh(t,e){if(!(t<=127)){if(t<=2047)return e(192|t>>6),void e(128|63&t);if(t<=65535)return e(224|t>>12),e(128|t>>6&63),void e(128|63&t);if(t<=1114111)return e(240|t>>18),e(128|t>>12&63),e(128|t>>6&63),void e(128|63&t);throw new Error(`Unrecognized Unicode codepoint: ${t.toString(16)}`)}e(t)}function Vh(t){const e=[],s={queue:0,queuedBits:0},n=t=>{e.push(t)};for(let r=0;r<t.length;r+=1)Ih(t.charCodeAt(r),s,n);return new Uint8Array(e)}function Uh(t){const e=[];return function(t,e){for(let s=0;s<t.length;s+=1){let n=t.charCodeAt(s);if(n>55295&&n<=56319){const e=1024*(n-55296)&65535;n=65536+(t.charCodeAt(s+1)-56320&65535|e),s+=1}Nh(n,e)}}(t,(t=>e.push(t))),new Uint8Array(e)}const Bh=()=>"undefined"!=typeof window&&"undefined"!=typeof document,Fh={tested:!1,writable:!1},zh=()=>{if(!Bh())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(Fh.tested)return Fh.writable;const t=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(t,t),globalThis.localStorage.removeItem(t),Fh.tested=!0,Fh.writable=!0}catch(e){Fh.tested=!0,Fh.writable=!1}return Fh.writable};const Wh=t=>{let e;return e=t||("undefined"==typeof fetch?(...t)=>nl((async()=>{const{default:t}=await Promise.resolve().then((()=>_l));return{default:t}}),void 0).then((({default:e})=>e(...t))):fetch),(...t)=>e(...t)},qh=async(t,e,s)=>{await t.setItem(e,JSON.stringify(s))},Hh=async(t,e)=>{const s=await t.getItem(e);if(!s)return null;try{return JSON.parse(s)}catch(n){return s}},Gh=async(t,e)=>{await t.removeItem(e)};class Kh{constructor(){this.promise=new Kh.promiseConstructor(((t,e)=>{this.resolve=t,this.reject=e}))}}function Jh(t){const e=t.split(".");if(3!==e.length)throw new Oh("Invalid JWT structure");for(let s=0;s<e.length;s++)if(!vh.test(e[s]))throw new Oh("JWT not in base64url format");return{header:JSON.parse($h(e[0])),payload:JSON.parse($h(e[1])),signature:Vh(e[2]),raw:{header:e[0],payload:e[1]}}}function Yh(t){return("0"+t.toString(16)).substr(-2)}async function Xh(t){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return t;const e=await async function(t){const e=(new TextEncoder).encode(t),s=await crypto.subtle.digest("SHA-256",e),n=new Uint8Array(s);return Array.from(n).map((t=>String.fromCharCode(t))).join("")}(t);return btoa(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function Zh(t,e,s=!1){const n=function(){const t=new Uint32Array(56);if("undefined"==typeof crypto){const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",e=t.length;let s="";for(let n=0;n<56;n++)s+=t.charAt(Math.floor(Math.random()*e));return s}return crypto.getRandomValues(t),Array.from(t,Yh).join("")}();let r=n;s&&(r+="/PASSWORD_RECOVERY"),await qh(t,`${e}-code-verifier`,r);const i=await Xh(n);return[i,n===i?"plain":"s256"]}Kh.promiseConstructor=Promise;const Qh=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var tu=function(t,e){var s={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(s[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(s[n[r]]=t[n[r]])}return s};const eu=t=>t.msg||t.message||t.error_description||t.error||JSON.stringify(t),su=[502,503,504];async function nu(t){var e,s;if(!("object"==typeof(s=t)&&null!==s&&"status"in s&&"ok"in s&&"json"in s&&"function"==typeof s.json))throw new Ah(eu(t),0);if(su.includes(t.status))throw new Ah(eu(t),t.status);let n,r;try{n=await t.json()}catch(o){throw new kh(eu(o),o)}const i=function(t){const e=t.headers.get(gh);if(!e)return null;if(!e.match(Qh))return null;try{return new Date(`${e}T00:00:00.0Z`)}catch(o){return null}}(t);if(i&&i.getTime()>=yh.timestamp&&"object"==typeof n&&n&&"string"==typeof n.code?r=n.code:"object"==typeof n&&n&&"string"==typeof n.error_code&&(r=n.error_code),r){if("weak_password"===r)throw new Rh(eu(n),t.status,(null===(e=n.weak_password)||void 0===e?void 0:e.reasons)||[]);if("session_not_found"===r)throw new Th}else if("object"==typeof n&&n&&"object"==typeof n.weak_password&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce(((t,e)=>t&&"string"==typeof e),!0))throw new Rh(eu(n),t.status,n.weak_password.reasons);throw new xh(eu(n),t.status||500,r)}async function ru(t,e,s,n){var r;const i=Object.assign({},null==n?void 0:n.headers);i[gh]||(i[gh]=yh.name),(null==n?void 0:n.jwt)&&(i.Authorization=`Bearer ${n.jwt}`);const o=null!==(r=null==n?void 0:n.query)&&void 0!==r?r:{};(null==n?void 0:n.redirectTo)&&(o.redirect_to=n.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await async function(t,e,s,n,r,i){const o=((t,e,s,n)=>{const r={method:t,headers:(null==e?void 0:e.headers)||{}};return"GET"===t?r:(r.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==e?void 0:e.headers),r.body=JSON.stringify(n),Object.assign(Object.assign({},r),s))})(e,n,r,i);let a;try{a=await t(s,Object.assign({},o))}catch(l){throw new Ah(eu(l),0)}a.ok||await nu(a);if(null==n?void 0:n.noResolveJson)return a;try{return await a.json()}catch(l){await nu(l)}}(t,e,s+a,{headers:i,noResolveJson:null==n?void 0:n.noResolveJson},{},null==n?void 0:n.body);return(null==n?void 0:n.xform)?null==n?void 0:n.xform(l):{data:Object.assign({},l),error:null}}function iu(t){var e;let s=null;var n;(function(t){return t.access_token&&t.refresh_token&&t.expires_in})(t)&&(s=Object.assign({},t),t.expires_at||(s.expires_at=(n=t.expires_in,Math.round(Date.now()/1e3)+n)));return{data:{session:s,user:null!==(e=t.user)&&void 0!==e?e:t},error:null}}function ou(t){const e=iu(t);return!e.error&&t.weak_password&&"object"==typeof t.weak_password&&Array.isArray(t.weak_password.reasons)&&t.weak_password.reasons.length&&t.weak_password.message&&"string"==typeof t.weak_password.message&&t.weak_password.reasons.reduce(((t,e)=>t&&"string"==typeof e),!0)&&(e.data.weak_password=t.weak_password),e}function au(t){var e;return{data:{user:null!==(e=t.user)&&void 0!==e?e:t},error:null}}function lu(t){return{data:t,error:null}}function cu(t){const{action_link:e,email_otp:s,hashed_token:n,redirect_to:r,verification_type:i}=t,o=tu(t,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:e,email_otp:s,hashed_token:n,redirect_to:r,verification_type:i},user:Object.assign({},o)},error:null}}function hu(t){return t}var uu=function(t,e){var s={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(s[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(n=Object.getOwnPropertySymbols(t);r<n.length;r++)e.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(t,n[r])&&(s[n[r]]=t[n[r]])}return s};class du{constructor({url:t="",headers:e={},fetch:s}){this.url=t,this.headers=e,this.fetch=Wh(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,e="global"){try{return await ru(this.fetch,"POST",`${this.url}/logout?scope=${e}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(s){if(wh(s))return{data:null,error:s};throw s}}async inviteUserByEmail(t,e={}){try{return await ru(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:e.data},headers:this.headers,redirectTo:e.redirectTo,xform:au})}catch(s){if(wh(s))return{data:{user:null},error:s};throw s}}async generateLink(t){try{const{options:e}=t,s=uu(t,["options"]),n=Object.assign(Object.assign({},s),e);return"newEmail"in s&&(n.new_email=null==s?void 0:s.newEmail,delete n.newEmail),await ru(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:n,headers:this.headers,xform:cu,redirectTo:null==e?void 0:e.redirectTo})}catch(e){if(wh(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(t){try{return await ru(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:au})}catch(e){if(wh(e))return{data:{user:null},error:e};throw e}}async listUsers(t){var e,s,n,r,i,o,a;try{const l={nextPage:null,lastPage:0,total:0},c=await ru(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(s=null===(e=null==t?void 0:t.page)||void 0===e?void 0:e.toString())&&void 0!==s?s:"",per_page:null!==(r=null===(n=null==t?void 0:t.perPage)||void 0===n?void 0:n.toString())&&void 0!==r?r:""},xform:hu});if(c.error)throw c.error;const h=await c.json(),u=null!==(i=c.headers.get("x-total-count"))&&void 0!==i?i:0,d=null!==(a=null===(o=c.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach((t=>{const e=parseInt(t.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(t.split(";")[1].split("=")[1]);l[`${s}Page`]=e})),l.total=parseInt(u)),{data:Object.assign(Object.assign({},h),l),error:null}}catch(l){if(wh(l))return{data:{users:[]},error:l};throw l}}async getUserById(t){try{return await ru(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:au})}catch(e){if(wh(e))return{data:{user:null},error:e};throw e}}async updateUserById(t,e){try{return await ru(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:e,headers:this.headers,xform:au})}catch(s){if(wh(s))return{data:{user:null},error:s};throw s}}async deleteUser(t,e=!1){try{return await ru(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:e},xform:au})}catch(s){if(wh(s))return{data:{user:null},error:s};throw s}}async _listFactors(t){try{const{data:e,error:s}=await ru(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:t=>({data:{factors:t},error:null})});return{data:e,error:s}}catch(e){if(wh(e))return{data:null,error:e};throw e}}async _deleteFactor(t){try{return{data:await ru(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(e){if(wh(e))return{data:null,error:e};throw e}}}const pu={getItem:t=>zh()?globalThis.localStorage.getItem(t):null,setItem:(t,e)=>{zh()&&globalThis.localStorage.setItem(t,e)},removeItem:t=>{zh()&&globalThis.localStorage.removeItem(t)}};function fu(t={}){return{getItem:e=>t[e]||null,setItem:(e,s)=>{t[e]=s},removeItem:e=>{delete t[e]}}}const mu=!!(globalThis&&zh()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class gu extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class yu extends gu{}async function vu(t,e,s){const n=new globalThis.AbortController;return e>0&&setTimeout((()=>{n.abort()}),e),await Promise.resolve().then((()=>globalThis.navigator.locks.request(t,0===e?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:n.signal},(async n=>{if(!n){if(0===e)throw new yu(`Acquiring an exclusive Navigator LockManager lock "${t}" immediately failed`);if(mu)try{await globalThis.navigator.locks.query()}catch(r){}return await s()}try{return await s()}finally{}}))))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(t){"undefined"!=typeof self&&(self.globalThis=self)}}();const bu={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:mh,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function wu(t,e,s){return await s()}class xu{constructor(t){var e,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=xu.nextInstanceID,xu.nextInstanceID+=1,this.instanceID>0&&Bh();const n=Object.assign(Object.assign({},bu),t);if(this.logDebugMessages=!!n.debug,"function"==typeof n.debug&&(this.logger=n.debug),this.persistSession=n.persistSession,this.storageKey=n.storageKey,this.autoRefreshToken=n.autoRefreshToken,this.admin=new du({url:n.url,headers:n.headers,fetch:n.fetch}),this.url=n.url,this.headers=n.headers,this.fetch=Wh(n.fetch),this.lock=n.lock||wu,this.detectSessionInUrl=n.detectSessionInUrl,this.flowType=n.flowType,this.hasCustomAuthorizationHeader=n.hasCustomAuthorizationHeader,n.lock?this.lock=n.lock:Bh()&&(null===(e=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===e?void 0:e.locks)?this.lock=vu:this.lock=wu,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?n.storage?this.storage=n.storage:zh()?this.storage=pu:(this.memoryStorage={},this.storage=fu(this.memoryStorage)):(this.memoryStorage={},this.storage=fu(this.memoryStorage)),Bh()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(r){}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",(async t=>{this._debug("received broadcast notification from other tab or client",t),await this._notifyAllSubscribers(t.data.event,t.data.session,!1)}))}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${dh}) ${(new Date).toISOString()}`,...t),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,(async()=>await this._initialize())))()),await this.initializePromise}async _initialize(){var t;try{const e=function(t){const e={},s=new URL(t);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach(((t,s)=>{e[s]=t}))}catch(n){}return s.searchParams.forEach(((t,s)=>{e[s]=t})),e}(window.location.href);let s="none";if(this._isImplicitGrantCallback(e)?s="implicit":await this._isPKCECallback(e)&&(s="pkce"),Bh()&&this.detectSessionInUrl&&"none"!==s){const{data:n,error:r}=await this._getSessionFromURL(e,s);if(r){if(this._debug("#_initialize()","error detecting session from URL",r),function(t){return wh(t)&&"AuthImplicitGrantRedirectError"===t.name}(r)){const e=null===(t=r.details)||void 0===t?void 0:t.code;if("identity_already_exists"===e||"identity_not_found"===e||"single_identity_not_deletable"===e)return{error:r}}return await this._removeSession(),{error:r}}const{session:i,redirectType:o}=n;return this._debug("#_initialize()","detected session in URL",i,"redirect type",o),await this._saveSession(i),setTimeout((async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)}),0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){return wh(e)?{error:e}:{error:new kh("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var e,s,n;try{const r=await ru(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(s=null===(e=null==t?void 0:t.options)||void 0===e?void 0:e.data)&&void 0!==s?s:{},gotrue_meta_security:{captcha_token:null===(n=null==t?void 0:t.options)||void 0===n?void 0:n.captchaToken}},xform:iu}),{data:i,error:o}=r;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(r){if(wh(r))return{data:{user:null,session:null},error:r};throw r}}async signUp(t){var e,s,n;try{let r;if("email"in t){const{email:s,password:n,options:i}=t;let o=null,a=null;"pkce"===this.flowType&&([o,a]=await Zh(this.storage,this.storageKey)),r=await ru(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:s,password:n,data:null!==(e=null==i?void 0:i.data)&&void 0!==e?e:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:o,code_challenge_method:a},xform:iu})}else{if(!("phone"in t))throw new Ph("You must provide either an email or phone number and a password");{const{phone:e,password:i,options:o}=t;r=await ru(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:e,password:i,data:null!==(s=null==o?void 0:o.data)&&void 0!==s?s:{},channel:null!==(n=null==o?void 0:o.channel)&&void 0!==n?n:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:iu})}}const{data:i,error:o}=r;if(o||!i)return{data:{user:null,session:null},error:o};const a=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(r){if(wh(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithPassword(t){try{let e;if("email"in t){const{email:s,password:n,options:r}=t;e=await ru(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}},xform:ou})}else{if(!("phone"in t))throw new Ph("You must provide either an email or phone number and a password");{const{phone:s,password:n,options:r}=t;e=await ru(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}},xform:ou})}}const{data:s,error:n}=e;return n?{data:{user:null,session:null},error:n}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:n}):{data:{user:null,session:null},error:new Sh}}catch(e){if(wh(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(t){var e,s,n,r;return await this._handleProviderSignIn(t.provider,{redirectTo:null===(e=t.options)||void 0===e?void 0:e.redirectTo,scopes:null===(s=t.options)||void 0===s?void 0:s.scopes,queryParams:null===(n=t.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:null===(r=t.options)||void 0===r?void 0:r.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,(async()=>this._exchangeCodeForSession(t)))}async _exchangeCodeForSession(t){const e=await Hh(this.storage,`${this.storageKey}-code-verifier`),[s,n]=(null!=e?e:"").split("/");try{const{data:e,error:r}=await ru(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:s},xform:iu});if(await Gh(this.storage,`${this.storageKey}-code-verifier`),r)throw r;return e&&e.session&&e.user?(e.session&&(await this._saveSession(e.session),await this._notifyAllSubscribers("SIGNED_IN",e.session)),{data:Object.assign(Object.assign({},e),{redirectType:null!=n?n:null}),error:r}):{data:{user:null,session:null,redirectType:null},error:new Sh}}catch(r){if(wh(r))return{data:{user:null,session:null,redirectType:null},error:r};throw r}}async signInWithIdToken(t){try{const{options:e,provider:s,token:n,access_token:r,nonce:i}=t,o=await ru(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:n,access_token:r,nonce:i,gotrue_meta_security:{captcha_token:null==e?void 0:e.captchaToken}},xform:iu}),{data:a,error:l}=o;return l?{data:{user:null,session:null},error:l}:a&&a.session&&a.user?(a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:l}):{data:{user:null,session:null},error:new Sh}}catch(e){if(wh(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(t){var e,s,n,r,i;try{if("email"in t){const{email:n,options:r}=t;let i=null,o=null;"pkce"===this.flowType&&([i,o]=await Zh(this.storage,this.storageKey));const{error:a}=await ru(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:n,data:null!==(e=null==r?void 0:r.data)&&void 0!==e?e:{},create_user:null===(s=null==r?void 0:r.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},code_challenge:i,code_challenge_method:o},redirectTo:null==r?void 0:r.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in t){const{phone:e,options:s}=t,{data:o,error:a}=await ru(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:e,data:null!==(n=null==s?void 0:s.data)&&void 0!==n?n:{},create_user:null===(r=null==s?void 0:s.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!==(i=null==s?void 0:s.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new Ph("You must provide either an email or phone number.")}catch(o){if(wh(o))return{data:{user:null,session:null},error:o};throw o}}async verifyOtp(t){var e,s;try{let n,r;"options"in t&&(n=null===(e=t.options)||void 0===e?void 0:e.redirectTo,r=null===(s=t.options)||void 0===s?void 0:s.captchaToken);const{data:i,error:o}=await ru(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:r}}),redirectTo:n,xform:iu});if(o)throw o;if(!i)throw new Error("An error occurred on token verification.");const a=i.session,l=i.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==t.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(n){if(wh(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithSSO(t){var e,s,n;try{let r=null,i=null;return"pkce"===this.flowType&&([r,i]=await Zh(this.storage,this.storageKey)),await ru(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:null!==(s=null===(e=t.options)||void 0===e?void 0:e.redirectTo)&&void 0!==s?s:void 0}),(null===(n=null==t?void 0:t.options)||void 0===n?void 0:n.captchaToken)?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:r,code_challenge_method:i}),headers:this.headers,xform:lu})}catch(r){if(wh(r))return{data:null,error:r};throw r}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._reauthenticate()))}async _reauthenticate(){try{return await this._useSession((async t=>{const{data:{session:e},error:s}=t;if(s)throw s;if(!e)throw new Th;const{error:n}=await ru(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:e.access_token});return{data:{user:null,session:null},error:n}}))}catch(t){if(wh(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const e=`${this.url}/resend`;if("email"in t){const{email:s,type:n,options:r}=t,{error:i}=await ru(this.fetch,"POST",e,{headers:this.headers,body:{email:s,type:n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}},redirectTo:null==r?void 0:r.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in t){const{phone:s,type:n,options:r}=t,{data:i,error:o}=await ru(this.fetch,"POST",e,{headers:this.headers,body:{phone:s,type:n,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:o}}throw new Ph("You must provide either an email or phone number and a type")}catch(e){if(wh(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,(async()=>this._useSession((async t=>t))))}async _acquireLock(t,e){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const t=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await t,await e()))();return this.pendingInLock.push((async()=>{try{await s}catch(t){}})()),s}return await this.lock(`lock:${this.storageKey}`,t,(async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const t=e();for(this.pendingInLock.push((async()=>{try{await t}catch(e){}})()),await t;this.pendingInLock.length;){const t=[...this.pendingInLock];await Promise.all(t),this.pendingInLock.splice(0,t.length)}return await t}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const e=await this.__loadSession();return await t(e)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let t=null;const e=await Hh(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",e),null!==e&&(this._isValidSession(e)?t=e:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const s=!!t.expires_at&&1e3*t.expires_at-Date.now()<fh;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",t.expires_at),!s){if(this.storage.isServer){let e=this.suppressGetSessionWarning;t=new Proxy(t,{get:(t,s,n)=>(e||"user"!==s||(e=!0,this.suppressGetSessionWarning=!0),Reflect.get(t,s,n))})}return{data:{session:t},error:null}}const{session:n,error:r}=await this._callRefreshToken(t.refresh_token);return r?{data:{session:null},error:r}:{data:{session:n},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){if(t)return await this._getUser(t);await this.initializePromise;return await this._acquireLock(-1,(async()=>await this._getUser()))}async _getUser(t){try{return t?await ru(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:au}):await this._useSession((async t=>{var e,s,n;const{data:r,error:i}=t;if(i)throw i;return(null===(e=r.session)||void 0===e?void 0:e.access_token)||this.hasCustomAuthorizationHeader?await ru(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(n=null===(s=r.session)||void 0===s?void 0:s.access_token)&&void 0!==n?n:void 0,xform:au}):{data:{user:null},error:new Th}}))}catch(e){if(wh(e))return function(t){return wh(t)&&"AuthSessionMissingError"===t.name}(e)&&(await this._removeSession(),await Gh(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(t,e={}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._updateUser(t,e)))}async _updateUser(t,e={}){try{return await this._useSession((async s=>{const{data:n,error:r}=s;if(r)throw r;if(!n.session)throw new Th;const i=n.session;let o=null,a=null;"pkce"===this.flowType&&null!=t.email&&([o,a]=await Zh(this.storage,this.storageKey));const{data:l,error:c}=await ru(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==e?void 0:e.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:o,code_challenge_method:a}),jwt:i.access_token,xform:au});if(c)throw c;return i.user=l.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}}))}catch(s){if(wh(s))return{data:{user:null},error:s};throw s}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._setSession(t)))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Th;const e=Date.now()/1e3;let s=e,n=!0,r=null;const{payload:i}=Jh(t.access_token);if(i.exp&&(s=i.exp,n=s<=e),n){const{session:e,error:s}=await this._callRefreshToken(t.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!e)return{data:{user:null,session:null},error:null};r=e}else{const{data:n,error:i}=await this._getUser(t.access_token);if(i)throw i;r={access_token:t.access_token,refresh_token:t.refresh_token,user:n.user,token_type:"bearer",expires_in:s-e,expires_at:s},await this._saveSession(r),await this._notifyAllSubscribers("SIGNED_IN",r)}return{data:{user:r.user,session:r},error:null}}catch(e){if(wh(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._refreshSession(t)))}async _refreshSession(t){try{return await this._useSession((async e=>{var s;if(!t){const{data:n,error:r}=e;if(r)throw r;t=null!==(s=n.session)&&void 0!==s?s:void 0}if(!(null==t?void 0:t.refresh_token))throw new Th;const{session:n,error:r}=await this._callRefreshToken(t.refresh_token);return r?{data:{user:null,session:null},error:r}:n?{data:{user:n.user,session:n},error:null}:{data:{user:null,session:null},error:null}}))}catch(e){if(wh(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(t,e){try{if(!Bh())throw new jh("No browser detected.");if(t.error||t.error_description||t.error_code)throw new jh(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(e){case"implicit":if("pkce"===this.flowType)throw new Eh("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new jh("Not a valid implicit grant flow url.")}if("pkce"===e){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new Eh("No code detected.");const{data:e,error:s}=await this._exchangeCodeForSession(t.code);if(s)throw s;const n=new URL(window.location.href);return n.searchParams.delete("code"),window.history.replaceState(window.history.state,"",n.toString()),{data:{session:e.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:n,access_token:r,refresh_token:i,expires_in:o,expires_at:a,token_type:l}=t;if(!(r&&o&&i&&l))throw new jh("No session defined in URL");const c=Math.round(Date.now()/1e3),h=parseInt(o);let u=c+h;a&&(u=parseInt(a));const{data:d,error:p}=await this._getUser(r);if(p)throw p;const f={provider_token:s,provider_refresh_token:n,access_token:r,expires_in:h,expires_at:u,refresh_token:i,token_type:l,user:d.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:f,redirectType:t.type},error:null}}catch(s){if(wh(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(t){return Boolean(t.access_token||t.error_description)}async _isPKCECallback(t){const e=await Hh(this.storage,`${this.storageKey}-code-verifier`);return!(!t.code||!e)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._signOut(t)))}async _signOut({scope:t}={scope:"global"}){return await this._useSession((async e=>{var s;const{data:n,error:r}=e;if(r)return{error:r};const i=null===(s=n.session)||void 0===s?void 0:s.access_token;if(i){const{error:e}=await this.admin.signOut(i,t);if(e&&(!function(t){return wh(t)&&"AuthApiError"===t.name}(e)||404!==e.status&&401!==e.status&&403!==e.status))return{error:e}}return"others"!==t&&(await this._removeSession(),await Gh(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))}onAuthStateChange(t){const e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(t){const e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})),s={id:e,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",e),this.stateChangeEmitters.delete(e)}};return this._debug("#onAuthStateChange()","registered callback with id",e),this.stateChangeEmitters.set(e,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,(async()=>{this._emitInitialSession(e)}))})(),{data:{subscription:s}}}async _emitInitialSession(t){return await this._useSession((async e=>{var s,n;try{const{data:{session:n},error:r}=e;if(r)throw r;await(null===(s=this.stateChangeEmitters.get(t))||void 0===s?void 0:s.callback("INITIAL_SESSION",n)),this._debug("INITIAL_SESSION","callback id",t,"session",n)}catch(r){await(null===(n=this.stateChangeEmitters.get(t))||void 0===n?void 0:n.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",r)}}))}async resetPasswordForEmail(t,e={}){let s=null,n=null;"pkce"===this.flowType&&([s,n]=await Zh(this.storage,this.storageKey,!0));try{return await ru(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:s,code_challenge_method:n,gotrue_meta_security:{captcha_token:e.captchaToken}},headers:this.headers,redirectTo:e.redirectTo})}catch(r){if(wh(r))return{data:null,error:r};throw r}}async getUserIdentities(){var t;try{const{data:e,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!==(t=e.user.identities)&&void 0!==t?t:[]},error:null}}catch(e){if(wh(e))return{data:null,error:e};throw e}}async linkIdentity(t){var e;try{const{data:s,error:n}=await this._useSession((async e=>{var s,n,r,i,o;const{data:a,error:l}=e;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:null===(s=t.options)||void 0===s?void 0:s.redirectTo,scopes:null===(n=t.options)||void 0===n?void 0:n.scopes,queryParams:null===(r=t.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:!0});return await ru(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(o=null===(i=a.session)||void 0===i?void 0:i.access_token)&&void 0!==o?o:void 0})}));if(n)throw n;return Bh()&&!(null===(e=t.options)||void 0===e?void 0:e.skipBrowserRedirect)&&window.location.assign(null==s?void 0:s.url),{data:{provider:t.provider,url:null==s?void 0:s.url},error:null}}catch(s){if(wh(s))return{data:{provider:t.provider,url:null},error:s};throw s}}async unlinkIdentity(t){try{return await this._useSession((async e=>{var s,n;const{data:r,error:i}=e;if(i)throw i;return await ru(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:null!==(n=null===(s=r.session)||void 0===s?void 0:s.access_token)&&void 0!==n?n:void 0})}))}catch(e){if(wh(e))return{data:null,error:e};throw e}}async _refreshAccessToken(t){const e=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(e,"begin");try{const r=Date.now();return await(s=async s=>(s>0&&await async function(t){return await new Promise((e=>{setTimeout((()=>e(null)),t)}))}(200*Math.pow(2,s-1)),this._debug(e,"refreshing attempt",s),await ru(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:iu})),n=(t,e)=>{const s=200*Math.pow(2,t);return e&&Ch(e)&&Date.now()+s-r<ph},new Promise(((t,e)=>{(async()=>{for(let i=0;i<1/0;i++)try{const e=await s(i);if(!n(i,null,e))return void t(e)}catch(r){if(!n(i,r))return void e(r)}})()})))}catch(r){if(this._debug(e,"error",r),wh(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(e,"end")}var s,n}_isValidSession(t){return"object"==typeof t&&null!==t&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,e){const s=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:e.redirectTo,scopes:e.scopes,queryParams:e.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",e,"url",s),Bh()&&!e.skipBrowserRedirect&&window.location.assign(s),{data:{provider:t,url:s},error:null}}async _recoverAndRefresh(){var t;const e="#_recoverAndRefresh()";this._debug(e,"begin");try{const s=await Hh(this.storage,this.storageKey);if(this._debug(e,"session from storage",s),!this._isValidSession(s))return this._debug(e,"session is not valid"),void(null!==s&&await this._removeSession());const n=1e3*(null!==(t=s.expires_at)&&void 0!==t?t:1/0)-Date.now()<fh;if(this._debug(e,`session has${n?"":" not"} expired with margin of 90000s`),n){if(this.autoRefreshToken&&s.refresh_token){const{error:t}=await this._callRefreshToken(s.refresh_token);t&&(Ch(t)||(this._debug(e,"refresh failed with a non-retryable error, removing the session",t),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){return void this._debug(e,"error",s)}finally{this._debug(e,"end")}}async _callRefreshToken(t){var e,s;if(!t)throw new Th;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const n=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{this.refreshingDeferred=new Kh;const{data:e,error:s}=await this._refreshAccessToken(t);if(s)throw s;if(!e.session)throw new Th;await this._saveSession(e.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",e.session);const n={session:e.session,error:null};return this.refreshingDeferred.resolve(n),n}catch(r){if(this._debug(n,"error",r),wh(r)){const t={session:null,error:r};return Ch(r)||await this._removeSession(),null===(e=this.refreshingDeferred)||void 0===e||e.resolve(t),t}throw null===(s=this.refreshingDeferred)||void 0===s||s.reject(r),r}finally{this.refreshingDeferred=null,this._debug(n,"end")}}async _notifyAllSubscribers(t,e,s=!0){const n=`#_notifyAllSubscribers(${t})`;this._debug(n,"begin",e,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:t,session:e});const n=[],r=Array.from(this.stateChangeEmitters.values()).map((async s=>{try{await s.callback(t,e)}catch(r){n.push(r)}}));if(await Promise.all(r),n.length>0){for(let t=0;t<n.length;t+=1);throw n[0]}}finally{this._debug(n,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await qh(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Gh(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&Bh()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(e){}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval((()=>this._autoRefreshTokenTick()),ph);this.autoRefreshTicker=t,t&&"object"==typeof t&&"function"==typeof t.unref?t.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(t),setTimeout((async()=>{await this.initializePromise,await this._autoRefreshTokenTick()}),0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,(async()=>{try{const e=Date.now();try{return await this._useSession((async t=>{const{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const n=Math.floor((1e3*s.expires_at-e)/ph);this._debug("#_autoRefreshTokenTick()",`access token expires in ${n} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),n<=3&&await this._callRefreshToken(s.refresh_token)}))}catch(t){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(t){if(!(t.isAcquireTimeout||t instanceof gu))throw t;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Bh()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){}}async _onVisibilityChanged(t){const e=`#_onVisibilityChanged(${t})`;this._debug(e,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,(async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(e,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,e,s){const n=[`provider=${encodeURIComponent(e)}`];if((null==s?void 0:s.redirectTo)&&n.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&n.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){const[t,e]=await Zh(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(t)}`,code_challenge_method:`${encodeURIComponent(e)}`});n.push(s.toString())}if(null==s?void 0:s.queryParams){const t=new URLSearchParams(s.queryParams);n.push(t.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&n.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${t}?${n.join("&")}`}async _unenroll(t){try{return await this._useSession((async e=>{var s;const{data:n,error:r}=e;return r?{data:null,error:r}:await ru(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:null===(s=null==n?void 0:n.session)||void 0===s?void 0:s.access_token})}))}catch(e){if(wh(e))return{data:null,error:e};throw e}}async _enroll(t){try{return await this._useSession((async e=>{var s,n;const{data:r,error:i}=e;if(i)return{data:null,error:i};const o=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},"phone"===t.factorType?{phone:t.phone}:{issuer:t.issuer}),{data:a,error:l}=await ru(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token});return l?{data:null,error:l}:("totp"===t.factorType&&(null===(n=null==a?void 0:a.totp)||void 0===n?void 0:n.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})}))}catch(e){if(wh(e))return{data:null,error:e};throw e}}async _verify(t){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async e=>{var s;const{data:n,error:r}=e;if(r)return{data:null,error:r};const{data:i,error:o}=await ru(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:null===(s=null==n?void 0:n.session)||void 0===s?void 0:s.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:o})}))}catch(e){if(wh(e))return{data:null,error:e};throw e}}))}async _challenge(t){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async e=>{var s;const{data:n,error:r}=e;return r?{data:null,error:r}:await ru(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:null===(s=null==n?void 0:n.session)||void 0===s?void 0:s.access_token})}))}catch(e){if(wh(e))return{data:null,error:e};throw e}}))}async _challengeAndVerify(t){const{data:e,error:s}=await this._challenge({factorId:t.factorId});return s?{data:null,error:s}:await this._verify({factorId:t.factorId,challengeId:e.id,code:t.code})}async _listFactors(){const{data:{user:t},error:e}=await this.getUser();if(e)return{data:null,error:e};const s=(null==t?void 0:t.factors)||[],n=s.filter((t=>"totp"===t.factor_type&&"verified"===t.status)),r=s.filter((t=>"phone"===t.factor_type&&"verified"===t.status));return{data:{all:s,totp:n,phone:r},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,(async()=>await this._useSession((async t=>{var e,s;const{data:{session:n},error:r}=t;if(r)return{data:null,error:r};if(!n)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:i}=Jh(n.access_token);let o=null;i.aal&&(o=i.aal);let a=o;(null!==(s=null===(e=n.user.factors)||void 0===e?void 0:e.filter((t=>"verified"===t.status)))&&void 0!==s?s:[]).length>0&&(a="aal2");return{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:i.amr||[]},error:null}}))))}async fetchJwk(t,e={keys:[]}){let s=e.keys.find((e=>e.kid===t));if(s)return s;if(s=this.jwks.keys.find((e=>e.kid===t)),s&&this.jwks_cached_at+6e5>Date.now())return s;const{data:n,error:r}=await ru(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(r)throw r;if(!n.keys||0===n.keys.length)throw new Oh("JWKS is empty");if(this.jwks=n,this.jwks_cached_at=Date.now(),s=n.keys.find((e=>e.kid===t)),!s)throw new Oh("No matching signing key found in JWKS");return s}async getClaims(t,e={keys:[]}){try{let s=t;if(!s){const{data:t,error:e}=await this.getSession();if(e||!t.session)return{data:null,error:e};s=t.session.access_token}const{header:n,payload:r,signature:i,raw:{header:o,payload:a}}=Jh(s);if(function(t){if(!t)throw new Error("Missing exp claim");if(t<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(r.exp),!n.kid||"HS256"===n.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:t}=await this.getUser(s);if(t)throw t;return{data:{claims:r,header:n,signature:i},error:null}}const l=function(t){switch(t){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(n.alg),c=await this.fetchJwk(n.kid,e),h=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,h,i,Uh(`${o}.${a}`))))throw new Oh("Invalid JWT signature");return{data:{claims:r,header:n,signature:i},error:null}}catch(s){if(wh(s))return{data:null,error:s};throw s}}}xu.nextInstanceID=0;const ku=xu;class _u extends ku{constructor(t){super(t)}}var Tu=function(t,e,s,n){return new(s||(s=Promise))((function(r,i){function o(t){try{l(n.next(t))}catch(e){i(e)}}function a(t){try{l(n.throw(t))}catch(e){i(e)}}function l(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}l((n=n.apply(t,e||[])).next())}))};class Su{constructor(t,e,s){var n,r,i;if(this.supabaseUrl=t,this.supabaseKey=e,!t)throw new Error("supabaseUrl is required.");if(!e)throw new Error("supabaseKey is required.");const o=(a=t).endsWith("/")?a:a+"/";var a;const l=new URL(o);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,h=function(t,e){var s,n;const{db:r,auth:i,realtime:o,global:a}=t,{db:l,auth:c,realtime:h,global:u}=e,d={db:Object.assign(Object.assign({},l),r),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},h),o),global:Object.assign(Object.assign(Object.assign({},u),a),{headers:Object.assign(Object.assign({},null!==(s=null==u?void 0:u.headers)&&void 0!==s?s:{}),null!==(n=null==a?void 0:a.headers)&&void 0!==n?n:{})}),accessToken:()=>uh(this,void 0,void 0,(function*(){return""}))};return t.accessToken?d.accessToken=t.accessToken:delete d.accessToken,d}(null!=s?s:{},{db:ih,realtime:ah,auth:Object.assign(Object.assign({},oh),{storageKey:c}),global:rh});this.storageKey=null!==(n=h.auth.storageKey)&&void 0!==n?n:"",this.headers=null!==(r=h.global.headers)&&void 0!==r?r:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(t,e)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(e)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(i=h.auth)&&void 0!==i?i:{},this.headers,h.global.fetch),this.fetch=hh(e,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new Ql(new URL("rest/v1",l).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new hl(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new sh(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,e={},s={}){return this.rest.rpc(t,e,s)}channel(t,e={config:{}}){return this.realtime.channel(t,e)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,e;return Tu(this,void 0,void 0,(function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return null!==(e=null===(t=s.session)||void 0===t?void 0:t.access_token)&&void 0!==e?e:null}))}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:e,detectSessionInUrl:s,storage:n,storageKey:r,flowType:i,lock:o,debug:a},l,c){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new _u({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),l),storageKey:r,autoRefreshToken:t,persistSession:e,detectSessionInUrl:s,storage:n,flowType:i,lock:o,debug:a,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Mc(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},null==t?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange(((t,e)=>{this._handleTokenChanged(t,"CLIENT",null==e?void 0:e.access_token)}))}_handleTokenChanged(t,e,s){"TOKEN_REFRESHED"!==t&&"SIGNED_IN"!==t||this.changedAccessToken===s?"SIGNED_OUT"===t&&(this.realtime.setAuth(),"STORAGE"==e&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=s}}const Pu="https://wikngnwwakatokbgvenw.supabase.co",ju="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4";const Eu=function(){try{const{url:t,key:e}=function(){try{new URL(Pu)}catch{throw new Error("Invalid Supabase URL format. Please ensure it's a valid URL (e.g., https://project.supabase.co)")}return{url:Pu,key:ju}}();return((t,e,s)=>new Su(t,e,s))(t,e,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},realtime:{params:{eventsPerSecond:10}}})}catch(t){return{functions:{invoke:async()=>({data:null,error:new Error("Supabase not configured - using mock client")})},auth:{getSession:async()=>({data:{session:null},error:null}),onAuthStateChange:()=>({data:{subscription:{unsubscribe:()=>{}}}}),signInWithPassword:async()=>({data:null,error:new Error("Authentication not available")}),signOut:async()=>({error:null})},from:()=>({select:()=>({data:[],error:null}),insert:()=>({data:null,error:new Error("Database not available")}),update:()=>({data:null,error:new Error("Database not available")}),delete:()=>({data:null,error:new Error("Database not available")})})}}}(),Au=t=>{const e=Mu(t),{conflictingClassGroups:s,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:t=>{const s=t.split("-");return""===s[0]&&1!==s.length&&s.shift(),Cu(s,e)||Ou(t)},getConflictingClassGroupIds:(t,e)=>{const r=s[t]||[];return e&&n[t]?[...r,...n[t]]:r}}},Cu=(t,e)=>{if(0===t.length)return e.classGroupId;const s=t[0],n=e.nextPart.get(s),r=n?Cu(t.slice(1),n):void 0;if(r)return r;if(0===e.validators.length)return;const i=t.join("-");return e.validators.find((({validator:t})=>t(i)))?.classGroupId},Ru=/^\[(.+)\]$/,Ou=t=>{if(Ru.test(t)){const e=Ru.exec(t)[1],s=e?.substring(0,e.indexOf(":"));if(s)return"arbitrary.."+s}},Mu=t=>{const{theme:e,prefix:s}=t,n={nextPart:new Map,validators:[]};return $u(Object.entries(t.classGroups),s).forEach((([t,s])=>{Lu(s,n,t,e)})),n},Lu=(t,e,s,n)=>{t.forEach((t=>{if("string"!=typeof t){if("function"==typeof t)return Iu(t)?void Lu(t(n),e,s,n):void e.validators.push({validator:t,classGroupId:s});Object.entries(t).forEach((([t,r])=>{Lu(r,Du(e,t),s,n)}))}else{(""===t?e:Du(e,t)).classGroupId=s}}))},Du=(t,e)=>{let s=t;return e.split("-").forEach((t=>{s.nextPart.has(t)||s.nextPart.set(t,{nextPart:new Map,validators:[]}),s=s.nextPart.get(t)})),s},Iu=t=>t.isThemeGetter,$u=(t,e)=>e?t.map((([t,s])=>[t,s.map((t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map((([t,s])=>[e+t,s]))):t))])):t,Nu=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,s=new Map,n=new Map;const r=(r,i)=>{s.set(r,i),e++,e>t&&(e=0,n=s,s=new Map)};return{get(t){let e=s.get(t);return void 0!==e?e:void 0!==(e=n.get(t))?(r(t,e),e):void 0},set(t,e){s.has(t)?s.set(t,e):r(t,e)}}},Vu=t=>{const{separator:e,experimentalParseClassName:s}=t,n=1===e.length,r=e[0],i=e.length,o=t=>{const s=[];let o,a=0,l=0;for(let u=0;u<t.length;u++){let c=t[u];if(0===a){if(c===r&&(n||t.slice(u,u+i)===e)){s.push(t.slice(l,u)),l=u+i;continue}if("/"===c){o=u;continue}}"["===c?a++:"]"===c&&a--}const c=0===s.length?t:t.substring(l),h=c.startsWith("!");return{modifiers:s,hasImportantModifier:h,baseClassName:h?c.substring(1):c,maybePostfixModifierPosition:o&&o>l?o-l:void 0}};return s?t=>s({className:t,parseClassName:o}):o},Uu=t=>{if(t.length<=1)return t;const e=[];let s=[];return t.forEach((t=>{"["===t[0]?(e.push(...s.sort(),t),s=[]):s.push(t)})),e.push(...s.sort()),e},Bu=/\s+/;function Fu(){let t,e,s=0,n="";for(;s<arguments.length;)(t=arguments[s++])&&(e=zu(t))&&(n&&(n+=" "),n+=e);return n}const zu=t=>{if("string"==typeof t)return t;let e,s="";for(let n=0;n<t.length;n++)t[n]&&(e=zu(t[n]))&&(s&&(s+=" "),s+=e);return s};function Wu(t,...e){let s,n,r,i=function(a){const l=e.reduce(((t,e)=>e(t)),t());return s=(t=>({cache:Nu(t.cacheSize),parseClassName:Vu(t),...Au(t)}))(l),n=s.cache.get,r=s.cache.set,i=o,o(a)};function o(t){const e=n(t);if(e)return e;const i=((t,e)=>{const{parseClassName:s,getClassGroupId:n,getConflictingClassGroupIds:r}=e,i=[],o=t.trim().split(Bu);let a="";for(let l=o.length-1;l>=0;l-=1){const t=o[l],{modifiers:e,hasImportantModifier:c,baseClassName:h,maybePostfixModifierPosition:u}=s(t);let d=Boolean(u),p=n(d?h.substring(0,u):h);if(!p){if(!d){a=t+(a.length>0?" "+a:a);continue}if(p=n(h),!p){a=t+(a.length>0?" "+a:a);continue}d=!1}const f=Uu(e).join(":"),m=c?f+"!":f,g=m+p;if(i.includes(g))continue;i.push(g);const y=r(p,d);for(let s=0;s<y.length;++s){const t=y[s];i.push(m+t)}a=t+(a.length>0?" "+a:a)}return a})(t,s);return r(t,i),i}return function(){return i(Fu.apply(null,arguments))}}const qu=t=>{const e=e=>e[t]||[];return e.isThemeGetter=!0,e},Hu=/^\[(?:([a-z-]+):)?(.+)\]$/i,Gu=/^\d+\/\d+$/,Ku=new Set(["px","full","screen"]),Ju=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Yu=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Xu=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Zu=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Qu=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,td=t=>sd(t)||Ku.has(t)||Gu.test(t),ed=t=>md(t,"length",gd),sd=t=>Boolean(t)&&!Number.isNaN(Number(t)),nd=t=>md(t,"number",sd),rd=t=>Boolean(t)&&Number.isInteger(Number(t)),id=t=>t.endsWith("%")&&sd(t.slice(0,-1)),od=t=>Hu.test(t),ad=t=>Ju.test(t),ld=new Set(["length","size","percentage"]),cd=t=>md(t,ld,yd),hd=t=>md(t,"position",yd),ud=new Set(["image","url"]),dd=t=>md(t,ud,bd),pd=t=>md(t,"",vd),fd=()=>!0,md=(t,e,s)=>{const n=Hu.exec(t);return!!n&&(n[1]?"string"==typeof e?n[1]===e:e.has(n[1]):s(n[2]))},gd=t=>Yu.test(t)&&!Xu.test(t),yd=()=>!1,vd=t=>Zu.test(t),bd=t=>Qu.test(t),wd=()=>{const t=qu("colors"),e=qu("spacing"),s=qu("blur"),n=qu("brightness"),r=qu("borderColor"),i=qu("borderRadius"),o=qu("borderSpacing"),a=qu("borderWidth"),l=qu("contrast"),c=qu("grayscale"),h=qu("hueRotate"),u=qu("invert"),d=qu("gap"),p=qu("gradientColorStops"),f=qu("gradientColorStopPositions"),m=qu("inset"),g=qu("margin"),y=qu("opacity"),v=qu("padding"),b=qu("saturate"),w=qu("scale"),x=qu("sepia"),k=qu("skew"),_=qu("space"),T=qu("translate"),S=()=>["auto",od,e],P=()=>[od,e],j=()=>["",td,ed],E=()=>["auto",sd,od],A=()=>["","0",od],C=()=>[sd,od];return{cacheSize:500,separator:":",theme:{colors:[fd],spacing:[td,ed],blur:["none","",ad,od],brightness:C(),borderColor:[t],borderRadius:["none","","full",ad,od],borderSpacing:P(),borderWidth:j(),contrast:C(),grayscale:A(),hueRotate:C(),invert:A(),gap:P(),gradientColorStops:[t],gradientColorStopPositions:[id,ed],inset:S(),margin:S(),opacity:C(),padding:P(),saturate:C(),scale:C(),sepia:A(),skew:C(),space:P(),translate:P()},classGroups:{aspect:[{aspect:["auto","square","video",od]}],container:["container"],columns:[{columns:[ad]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",od]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",rd,od]}],basis:[{basis:S()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",od]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",rd,od]}],"grid-cols":[{"grid-cols":[fd]}],"col-start-end":[{col:["auto",{span:["full",rd,od]},od]}],"col-start":[{"col-start":E()}],"col-end":[{"col-end":E()}],"grid-rows":[{"grid-rows":[fd]}],"row-start-end":[{row:["auto",{span:[rd,od]},od]}],"row-start":[{"row-start":E()}],"row-end":[{"row-end":E()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",od]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",od]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",od,e]}],"min-w":[{"min-w":[od,e,"min","max","fit"]}],"max-w":[{"max-w":[od,e,"none","full","min","max","fit","prose",{screen:[ad]},ad]}],h:[{h:[od,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[od,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[od,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[od,e,"auto","min","max","fit"]}],"font-size":[{text:["base",ad,ed]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",nd]}],"font-family":[{font:[fd]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",od]}],"line-clamp":[{"line-clamp":["none",sd,nd]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",td,od]}],"list-image":[{"list-image":["none",od]}],"list-style-type":[{list:["none","disc","decimal",od]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",td,ed]}],"underline-offset":[{"underline-offset":["auto",td,od]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",od]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",od]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",hd]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",cd]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},dd]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[td,od]}],"outline-w":[{outline:[td,ed]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:j()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[td,ed]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",ad,pd]}],"shadow-color":[{shadow:[fd]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[s]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",ad,od]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[u]}],saturate:[{saturate:[b]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[s]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",od]}],duration:[{duration:C()}],ease:[{ease:["linear","in","out","in-out",od]}],delay:[{delay:C()}],animate:[{animate:["none","spin","ping","pulse","bounce",od]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[rd,od]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",od]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",od]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",od]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[td,ed,nd]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},xd=Wu(wd);function kd(...t){return xd(r(t))}const _d=({children:s,className:n="",id:r,fullWidth:i=!1,background:o="default",animate:a=!1,delay:l=0,animationStyle:c="fade",onMouseEnter:h,onMouseLeave:u})=>{const d=t.useRef(null),[p,f]=t.useState(!1),m=t.useRef("undefined"!=typeof window&&window.matchMedia("(prefers-reduced-motion: reduce)").matches);return t.useEffect((()=>{if(!a||m.current)return void f(!0);const t=new IntersectionObserver((([e])=>{e.isIntersecting&&(setTimeout((()=>{f(!0)}),l),t.unobserve(e.target))}),{threshold:.1,rootMargin:"0px 0px -10% 0px"});return d.current&&t.observe(d.current),()=>{d.current&&t.unobserve(d.current)}}),[a,l]),t.useEffect((()=>{if("undefined"==typeof window)return;const t=window.matchMedia("(prefers-reduced-motion: reduce)"),e=t=>{m.current=t.matches,t.matches&&f(!0)};return t.addEventListener("change",e),()=>{t.removeEventListener("change",e)}}),[]),e.jsxs("section",{id:r,ref:d,className:kd("py-8 xs:py-10 sm:py-12 md:py-16 lg:py-20 relative overflow-hidden",n,a?"transform transition-all duration-700":"",(()=>{if(!a||m.current)return"";if(!p)switch(c){case"fade":return"opacity-0";case"slide-up":return"opacity-0 translate-y-10";case"slide-down":return"opacity-0 -translate-y-10";case"scale":return"opacity-0 scale-95";default:return""}return"opacity-100 translate-y-0 scale-100"})()),style:{transitionDelay:`${l}ms`},onMouseEnter:h,onMouseLeave:u,children:[e.jsx("div",{className:kd("relative z-10",i?"w-full":"container mx-auto px-3 xs:px-4 sm:px-5 md:px-6 lg:px-8"),children:s}),r&&e.jsx("div",{className:"absolute left-0 top-1/2 w-1 h-16 -translate-y-1/2 transition-all duration-500 \n                      "+(p?"bg-green-bright/50 shadow-[0_0_10px_rgba(0,255,140,0.6)]":"bg-transparent"),"aria-hidden":"true"})]})},Td=[{text:"NZ Owned & Operated",icon:i,description:"A New Zealand company understanding local SME needs"},{text:"Enterprise-Grade Protection",icon:o,description:"Top-tier security without enterprise complexity"},{text:"Rapid 2-4 Week Implementation",icon:a,description:"Quick security improvements with minimal disruption"},{text:"SME-Focused Solutions",icon:l,description:"Tailored cybersecurity for small and medium enterprises"}],Sd=()=>{const[e,s]=t.useState(null),[n,r]=t.useState(!0),[i,o]=t.useState(null),a=async()=>{try{r(!0),o(null);const{data:t,error:e}=await Eu.functions.invoke("cloudflare-radar-stats");if(e){if(e.message?.includes("Supabase not configured"))return void s({phishing:{total:125847,trend:12.5,topTargets:["Microsoft","Apple","Amazon","Google","PayPal"]},spoofing:{total:89234,trend:-3.2,topMethods:["Email Spoofing","Domain Spoofing","IP Spoofing"]},dmarc:{adoptionRate:67.8,compliance:45.2,trend:8.7},industryRisks:{"Financial Services":89.2,Healthcare:76.5,Technology:82.1,Government:91.7,Education:68.9},emailSecurity:{malicious:{detected:45892,clean:1234567,detectionRate:3.6},dkim:{pass:892456,fail:45123,none:23456,adoptionRate:92.8},spf:{pass:945678,fail:34567,none:19876,adoptionRate:94.2},spam:{detected:234567,clean:1098765,detectionRate:17.6}},lastUpdated:(new Date).toISOString(),dataSource:"fallback",error:"Supabase not configured - using demo data"});throw e}s(t),"cloudflare_radar_api"===t.dataSource?t.emailSecurity:t.error}catch(t){o("Failed to load threat intelligence data")}finally{r(!1)}};t.useEffect((()=>{a();const t=setInterval(a,18e5);return()=>clearInterval(t)}),[]);const l="cloudflare_radar_api"===e?.dataSource;return{radarData:e,loading:n,error:i,refetch:a,hasRealData:l,apiStatus:{isActive:l,dataSource:e?.dataSource||"unknown"}}},Pd=c.memo((({value:s,duration:n=2e3,suffix:r="",prefix:i="",decimals:o=0})=>{const[a,l]=t.useState(0),[c,h]=t.useState(!1);t.useEffect((()=>{if(isNaN(s)||s===a)return;let t,e;h(!0);const r=i=>{t||(t=i);const o=Math.min((i-t)/n,1),a=1-Math.pow(1-o,4);l(s*a),o<1?e=requestAnimationFrame(r):h(!1)};return e=requestAnimationFrame(r),()=>{e&&cancelAnimationFrame(e),h(!1)}}),[s,n]);return e.jsxs("span",{className:"font-mono",children:[i,(u=a,u>=1e6?(u/1e6).toFixed(o)+"M":u>=1e3?(u/1e3).toFixed(o)+"K":u.toFixed(o)),r]});var u}));c.memo((({icon:t,label:s,value:n,suffix:r="",trend:i,isLive:o=!1,delay:a=0})=>e.jsx(_a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:a},className:"relative group",children:e.jsxs("div",{className:"relative bg-black-soft/60 backdrop-blur-sm border border-green-muted/30 rounded-lg p-4 \n                      hover:border-green-muted/50 transition-all duration-300 group-hover:shadow-[0_0_20px_rgba(0,255,140,0.1)]",children:[o&&e.jsxs("div",{className:"absolute top-2 right-2 flex items-center gap-1",children:[e.jsx(_a.div,{className:"w-2 h-2 bg-green-bright rounded-full",animate:{opacity:[1,.3,1]},transition:{duration:2,repeat:1/0}}),e.jsx("span",{className:"text-xs text-green-bright font-mono",children:"LIVE"})]}),e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:"p-2 bg-green-dark/40 rounded-md",children:t}),void 0!==i&&e.jsxs("div",{className:"flex items-center gap-1 text-xs "+(i>=0?"text-red-400":"text-green-400"),children:[e.jsx(h,{className:"w-3 h-3 "+(i<0?"rotate-180":"")}),e.jsxs("span",{children:[Math.abs(i).toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-2",children:e.jsx("div",{className:"text-2xl font-bold text-white",children:e.jsx(Pd,{value:n,suffix:r,decimals:"%"===r?1:0})})}),e.jsx("div",{className:"text-sm text-white/70",children:s}),e.jsx("div",{className:"absolute inset-0 bg-green-bright/5 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"})]})})));const jd=({className:t="",delay:s=0})=>{const{radarData:n,loading:r,hasRealData:i}=Sd();return r?e.jsxs(_a.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:s},className:`bg-black-soft/40 backdrop-blur-sm border border-green-muted/30 rounded-lg p-4 ${t}`,children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[e.jsx(u,{className:"w-4 h-4 text-green-bright animate-pulse"}),e.jsx("span",{className:"text-sm text-green-bright font-mono",children:"Loading live data..."})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[...Array(2)].map(((t,s)=>e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-6 bg-green-muted/20 rounded w-3/4 mb-1"}),e.jsx("div",{className:"h-3 bg-green-muted/20 rounded w-full"})]},s)))})]}):n?e.jsxs(_a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.8,delay:s},className:`bg-black-soft/40 backdrop-blur-sm border border-green-muted/30 rounded-lg p-4 hover:border-green-muted/50 transition-all duration-300 hover:shadow-[0_0_20px_rgba(0,255,140,0.1)] ${t}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(u,{className:"w-4 h-4 text-green-bright"}),e.jsx("span",{className:"text-sm text-green-bright font-mono",children:i?"LIVE THREAT DATA":"THREAT PREVIEW"})]}),i&&e.jsx(_a.div,{className:"w-2 h-2 bg-green-bright rounded-full",animate:{opacity:[1,.3,1]},transition:{duration:2,repeat:1/0}})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-lg font-bold text-white font-mono",children:e.jsx(Pd,{value:n.phishing.total})}),e.jsx("div",{className:"text-xs text-white/70",children:"Global Phishing Attacks"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-lg font-bold text-white font-mono",children:e.jsx(Pd,{value:n.emailSecurity.malicious.detectionRate,suffix:"%",decimals:1})}),e.jsx("div",{className:"text-xs text-white/70",children:"Malicious Email Rate"})]})]}),e.jsx("div",{className:"text-xs text-white/50 text-center mt-3 pt-2 border-t border-green-muted/20",children:i?"Powered by Cloudflare Radar":"Demo data available"})]}):null},Ed=c.memo((({loaded:s,animDelays:n})=>{const[r,i]=t.useState(null),c={hidden:{opacity:0,x:-20},visible:{opacity:1,x:0,transition:{duration:.5}}};return e.jsxs(_a.div,{initial:"hidden",animate:s?"visible":"hidden",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},className:"space-y-3 xs:space-y-4 sm:space-y-5 md:space-y-6 order-2 md:order-1 md:pr-0 lg:pr-6 xl:pr-12",children:[e.jsx(_a.div,{variants:c,className:"relative",children:e.jsxs("span",{className:"font-mono uppercase tracking-wider text-[10px] xs:text-xs bg-green-dark/60 text-green-200 px-3 py-1.5 rounded-full inline-flex items-center gap-1.5 shadow-sm",children:[e.jsx(o,{className:"w-3 h-3 text-green-200"}),"SME Cybersecurity Specialists"]})}),e.jsx(_a.div,{variants:c,className:"relative",children:e.jsx(d,{to:"/services",className:"group block bg-gradient-to-r from-green-dark/30 to-green-dark/20 border border-green-muted/40 rounded-lg p-3 xs:p-4 hover:border-green-bright/60 transition-all duration-300",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"flex-shrink-0 w-10 h-10 bg-green-dark/50 rounded-lg flex items-center justify-center border border-green-muted/30",children:e.jsx(l,{className:"w-5 h-5 text-green-bright"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("span",{className:"text-[10px] xs:text-xs font-mono uppercase tracking-wider text-green-bright bg-green-dark/40 px-2 py-0.5 rounded",children:"Comprehensive Services"}),e.jsx("span",{className:"text-[10px] xs:text-xs text-white/60",children:"Available Now"})]}),e.jsx("h3",{className:"text-sm xs:text-base font-semibold text-white mb-1 group-hover:text-green-bright transition-colors",children:"Security Assessments, Audits & Emergency Response"}),e.jsx("p",{className:"text-xs xs:text-sm text-white/70 mb-2",children:"From vulnerability assessments to incident response - we've got you covered"}),e.jsxs("div",{className:"flex items-center gap-1 text-xs text-green-bright",children:[e.jsx("span",{children:"Explore our services"}),e.jsx(p,{className:"w-3 h-3 group-hover:translate-x-0.5 transition-transform"})]})]})]})})}),e.jsx(_a.div,{variants:c,children:e.jsxs("h1",{className:"text-3xl xs:text-4xl sm:text-5xl md:text-6xl font-bold leading-tight",children:[e.jsx("span",{className:"block mb-2",children:"SME Cybersecurity"}),e.jsxs("span",{className:"text-green-bright relative inline-block",children:["Done RIGHT",e.jsx("span",{className:"absolute -bottom-1 left-0 w-full h-[3px] bg-gradient-to-r from-green-bright to-transparent"})]})]})}),e.jsxs(_a.p,{variants:c,className:"text-base xs:text-lg sm:text-xl text-white/90 leading-relaxed max-w-xl",children:["Enterprise-grade cybersecurity designed specifically for SMEs. We deliver comprehensive security assessments, rapid incident response, and ongoing protection that ",e.jsx("strong",{className:"text-green-bright",children:"actually works"})," for your business size and budget."]}),e.jsx(_a.div,{variants:c,className:"flex flex-wrap gap-2 xs:gap-3 items-center",children:Td.slice(0,4).map(((t,s)=>{const n=t.icon,o=r===s;return e.jsxs("div",{className:"flex items-center gap-2 px-3 xs:px-4 py-2 xs:py-2.5 rounded-md text-xs xs:text-sm transition-all duration-300 "+(o?"bg-green-dark/50 shadow-[0_0_10px_rgba(0,255,140,0.2)]":"bg-green-dark/20"),onMouseEnter:()=>i(s),onMouseLeave:()=>i(null),children:[e.jsx(n,{className:`h-3.5 w-3.5 xs:h-4 xs:w-4 ${o?"text-green-bright":"text-green-bright/80"} transition-colors duration-300`}),e.jsx("span",{className:(o?"text-white":"text-white/80")+" transition-colors duration-300",children:t.text})]},s)}))}),e.jsxs(_a.div,{variants:c,className:"flex items-start xs:items-center gap-3 p-3 xs:p-4 bg-gradient-to-r from-blue-900/30 to-green-900/30 border border-green-500/30 rounded-md text-sm xs:text-base max-w-xl shadow-lg",children:[e.jsx(a,{className:"h-5 w-5 xs:h-6 xs:w-6 text-green-400 flex-shrink-0 mt-0.5 xs:mt-0"}),e.jsxs("p",{className:"text-white/90",children:[e.jsx("strong",{children:"Data-Driven Security:"})," We leverage real-time global threat intelligence to protect SMEs with enterprise-grade security. See live threat data below and get started in just 2-4 weeks."]})]}),e.jsx(_a.div,{variants:c,children:e.jsx(jd,{delay:parseFloat(n.warning)})}),e.jsxs(_a.div,{variants:c,className:"pt-4 xs:pt-6 flex flex-col xs:flex-row flex-wrap gap-4",children:[e.jsx(d,{to:"/phishing-assessment",className:"group relative overflow-hidden bg-gradient-to-r from-green-500 to-green-600 text-white font-bold rounded-md inline-flex items-center px-6 py-3 text-base shadow-lg hover:from-green-400 hover:to-green-500 transition-all duration-300",children:e.jsxs("span",{className:"relative z-10 flex items-center gap-2",children:[e.jsx(o,{className:"w-5 h-5"}),"Free Security Assessment"]})}),e.jsx(d,{to:"/services",className:"group relative overflow-hidden bg-green-bright text-black font-medium rounded-md inline-flex items-center px-5 py-3 text-base hover:bg-green-muted transition-colors",children:e.jsxs("span",{className:"relative z-10 flex items-center gap-2",children:[e.jsx(l,{className:"w-5 h-5"}),e.jsx("span",{children:"View All Services"})]})}),e.jsxs("a",{href:"https://calendly.com/blackveil",target:"_blank",rel:"noopener noreferrer",className:"group relative inline-flex items-center bg-transparent border border-green-400/50 text-green-200 hover:text-green-100 hover:border-green-300 px-5 py-3 rounded-md font-medium text-base transition-all duration-300",children:[e.jsx(f,{className:"w-5 h-5 mr-2"}),e.jsx("span",{children:"Book Consultation"}),e.jsx(m,{className:"w-5 h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1"})]})]})]})})),Ad={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.5,staggerChildren:.1}}},Cd={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}},Rd={initial:{scale:1,opacity:.8},animate:{scale:[1,1.05,1],opacity:[.8,1,.8],transition:{duration:3,repeat:1/0,ease:"easeInOut"}}},Od={initial:{y:-5,opacity:.3},animate:{y:100,opacity:[.3,.7,.3],transition:{duration:1.5,repeat:1/0,ease:"linear"}}},Md=()=>e.jsxs("div",{className:"absolute inset-0 pointer-events-none",children:[e.jsxs(_a.div,{className:"absolute bottom-0 left-0 right-0 p-3 xs:p-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:1.2,duration:.5},children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx("div",{className:"w-2 h-2 bg-green-bright rounded-full mr-2 animate-pulse"}),e.jsx("p",{className:"font-mono text-[10px] xs:text-xs text-white/80 tracking-wide",children:"REAL-TIME PROTECTION ACTIVE"})]}),e.jsx(_a.p,{className:"font-mono text-[10px] xs:text-xs text-green-bright/90 tracking-wide mb-2",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.4,duration:.5},children:"EMAIL SECURITY MONITORING"}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"text-[9px] xs:text-[10px] font-mono text-white/60",children:["STATUS: ",e.jsx("span",{className:"text-green-bright/90",children:"SECURE"})]}),e.jsx(_a.div,{className:"flex space-x-1.5",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.6,duration:.5},children:[1,2,3].map((t=>e.jsx(_a.div,{className:"w-5 h-0.5 bg-green-bright/50 rounded-sm",initial:{scaleX:0},animate:{scaleX:1},transition:{delay:1.6+.1*t,duration:.3}},t)))})]})]}),e.jsx("div",{className:"absolute inset-0 bg-cyber-grid opacity-10"})]}),Ld=c.memo((({loaded:t,animDelay:s})=>e.jsxs(_a.div,{initial:"hidden",animate:t?"visible":"hidden",variants:Ad,className:"relative mt-0 mb-0 order-1 md:order-2",style:{perspective:1e3},children:[e.jsxs(_a.div,{className:"relative rounded-lg overflow-hidden border border-green-muted/30 bg-black-soft/30 backdrop-blur-sm \n                   p-4 xs:p-6 md:p-8 aspect-[4/3]\n                   hover:border-green-muted/50 transition-all duration-500",variants:Cd,whileHover:{boxShadow:"0 0 30px rgba(0, 255, 140, 0.15)",translateY:-5,transition:{duration:.3}},children:[e.jsxs("div",{className:"relative flex flex-col items-center justify-center h-full",children:[e.jsx(_a.div,{className:"absolute inset-0 rounded-full bg-green-bright/10 blur-[80px] z-0",variants:Rd,initial:"initial",animate:"animate"}),e.jsxs(_a.div,{className:"relative z-10 mb-4",variants:Rd,initial:"initial",animate:"animate",children:[e.jsx(g,{className:"w-28 h-28 xs:w-36 xs:h-36 md:w-48 md:h-48 text-green-bright/90",strokeWidth:1.25,"aria-hidden":"true"}),e.jsx(_a.div,{className:"absolute -right-10 top-5 bg-black-soft/90 border border-green-muted/40 rounded-full p-2",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.8,duration:.5},children:e.jsx(y,{className:"w-5 h-5 text-green-bright/80"})}),e.jsx(_a.div,{className:"absolute -left-8 bottom-12 bg-black-soft/90 border border-green-muted/40 rounded-full p-2",initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},transition:{delay:1,duration:.5},children:e.jsx(v,{className:"w-4 h-4 text-green-bright/80"})}),e.jsx(_a.div,{className:"absolute -left-12 -top-2 opacity-30",initial:{opacity:0},animate:{opacity:.3},transition:{delay:1.2,duration:.8},children:e.jsxs("svg",{width:"24",height:"40",viewBox:"0 0 24 40",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12,0 C14,5 10,10 12,15 C14,20 10,25 12,30 C14,35 10,40 12,40",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none"}),e.jsx("path",{d:"M12,15 C14,12 16,15 18,12",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"}),e.jsx("path",{d:"M12,25 C14,22 16,25 18,22",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"}),e.jsx("path",{d:"M12,15 C10,12 8,15 6,12",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"}),e.jsx("path",{d:"M12,25 C10,22 8,25 6,22",stroke:"#00ff8c",strokeWidth:"0.3",fill:"none"})]})})]}),e.jsx(_a.div,{className:"absolute left-0 right-0 h-[2px] bg-green-bright/40 z-20",variants:Od,initial:"initial",animate:"animate"})]}),e.jsx(Md,{})]}),e.jsx("div",{className:"absolute -top-3 -left-3 w-12 h-12 border-t-2 border-l-2 border-green-bright/20 rounded-tl-xl"}),e.jsx("div",{className:"absolute -bottom-3 -right-3 w-12 h-12 border-b-2 border-r-2 border-green-bright/20 rounded-br-xl"}),e.jsxs("svg",{className:"absolute -z-10 top-0 left-0 w-full h-full opacity-20",viewBox:"0 0 200 150",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx(_a.path,{d:"M0,75 Q50,60 100,75 T200,75",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:.2},transition:{duration:2,delay:1.5}}),e.jsx(_a.path,{d:"M0,40 Q50,55 100,40 T200,40",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:.2},transition:{duration:2,delay:1.8}}),e.jsx(_a.path,{d:"M0,110 Q50,95 100,110 T200,110",stroke:"#00ff8c",strokeWidth:"0.5",fill:"none",initial:{pathLength:0,opacity:0},animate:{pathLength:1,opacity:.2},transition:{duration:2,delay:2.1}})]})]}))),Dd=()=>e.jsxs("div",{className:"absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90 transition-all duration-700",children:[e.jsxs("svg",{width:"90%",height:"90%",viewBox:"0 0 800 400",className:"mx-auto",style:{maxWidth:900,maxHeight:400},children:[e.jsx("line",{x1:"100",y1:"50",x2:"100",y2:"350",stroke:"#0f0",strokeWidth:"8"}),e.jsx("line",{x1:"700",y1:"50",x2:"700",y2:"350",stroke:"#0f0",strokeWidth:"8"}),[0,1,2,3,4].map((t=>e.jsxs("g",{children:[e.jsx("line",{x1:"100",y1:90+60*t,x2:"700",y2:90+60*t,stroke:"#0f0",strokeWidth:"5",className:"ladder-rung"}),e.jsx("rect",{x:250+80*t,y:70+60*t,width:"60",height:"40",rx:"10",fill:"#111",stroke:"#0f0",strokeWidth:"3",className:"gate-box"}),e.jsx(_a.rect,{x:250+80*t,y:70+60*t,width:"60",height:"40",rx:"10",fill:"red",initial:{opacity:0},animate:{opacity:[0,0,1,1,.5,.2]},transition:{delay:.5+.7*t,duration:2,repeat:0},style:{mixBlendMode:"screen"}}),e.jsx(_a.text,{x:280+80*t,y:95+60*t,textAnchor:"middle",fontSize:"32",fill:"red",fontWeight:"bold",initial:{opacity:0},animate:{opacity:[0,0,1,1,.5,.2]},transition:{delay:.7+.7*t,duration:1.5,repeat:0},children:"✗"})]},t))),e.jsx("text",{x:"400",y:"40",textAnchor:"middle",fontSize:"32",fill:"#0f0",fontWeight:"bold",children:"PLC Ladder Logic"})]}),e.jsx(_a.div,{className:"absolute inset-0 flex items-center justify-center pointer-events-none",initial:{opacity:0},animate:{opacity:[0,1,1,.8,1]},transition:{duration:2,repeat:1/0,repeatType:"reverse"},children:e.jsx("div",{className:"bg-black bg-opacity-80 rounded-xl px-8 py-6 border-4 border-red-600 shadow-2xl",children:e.jsx("div",{className:"text-2xl md:text-4xl font-mono text-red-500 tracking-widest text-center select-none",style:{textShadow:"0 0 16px #f00"},children:"53 79 73 74 65 6d 20 43 6f 6d 70 72 6f 6d 69 73 65 64"})})})]}),Id=({stuxnetCascadeActive:s=!1})=>{const[n,r]=t.useState(!1);t.useEffect((()=>{const t=setTimeout((()=>{r(!0)}),50);return()=>clearTimeout(t)}),[]);const i={badge:"0.1s",heading:"0.2s",divider:"0.25s",description:"0.3s",features:"0.4s",warning:"0.5s",cta:"0.6s",image:"0.3s"};return e.jsxs(_d,{className:"pt-10 md:pt-24 pb-10 md:pb-28 overflow-hidden relative",children:[s&&e.jsx(Dd,{}),!s&&e.jsxs("div",{className:"absolute inset-0 pointer-events-none",children:[e.jsx(_a.div,{className:"absolute top-0 right-0 w-3/4 h-3/4 bg-green-bright/5 rounded-full blur-[120px] -translate-y-1/2 translate-x-1/2",animate:{opacity:[.3,.5,.3],scale:[1,1.05,1]},transition:{duration:8,repeat:1/0,ease:"easeInOut"}}),e.jsx(_a.div,{className:"absolute bottom-0 left-0 w-1/2 h-1/2 bg-green-bright/3 rounded-full blur-[80px] translate-y-1/4 -translate-x-1/4",animate:{opacity:[.2,.4,.2],scale:[1,1.03,1]},transition:{duration:6,repeat:1/0,ease:"easeInOut",delay:1}}),e.jsx("div",{className:"absolute opacity-5 bottom-10 right-10 hidden md:block",children:e.jsxs("svg",{width:"200",height:"300",viewBox:"0 0 100 150",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M50,10 C60,30 40,40 50,50 C60,60 40,70 50,80 C60,90 40,100 50,110 C60,120 40,130 50,140",stroke:"currentColor",strokeWidth:"1",className:"text-green-bright"}),e.jsx("path",{d:"M50,50 C55,45 60,50 65,45 C70,40 75,45 80,40",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,80 C55,75 60,80 65,75 C70,70 75,75 80,70",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,110 C55,105 60,110 65,105 C70,100 75,105 80,100",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,50 C45,45 40,50 35,45 C30,40 25,45 20,40",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,80 C45,75 40,80 35,75 C30,70 25,75 20,70",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"}),e.jsx("path",{d:"M50,110 C45,105 40,110 35,105 C30,100 25,105 20,100",stroke:"currentColor",strokeWidth:"0.7",className:"text-green-bright"})]})})]}),e.jsxs(_a.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.8},className:"grid md:grid-cols-2 gap-8 md:gap-16 items-center",children:[e.jsx(Ed,{loaded:n,animDelays:i}),e.jsx(Ld,{loaded:n,animDelay:i.image})]})]})};export{O as A,Id as H,_d as S,nl as _,tl as a,Sd as b,kd as c,_a as m,Eu as s,Za as u};
