import{j as e,S as s,m as t,a1 as i}from"./vendor-DEz8SL3m.js";import{S as a}from"./core-ClMcqRvE.js";import{P as r,B as n,r as c}from"./index-DeIkHZyv.js";import"./ui-BD6Ux9Dl.js";const l=()=>e.jsxs("div",{children:[e.jsx(r,{title:"Cybersecurity Assessment Library | BlackVeil",description:"Take comprehensive cybersecurity assessments to identify vulnerabilities and improve your security posture. Free evaluations for phishing, ransomware, compliance and more.",canonicalUrl:"https://blackveil.co.nz/assessments",keywords:"cybersecurity assessment, security evaluation, phishing test, ransomware readiness, compliance check, security audit"}),e.jsx(n,{items:[{name:"Home",url:"https://blackveil.co.nz/"},{name:"Assessment Library",url:"https://blackveil.co.nz/assessments"}]}),e.jsx(a,{className:"pt-20 pb-12",children:e.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[e.jsxs("div",{className:"inline-flex items-center gap-2 bg-green-dark/30 px-4 py-2 rounded-full mb-6",children:[e.jsx(s,{className:"h-5 w-5 text-green-bright"}),e.jsx("span",{className:"text-green-bright font-medium",children:"Free Security Assessments"})]}),e.jsxs("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-6",children:["Know Your ",e.jsx("span",{className:"cyber-glow-text",children:"Security Risks"})]}),e.jsx("p",{className:"text-xl md:text-2xl text-white/80 mb-8 leading-relaxed",children:"Take comprehensive cybersecurity assessments to identify vulnerabilities, get personalized recommendations, and improve your organization's security posture."}),e.jsxs("div",{className:"grid md:grid-cols-3 gap-6 mb-12",children:[e.jsxs("div",{className:"cyber-card-highlight p-6",children:[e.jsx(t,{className:"h-8 w-8 text-green-bright mx-auto mb-3"}),e.jsx("h3",{className:"font-bold text-white mb-2",children:"Identify Risks"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Discover vulnerabilities and security gaps in your organization"})]}),e.jsxs("div",{className:"cyber-card-highlight p-6",children:[e.jsx(i,{className:"h-8 w-8 text-green-bright mx-auto mb-3"}),e.jsx("h3",{className:"font-bold text-white mb-2",children:"Get Recommendations"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Receive prioritized, actionable security improvement plans"})]}),e.jsxs("div",{className:"cyber-card-highlight p-6",children:[e.jsx(s,{className:"h-8 w-8 text-green-bright mx-auto mb-3"}),e.jsx("h3",{className:"font-bold text-white mb-2",children:"Improve Security"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Build stronger defenses with expert guidance and support"})]})]})]})}),e.jsx(a,{className:"pb-20",children:e.jsx("div",{className:"max-w-6xl mx-auto",children:e.jsx(c,{})})})]});export{l as default};
