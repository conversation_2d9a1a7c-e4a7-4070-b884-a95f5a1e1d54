
import { createClient } from '@supabase/supabase-js'
import type { Database } from './types'

// Fallback configuration for Lovable.dev deployments
// These values are embedded during build time when .env.local is available
const FALLBACK_SUPABASE_URL = 'https://wikngnwwakatokbgvenw.supabase.co'
const FALLBACK_SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4'

// Get Supabase configuration from environment variables with fallback
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || FALLBACK_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || FALLBACK_SUPABASE_ANON_KEY

// Enhanced environment detection for Lovable.dev compatibility
const isDevelopment = import.meta.env.DEV
const isProduction = import.meta.env.PROD
const isUsingFallback = !import.meta.env.VITE_SUPABASE_URL && !import.meta.env.VITE_SUPABASE_ANON_KEY
const enableGracefulDegradation = import.meta.env.VITE_ENABLE_GRACEFUL_DEGRADATION === 'true'

// Lovable.dev specific detection: DEV=true but no env vars = Lovable deployment
const isLovableDeployment = isDevelopment && isUsingFallback && typeof window !== 'undefined'
// True local development: DEV=true AND has env vars OR is server-side
const isTrueLocalDevelopment = isDevelopment && (!isUsingFallback || typeof window === 'undefined')

// Log configuration status for debugging
console.log('🔧 Supabase Configuration:', {
  hasEnvUrl: !!import.meta.env.VITE_SUPABASE_URL,
  hasEnvKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
  usingFallback: isUsingFallback,
  isDevelopment,
  isProduction,
  isLovableDeployment,
  isTrueLocalDevelopment,
  environment: isLovableDeployment ? 'lovable-deployment' : (isTrueLocalDevelopment ? 'local-development' : 'production'),
  finalUrl: supabaseUrl,
  finalKeyPresent: !!supabaseAnonKey
})

// Create a comprehensive error message for missing environment variables
function createEnvironmentErrorMessage(): string {
  const baseMessage = 'Missing Supabase environment variables.'

  if (isLovableDeployment) {
    return `${baseMessage}

🚨 LOVABLE.DEV DEPLOYMENT ISSUE:
This appears to be a Lovable.dev deployment where environment variables are not configured.
However, fallback configuration should have prevented this error.

DEBUGGING INFO:
- Environment: Lovable.dev deployment (DEV=true, no env vars)
- Fallback URL: ${FALLBACK_SUPABASE_URL ? 'Available' : 'Missing'}
- Fallback Key: ${FALLBACK_SUPABASE_ANON_KEY ? 'Available' : 'Missing'}

If you see this error, please report it as a configuration issue.`
  }

  if (isTrueLocalDevelopment) {
    return `${baseMessage}

For local development:
1. Copy .env.example to .env.local
2. Fill in your Supabase project credentials
3. Restart the development server

Required variables:
- VITE_SUPABASE_URL
- VITE_SUPABASE_ANON_KEY`
  }

  return `${baseMessage}

Please ensure the following environment variables are set:
- VITE_SUPABASE_URL: Your Supabase project URL
- VITE_SUPABASE_ANON_KEY: Your Supabase anonymous key

For deployment platforms, configure these as environment variables or secrets.`
}

// Validate environment variables with better error handling
function validateEnvironmentVariables(): { url: string; key: string } {
  // Enhanced debugging for all environments
  console.log('🔍 Validation Debug Info:', {
    supabaseUrl: supabaseUrl ? 'Set' : 'Missing',
    supabaseAnonKey: supabaseAnonKey ? 'Set' : 'Missing',
    fallbackUrl: FALLBACK_SUPABASE_URL ? 'Available' : 'Missing',
    fallbackKey: FALLBACK_SUPABASE_ANON_KEY ? 'Available' : 'Missing',
    isLovableDeployment,
    isTrueLocalDevelopment
  })

  // Log configuration status for debugging
  if (isLovableDeployment && isUsingFallback) {
    console.log('🔧 Using fallback Supabase configuration for Lovable.dev deployment')
  }

  // Only throw error if both URL and key are missing (which shouldn't happen with fallbacks)
  if (!supabaseUrl || !supabaseAnonKey) {
    const errorMessage = createEnvironmentErrorMessage()
    console.error('❌ Supabase Configuration Error:', errorMessage)
    console.error('❌ Debug: URL =', supabaseUrl, 'Key =', supabaseAnonKey ? 'Present' : 'Missing')
    throw new Error(errorMessage)
  }

  // Validate URL format
  try {
    new URL(supabaseUrl)
  } catch {
    throw new Error(
      'Invalid Supabase URL format. ' +
      'Please ensure it\'s a valid URL (e.g., https://project.supabase.co)'
    )
  }

  // Validate key format (basic check)
  if (supabaseAnonKey.length < 100) {
    throw new Error(
      'Invalid Supabase anon key format. ' +
      'Please ensure you\'re using the correct anon key from your Supabase project.'
    )
  }

  return { url: supabaseUrl, key: supabaseAnonKey }
}

// Initialize Supabase client with proper error handling and graceful degradation
function initializeSupabaseClient() {
  try {
    const { url, key } = validateEnvironmentVariables()

    return createClient<Database>(url, key, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    })
  } catch (error) {
    console.error('Failed to initialize Supabase client:', error)

    // If graceful degradation is enabled, return a mock client
    if (enableGracefulDegradation) {
      console.warn('🔄 Graceful degradation enabled: Using mock Supabase client')
      return createMockSupabaseClient()
    }

    throw error
  }
}

// Create a mock Supabase client for graceful degradation
function createMockSupabaseClient() {
  const mockClient = {
    functions: {
      invoke: async () => ({
        data: null,
        error: new Error('Supabase not configured - using mock client')
      })
    },
    auth: {
      getSession: async () => ({ data: { session: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: () => {} } } }),
      signInWithPassword: async () => ({ data: null, error: new Error('Authentication not available') }),
      signOut: async () => ({ error: null })
    },
    from: () => ({
      select: () => ({ data: [], error: null }),
      insert: () => ({ data: null, error: new Error('Database not available') }),
      update: () => ({ data: null, error: new Error('Database not available') }),
      delete: () => ({ data: null, error: new Error('Database not available') })
    })
  }

  return mockClient as any
}

export const supabase = initializeSupabaseClient()
