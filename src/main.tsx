
import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import App from './App';
import { AuthProvider } from './contexts/AuthContext';
import './index.css';
import { logSecurityHeaderRecommendations, applySecurityMetaTags } from './middleware/securityHeaders';
import { initPerformanceMonitoring } from './utils/performance/index';
import { initAnalytics, ensureCloudflareAnalytics, runAnalyticsTests } from './utils/analytics';
import { EnvironmentError } from './components/EnvironmentError';
import { ENV, hasRequiredEnvironmentVariables, createEnvironmentErrorProps, logEnvironmentStatus } from './config/environment';

// Initialize analytics
const initAnalyticsWithCheck = async () => {
  // Initialize analytics immediately
  initAnalytics();
  
  // Check if Cloudflare analytics is available
  const success = await ensureCloudflareAnalytics();
  
  if (!success) {
    console.info("ℹ️ Cloudflare analytics not detected. This is expected when:");
    console.info("   - Not running on blackveil.co.nz domain");
    console.info("   - Not proxied through Cloudflare");
    console.info("   - Web Analytics not enabled in Cloudflare dashboard");
  } else {
    console.info("✅ Cloudflare analytics detected successfully during initialization");
  }

  // Run comprehensive analytics tests in development
  if (import.meta.env.DEV) {
    setTimeout(() => {
      runAnalyticsTests().catch(console.error);
    }, 4000);
  }
};

// Apply security meta tags to help with CORS
applySecurityMetaTags();

// Initialize analytics
initAnalyticsWithCheck();

// Log security headers in development
if (import.meta.env.DEV) {
  logSecurityHeaderRecommendations();
}

// Error boundary component for catching Supabase initialization errors
class EnvironmentErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    // Check if this is a Supabase environment variable error
    if (error.message.includes('Missing Supabase environment variables') ||
        error.message.includes('VITE_SUPABASE_URL') ||
        error.message.includes('VITE_SUPABASE_ANON_KEY')) {
      return { hasError: true, error };
    }
    // For other errors, don't catch them here
    return null;
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Environment Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const errorProps = createEnvironmentErrorProps();
      return <EnvironmentError {...errorProps} />;
    }

    return this.props.children;
  }
}

// Initialize application with environment checks
function initializeApplication() {
  // Log environment status for debugging
  logEnvironmentStatus();

  // Get root element
  const root = document.getElementById("root");

  if (!root) {
    console.error("Root element not found");
    return;
  }

  // Enable React concurrent features for better performance
  const rootInstance = createRoot(root);

  // Check if we have required environment variables
  const hasEnvVars = hasRequiredEnvironmentVariables();
  const enableGracefulDegradation = import.meta.env.VITE_ENABLE_GRACEFUL_DEGRADATION === 'true';

  if (!hasEnvVars && ENV.isProduction && !enableGracefulDegradation) {
    // In production without environment variables and graceful degradation disabled, show the error page
    console.warn('⚠️ Missing environment variables in production deployment');
    const errorProps = createEnvironmentErrorProps();
    rootInstance.render(<EnvironmentError {...errorProps} />);
    return;
  }

  if (!hasEnvVars && enableGracefulDegradation) {
    console.warn('⚠️ Missing environment variables but graceful degradation enabled - continuing with demo data');
  }

  // Wrap the app with error boundary to catch Supabase initialization errors
  const AppComponent = (
    <EnvironmentErrorBoundary>
      {import.meta.env.DEV ? (
        <BrowserRouter>
          <AuthProvider>
            <App />
          </AuthProvider>
        </BrowserRouter>
      ) : (
        <React.StrictMode>
          <BrowserRouter>
            <AuthProvider>
              <App />
            </AuthProvider>
          </BrowserRouter>
        </React.StrictMode>
      )}
    </EnvironmentErrorBoundary>
  );

  rootInstance.render(AppComponent);

  // Initialize performance monitoring in production
  if (import.meta.env.PROD) {
    setTimeout(() => {
      initPerformanceMonitoring();
    }, 0);
  }
}

// Initialize the application
initializeApplication();
