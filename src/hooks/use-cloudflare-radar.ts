
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

interface RadarStats {
  phishing: {
    total: number;
    trend: number;
    topTargets: string[];
  };
  spoofing: {
    total: number;
    trend: number;
    topMethods: string[];
  };
  dmarc: {
    adoptionRate: number;
    compliance: number;
    trend: number;
  };
  industryRisks: Record<string, number>;
  // NEW: Email security metrics
  emailSecurity: {
    malicious: {
      detected: number;
      clean: number;
      detectionRate: number;
    };
    dkim: {
      pass: number;
      fail: number;
      none: number;
      adoptionRate: number;
    };
    spf: {
      pass: number;
      fail: number;
      none: number;
      adoptionRate: number;
    };
    spam: {
      detected: number;
      clean: number;
      detectionRate: number;
    };
  };
  lastUpdated: string;
  dataSource?: 'cloudflare_radar_api' | 'fallback_no_token' | 'fallback_api_error' | 'fallback_api_unavailable' | 'fallback_critical_error' | 'fallback';
  error?: string;
}

// Create fallback data when Supabase is not configured
const createFallbackData = (): RadarStats => ({
  phishing: {
    total: 125847,
    trend: 12.5,
    topTargets: ['Microsoft', 'Apple', 'Amazon', 'Google', 'PayPal']
  },
  spoofing: {
    total: 89234,
    trend: -3.2,
    topMethods: ['Email Spoofing', 'Domain Spoofing', 'IP Spoofing']
  },
  dmarc: {
    adoptionRate: 67.8,
    compliance: 45.2,
    trend: 8.7
  },
  industryRisks: {
    'Financial Services': 89.2,
    'Healthcare': 76.5,
    'Technology': 82.1,
    'Government': 91.7,
    'Education': 68.9
  },
  emailSecurity: {
    malicious: {
      detected: 45892,
      clean: 1234567,
      detectionRate: 3.6
    },
    dkim: {
      pass: 892456,
      fail: 45123,
      none: 23456,
      adoptionRate: 92.8
    },
    spf: {
      pass: 945678,
      fail: 34567,
      none: 19876,
      adoptionRate: 94.2
    },
    spam: {
      detected: 234567,
      clean: 1098765,
      detectionRate: 17.6
    }
  },
  lastUpdated: new Date().toISOString(),
  dataSource: 'fallback',
  error: 'Supabase not configured - using demo data'
});

export const useCloudflareRadar = () => {
  const [radarData, setRadarData] = useState<RadarStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRadarData = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: functionError } = await supabase.functions.invoke('cloudflare-radar-stats');

      if (functionError) {
        // Check if this is a configuration error
        if (functionError.message?.includes('Supabase not configured')) {
          console.warn('⚠️ Supabase not configured, using fallback data');
          setRadarData(createFallbackData());
          return;
        }
        throw functionError;
      }

      setRadarData(data);

      // Log API integration status
      if (data.dataSource === 'cloudflare_radar_api') {
        console.log('✅ Direct API Integration: Successfully loaded Cloudflare Radar data');

        // Log email security data availability
        if (data.emailSecurity) {
          console.log('📧 Email Security Data: Enhanced metrics loaded');
          console.log(`   - Malicious detection rate: ${data.emailSecurity.malicious.detectionRate}%`);
          console.log(`   - DKIM adoption rate: ${data.emailSecurity.dkim.adoptionRate}%`);
          console.log(`   - SPF adoption rate: ${data.emailSecurity.spf.adoptionRate}%`);
          console.log(`   - Spam detection rate: ${data.emailSecurity.spam.detectionRate}%`);
        }
      } else {
        console.log('⚠️ API Integration: Using fallback data source:', data.dataSource);
        if (data.error) {
          console.log('❌ Error details:', data.error);
        }
      }

    } catch (err) {
      console.error('Error fetching Cloudflare Radar data:', err);
      setError('Failed to load threat intelligence data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRadarData();

    // Refresh data every 30 minutes
    const interval = setInterval(fetchRadarData, 30 * 60 * 1000);

    return () => clearInterval(interval);
  }, []); // Empty dependency array to prevent infinite re-renders

  // Helper function to check if we have real API data
  const hasRealData = radarData?.dataSource === 'cloudflare_radar_api';

  return {
    radarData,
    loading,
    error,
    refetch: fetchRadarData,
    hasRealData,
    apiStatus: {
      isActive: hasRealData,
      dataSource: radarData?.dataSource || 'unknown'
    }
  };
};
