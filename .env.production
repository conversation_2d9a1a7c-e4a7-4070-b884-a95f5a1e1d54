# Production Environment Configuration for BlackVeil Security Platform
#
# This file contains production environment variable templates.
# For security, actual values should be set in the deployment platform's environment settings.
#
# IMPORTANT: This file can be committed to version control as it contains no actual secrets.
# The actual environment variables should be configured in your deployment platform:
# - Lovable.dev: Contact support for environment variable configuration
# - GitHub Pages: Set as repository secrets in GitHub Actions
# - Vercel/Netlify: Configure in project environment settings
# - Self-hosted: Set in your server environment

# Supabase Configuration (REQUIRED)
# These variables are required for the application to function
VITE_SUPABASE_URL=https://wikngnwwakatokbgvenw.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Indpa25nbnd3YWthdG9rYmd2ZW53Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1NjY0NTQsImV4cCI6MjA2NDE0MjQ1NH0.CCoK3cPN0Pe-AVUgePrxmWvf9qZho0b-prGDilfKmZ4

# Enable graceful degradation for missing environment variables
VITE_ENABLE_GRACEFUL_DEGRADATION=true

# Build Configuration
NODE_ENV=production

# Deployment Information
# These help with debugging deployment issues
VITE_DEPLOYMENT_PLATFORM=production
VITE_BUILD_TIMESTAMP=__BUILD_TIMESTAMP__
VITE_VERSION=__VERSION__

# Note: The VITE_ prefix is required for Vite to expose these variables to the client-side code
# Variables without the VITE_ prefix are only available during the build process
